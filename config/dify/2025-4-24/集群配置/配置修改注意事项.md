
# ------------------------------
# .env 配置相关
# ------------------------------

CONSOLE_API_URL=http://************
CONSOLE_WEB_URL=http://************
SERVICE_API_URL=http://************
APP_API_URL=http://************
APP_WEB_URL=http://************
FILES_URL=http://************

# The log level for the application.
# Supported values are `DEBUG`, `INFO`, `WARNING`, `ERROR`, `CRITICAL`
- LOG_LEVEL=INFO
# 日志路径
- LOG_FILE=/app/logs/dify.log
# 日志时间格式化
- LOG_DATEFORMAT=%Y-%m-%d %H:%M:%S
# 日志时区
- LOG_TZ=Asia/Shanghai


# Worker进程默认为10个
- SERVER_WORKER_CONNECTIONS=20
# it is recommended to set it to 360 to support a longer sse connection time.
- GUNICORN_TIMEOUT=580
# The number of Celery workers. The default is 1, and can be set as needed.
- CELERY_WORKER_AMOUNT=5


# ------------------------------
# PostgreSQL 配置相关
# The database uses PostgreSQL. Please use the public schema.
# It is consistent with the configuration in the 'db' service below.
# ------------------------------

DB_USERNAME=postgres
DB_PASSWORD=difyai123456
DB_HOST=************
DB_PORT=5432
DB_DATABASE=dify
# 最大可连接条数
SQLALCHEMY_POOL_SIZE=1000
# 连接池的生命周期，默认3600秒
SQLALCHEMY_POOL_RECYCLE=3600
# 打印日志，默认是false
SQLALCHEMY_ECHO=false

# Maximum number of connections to the database
# Default is 100
#
# Reference: https://www.postgresql.org/docs/current/runtime-config-connection.html#GUC-MAX-CONNECTIONS
POSTGRES_MAX_CONNECTIONS=1000

# 共享内存。
# 默认 128MB
# Recommended value: 25% of available memory
# Reference: https://www.postgresql.org/docs/current/runtime-config-resource.html#GUC-SHARED-BUFFERS
POSTGRES_SHARED_BUFFERS=256MB

# 每一条连接的内存.
# 默认 4MB
# Reference: https://www.postgresql.org/docs/current/runtime-config-resource.html#GUC-WORK-MEM
POSTGRES_WORK_MEM=32MB

# Sets the amount of memory reserved for maintenance activities.
# Default is 64MB
#
# Reference: https://www.postgresql.org/docs/current/runtime-config-resource.html#GUC-MAINTENANCE-WORK-MEM
POSTGRES_MAINTENANCE_WORK_MEM=256MB

# Sets the planner's assumption about the effective cache size.
# Default is 4096MB
#
# Reference: https://www.postgresql.org/docs/current/runtime-config-query.html#GUC-EFFECTIVE-CACHE-SIZE
POSTGRES_EFFECTIVE_CACHE_SIZE=1024MB


# ------------------------------
# Redis 配置相关
# This Redis configuration is used for caching and for pub/sub during conversation.
# ------------------------------

REDIS_HOST=************
REDIS_PORT=6379
REDIS_USERNAME=
REDIS_PASSWORD=difyai123456
REDIS_USE_SSL=false
REDIS_DB=0

# ------------------------------
# Celery Configuration
# ------------------------------

# Use redis as the broker, and redis db 1 for celery broker.
# Format as follows: `redis://<redis_username>:<redis_password>@<redis_host>:<redis_port>/<redis_database>`
# Example: redis://:difyai123456@redis:6379/1
# If use Redis Sentinel, format as follows: `sentinel://<sentinel_username>:<sentinel_password>@<sentinel_host>:<sentinel_port>/<redis_database>`
# Example: sentinel://localhost:26379/1;sentinel://localhost:26380/1;sentinel://localhost:26381/1
CELERY_BROKER_URL=redis://:difyai123456@************:6379/1


# Pin, include, and exclude tools
# Use comma-separated values with no spaces between items.
# ===== Required ENVS ======
# ===== Required ENVS ======
# ===== Required ENVS ======
NUM_WORKERS_PER_QUEUE=8
PORT=3002
HOST=0.0.0.0
REDIS_URL=redis://************:6379
REDIS_RATE_LIMIT_URL=redis://************:6379


NUM_WORKERS_PER_QUEUE=8
PORT=3002
HOST=0.0.0.0
REDIS_URL=redis://************:6379
REDIS_RATE_LIMIT_URL=redis://************:6379

# ------------------------------
# 插件守护进程 配置相关
# ------------------------------
PLUGIN_DAEMON_URL=http://************:5002
PLUGIN_DIFY_INNER_API_URL=http://************:5001
PLUGIN_PYTHON_ENV_INIT_TIMEOUT=360
PLUGIN_MAX_EXECUTION_TIMEOUT=2400


# ------------------------------
# Nginx 配置相关 default.conf.example
# ------------------------------
```nginx
# Please do not directly edit this file. Instead, modify the .env variables related to NGINX configuration.
upstream api_backend {
    server api:5001;
}

server {
    listen ${NGINX_PORT};
    server_name ${NGINX_SERVER_NAME};

    location /console/api {
      proxy_pass http://api_backend;
      include proxy.conf;
    }

    location /api {
      proxy_pass http://api_backend;
      include proxy.conf;
    }

    location /v1 {
      proxy_pass http://api_backend;
      include proxy.conf;
    }

    location /files {
      proxy_pass http://api_backend;
      include proxy.conf;
    }

    # location /explore {
    #   proxy_pass http://web:3000;
    #   include proxy.conf;
    # }

    location /e/ {
      proxy_pass http://************:5002;
      proxy_set_header Dify-Hook-Url $scheme://$host$request_uri;
      include proxy.conf;
    }

    # location / {
    #   proxy_pass http://web:3000;
    #   include proxy.conf;
    # }

    # placeholder for acme challenge location
    ${ACME_CHALLENGE_LOCATION}

    # placeholder for https config defined in https.conf.template
    ${HTTPS_CONFIG}
}
```

# ------------------------------
# docker-compose.yaml 配置相关
# ------------------------------

需要移除db、web、redis的依赖  
注释db、web、redis 、weaviate使用主节点的


### 修改这些参数
  - PLUGIN_REMOTE_INSTALL_HOST: http://************
  - DIFY_INNER_API_URL: http://************:5001
  - WEAVIATE_ENDPOINT=http://************:8080
  - redis暴露6379端口
  - postgresql暴露5432端口
  - plugin_daemon暴露5002端口
