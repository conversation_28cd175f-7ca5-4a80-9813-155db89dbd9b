package com.mkefu.chatflow.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR> 杨国锋
 * @since 2025-05-29
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "")
public class CozeKnowledgeEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "知识库信息。")
    @JsonProperty("knowledge_infos")
    private List<CozeKnowledgeInfosEntity> knowledgeInfos;
}
