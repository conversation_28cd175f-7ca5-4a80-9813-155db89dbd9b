package com.mkefu.wechat.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mkefu.common.entity.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.io.Serializable;

/**
 * 租户负载均衡关联Entity
 * <AUTHOR> 杨国锋 
 * @since 2025-07-15
 */
@Data
@TableName("mkefu_env_info")
@EqualsAndHashCode(callSuper = true)
@Schema(name = "租户负载均衡关联Entity", description = "MkefuEnvInfoEntity")
public class MkefuEnvInfoEntity extends BaseEntity implements Serializable {
    
    @Serial
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    @Schema(description = "主键uuid")
    private Long id;

    @TableField("plat_form_id")
    @Schema(description = "租户Id", example = "tenant_abcde")
    private String platFormId;

    @TableField("tenant_id")
    @Schema(description = "租户Id", example = "tenant_abcde")
    private String tenantId;

    @TableField("show_name")
    @Schema(description = "服务器环境", example = "prod")
    private String showName;
}