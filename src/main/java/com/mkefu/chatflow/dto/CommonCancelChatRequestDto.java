package com.mkefu.chatflow.dto;

import com.fasterxml.jackson.annotation.JsonAlias;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR> 杨国锋
 * @since 2025-06-09
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "通用取消进行中的对话请求对象", description = "CommonCancelChatRequestDto")
public class CommonCancelChatRequestDto implements Serializable {
    
    @Serial
    private static final long serialVersionUID = 1L;
    
    @NotBlank(message = "请传入chatId或taskId")
    @Schema(description = "coze中=chatId，dify中=taskId")
    @JsonAlias({"chatId", "taskId"})
    private String chatId;

    @NotBlank(message = "请传入userId或user")
    @Schema(description = "coze中=userId，dify中=user")
    @JsonAlias({"userId", "user"})
    private String userId;

    @NotBlank(message = "请传入conversationId")
    @Schema(description = "coze中=conversationId，dify中=conversationId")
    @JsonAlias({"conversationId"})
    private String conversationId;
}
