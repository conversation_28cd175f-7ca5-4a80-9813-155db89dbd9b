package com.mkefu.wechat.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.mkefu.wechat.entity.WeChatCommonEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 获取企业授权信息接口的响应DTO
 * <AUTHOR> 杨国锋
 * @since  2025-6-24
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Schema(name = "WeChatAuthInfoResponseDto", description = "获取企业授权信息接口的响DTO")
public class WeChatAuthInfoResponseDto extends WeChatCommonEntity implements Serializable {
    
    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "服务商企业信息")
    @JsonProperty("dealer_corp_info")
    private DealerCorpInfo dealerCorpInfo;

    @Schema(description = "授权方企业信息")
    @JsonProperty("auth_corp_info")
    private AuthCorpInfo authCorpInfo;

    @Schema(description = "授权的应用信息")
    @JsonProperty("auth_info")
    private AuthInfo authInfo;

    @Data
    @NoArgsConstructor
    @Schema(name = "DealerCorpInfo", description = "服务商企业信息")
    public static class DealerCorpInfo {
        @Schema(description = "服务商企业CorpID")
        @JsonProperty("corpid")
        private String corpid;

        @Schema(description = "服务商企业名称")
        @JsonProperty("corp_name")
        private String corpName;
    }

    @Data
    @NoArgsConstructor
    @Schema(name = "AuthCorpInfo", description = "授权方企业信息")
    public static class AuthCorpInfo {
        @Schema(description = "授权方企业CorpID")
        @JsonProperty("corpid")
        private String corpid;

        @Schema(description = "授权方企业名称")
        @JsonProperty("corp_name")
        private String corpName;

        @Schema(description = "企业类型，'verified'表示认证企业，'unverified'表示未认证企业")
        @JsonProperty("corp_type")
        private String corpType;

        @Schema(description = "授权方企业方形头像")
        @JsonProperty("corp_square_logo_url")
        private String corpSquareLogoUrl;

        @Schema(description = "授权方企业用户规模")
        @JsonProperty("corp_user_max")
        private Integer corpUserMax;

        @Schema(description = "授权方企业完整名称")
        @JsonProperty("corp_full_name")
        private String corpFullName;

        @Schema(description = "认证到期时间")
        @JsonProperty("verified_end_time")
        private Long verifiedEndTime;

        @Schema(description = "主体类型：1-企业，2-政府以及事业单位，3-其他组织，4-团队号")
        @JsonProperty("subject_type")
        private Integer subjectType;

        @Schema(description = "企业微信二维码")
        @JsonProperty("corp_wxqrcode")
        private String corpWxQrcode;


        @Schema(description = "企业规模")
        @JsonProperty("corp_scale")
        private String corpScale;

        @Schema(description = "企业所属行业")
        @JsonProperty("corp_industry")
        private String corpIndustry;

        @Schema(description = "企业所属子行业")
        @JsonProperty("corp_sub_industry")
        private String corpSubIndustry;

        @Schema(description = "企业曾用名")
        @JsonProperty("corp_ex_name")
        private CorpExName corpExName;
    }

    @Data
    @NoArgsConstructor
    @Schema(name = "CorpExName", description = "企业曾用名信息")
    public static class CorpExName {
        @Schema(description = "曾用名列表")
        @JsonProperty("name_list")
        private String nameList; // 注意：根据您的JSON，这里是String类型
    }

    @Data
    @NoArgsConstructor
    @Schema(name = "AuthInfo", description = "授权的应用信息")
    public static class AuthInfo {
        @Schema(description = "授权的应用列表")
        @JsonProperty("agent")
        private List<Agent> agent;
    }

    @Data
    @NoArgsConstructor
    @Schema(name = "Agent", description = "授权的应用详细信息")
    public static class Agent {
        @Schema(description = "授权方应用id")
        @JsonProperty("agentid")
        private Integer agentId;

        @Schema(description = "授权方应用名字")
        @JsonProperty("name")
        private String name;

        @Schema(description = "授权方应用圆形头像")
        @JsonProperty("round_logo_url")
        private String roundLogoUrl;

        @Schema(description = "授权方应用方形头像")
        @JsonProperty("square_logo_url")
        private String squareLogoUrl;

        @Schema(description = "旧的多应用套件中的应用id")
        @JsonProperty("appid")
        private Integer appid;

        @Schema(description = "授权模式，0为手动授权，1为自动授权")
        @JsonProperty("auth_mode")
        private Integer authMode;

        @Schema(description = "是否为定制应用")
        @JsonProperty("is_customized_app")
        private Boolean isCustomizedApp;

        @Schema(description = "是否从第三方应用导入")
        @JsonProperty("auth_from_thirdapp")
        private Boolean authFromThirdApp;

        @Schema(description = "应用对应的权限")
        @JsonProperty("privilege")
        private Privilege privilege;

        @Schema(description = "共享了此应用的企业的corpid")
        @JsonProperty("shared_from")
        private SharedFrom sharedFrom;
    }

    @Data
    @NoArgsConstructor
    @Schema(name = "Privilege", description = "应用对应的权限")
    public static class Privilege {
        @Schema(description = "权限等级：1-通讯录基本信息只读，2-通讯录基本信息读写，3-通讯录全部信息读写，4-单个基本信息只读，5-通讯录全部信息只读")
        @JsonProperty("level")
        private Integer level;

        @Schema(description = "应用可见范围（部门）")
        @JsonProperty("allow_party")
        private List<Integer> allowParty;

        @Schema(description = "应用可见范围（成员）")
        @JsonProperty("allow_user")
        private List<String> allowUser;

        @Schema(description = "应用可见范围（标签）")
        @JsonProperty("allow_tag")
        private List<Integer> allowTag;

        @Schema(description = "额外可见范围（部门）")
        @JsonProperty("extra_party")
        private List<Integer> extraParty;

        @Schema(description = "额外可见范围（成员）")
        @JsonProperty("extra_user")
        private List<String> extraUser;

        @Schema(description = "额外可见范围（标签）")
        @JsonProperty("extra_tag")
        private List<Integer> extraTag;
    }

    @Data
    @NoArgsConstructor
    @Schema(name = "SharedFrom", description = "共享应用信息")
    public static class SharedFrom {
        @Schema(description = "共享了此应用的企业的corpid")
        @JsonProperty("corpid")
        private String corpid;

        @Schema(description = "共享类型：0-普通共享，1-上下游共享")
        @JsonProperty("share_type")
        private Integer shareType;
    }
}