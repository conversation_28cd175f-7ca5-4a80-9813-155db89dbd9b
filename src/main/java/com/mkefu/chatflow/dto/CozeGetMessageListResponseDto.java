package com.mkefu.chatflow.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.mkefu.chatflow.entity.CozeBaseResponseEntity;
import com.mkefu.chatflow.entity.OpenMessageApiEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR> Yang 杨国锋
 * @since 2025-05-09
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "获取消息列表响应DTO", description = "CozeGetMessageListResponseDto")
@EqualsAndHashCode(callSuper = true)
public class CozeGetMessageListResponseDto extends CozeBaseResponseEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "消息详情。")
    private List<OpenMessageApiEntity> data;

    @Schema(description = "是否已返回全部消息。" +
            "true：未返回全部消息，可再次调用此接口查看其他分页。" +
            "false：已返回全部消息。")
    @JsonProperty("has_more")
    private boolean hasMore;

    @Schema(description = "返回的消息列表中，第一条消息的 Message ID。")
    @JsonProperty("first_id")
    private String firstId;

    @Schema(description = "返回的消息列表中，最后一条消息的 Message ID。")
    @JsonProperty("last_id")
    private String lastId;
}

