package com.mkefu.chatflow.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.Map;

/**
 * <AUTHOR> 杨国锋
 * @since 2025-05-08
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "取消进行中的对话响应实体类", description = "CozeCancelChatResponseDto")
public class CozeCancelChatResponseDto implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "被取消对话的详细信息")
    private Map<String, Object> data;

    @Schema(description = "详细信息")
    private Map<String, Object> detail;

    @Schema(description = "调用状态码。" +
            "0 表示调用成功。" +
            "其他值表示调用失败，你可以通过 msg 字段判断详细的错误原因。")
    private int code;

    @Schema(description = "调用状态信息。API 调用失败时可通过此字段查看详细错误信息。")
    private String msg;
}
