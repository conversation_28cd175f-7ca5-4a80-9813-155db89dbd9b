package com.mkefu.wechat.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 00服务器负载均衡实体类
 * <AUTHOR> 杨国锋
 * @since 2025-07-15
 */
@Data
@TableName("fs_mkefu_server_info")
@Schema(name = "00服务器负载均衡实体类", description = "FsMkefuServerInfoEntity")
public class FsMkefuServerInfoEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    @Schema(description = "自增主键ID", example = "1")
    private Long id;

    @TableField("show_name")
    @Schema(description = "显示名称，例如服务器环境标识", example = "Prod_Server_001")
    private String showName;

    @TableField("app_id")
    @Schema(description = "应用ID，唯一标识", example = "wx1234567890abcdef")
    private String appId;

    @TableField("app_id_query")
    @Schema(description = "用于查询的App ID或简写", example = "app001")
    private String appIdQuery;

    @TableField("access_token")
    @Schema(description = "访问令牌", example = "your_access_token_here")
    private String accessToken;

    @TableField("encoding_aes_key")
    @Schema(description = "消息加密解密密钥", example = "your_encoding_aes_key_here")
    private String encodingAesKey;

    @TableField("server_url")
    @Schema(description = "服务器URL", example = "https://yourdomain.com/webhook/callback")
    private String serverUrl;

    @TableField("api_version")
    @Schema(description = "API版本", example = "1", defaultValue = "1")
    private int apiVersion;

    @TableField("create_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建时间", example = "2024-07-29 10:00:00")
    private Date createTime;

    @TableField("update_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新时间", example = "2024-07-29 10:30:00")
    private Date updateTime;
}