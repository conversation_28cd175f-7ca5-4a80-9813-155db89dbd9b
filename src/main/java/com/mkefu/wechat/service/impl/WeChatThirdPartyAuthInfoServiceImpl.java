package com.mkefu.wechat.service.impl;

import cn.hutool.core.lang.UUID;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mkefu.common.constant.BaseCode;
import com.mkefu.common.constant.CustomConstant;
import com.mkefu.common.constant.ErrorCode;
import com.mkefu.common.entity.WeChatKfConfigEntity;
import com.mkefu.common.exception.BusinessException;
import com.mkefu.common.response.Result;
import com.mkefu.common.util.MkefuImLoadBalancerUtil;
import com.mkefu.common.util.RedisUtil;
import com.mkefu.system.entity.TenantAuthInfoEntity;
import com.mkefu.wechat.dto.*;
import com.mkefu.wechat.entity.*;
import com.mkefu.wechat.mapper.*;
import com.mkefu.wechat.service.WeChatThirdPartyAuthInfoService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.MalformedURLException;
import java.net.URI;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;


/**
 * 微信应用授权实现层
 * <AUTHOR> Yang 杨国锋
 * @since 2025-06-18
 */
@Slf4j
@Service
public class WeChatThirdPartyAuthInfoServiceImpl extends ServiceImpl<WeChatThirdPartyAuthInfoMapper, WeChatThirdPartyAuthInfoEntity> implements WeChatThirdPartyAuthInfoService {
    
    private final ObjectMapper objectMapper;
    
    private final WeChatCustomerCompanyInfoMapper weChatCustomerCompanyInfoMapper;
    
    private final RedisUtil redisUtil;
    
    private final WeChatKfConfigEntity weChatKfConfigEntity;
    
    private final WeChatKfAccountInfoMapper weChatKfAccountInfoMapper;
    
    private final MkefuEnvInfoMapper mkefuEnvInfoMapper;
    
    private final WeChatThirdPartyBaseInfoMapper weChatThirdPartyBaseInfoMapper;

    private final MkefuImLoadBalancerUtil mkefuImLoadBalancerUtil;

    public WeChatThirdPartyAuthInfoServiceImpl(ObjectMapper objectMapper, WeChatCustomerCompanyInfoMapper weChatCustomerCompanyInfoMapper, 
                                               RedisUtil redisUtil, WeChatKfConfigEntity weChatKfConfigEntity, WeChatKfAccountInfoMapper weChatKfAccountInfoMapper, 
                                               MkefuEnvInfoMapper mkefuEnvInfoMapper, WeChatThirdPartyBaseInfoMapper weChatThirdPartyBaseInfoMapper,
                                               MkefuImLoadBalancerUtil mkefuImLoadBalancerUtil) {
        this.objectMapper = objectMapper;
        this.weChatCustomerCompanyInfoMapper = weChatCustomerCompanyInfoMapper;
        this.redisUtil = redisUtil;
        this.weChatKfConfigEntity = weChatKfConfigEntity;
        this.weChatKfAccountInfoMapper = weChatKfAccountInfoMapper;
        this.mkefuEnvInfoMapper = mkefuEnvInfoMapper;
        this.weChatThirdPartyBaseInfoMapper = weChatThirdPartyBaseInfoMapper;
        this.mkefuImLoadBalancerUtil = mkefuImLoadBalancerUtil;
    }

    /**
     * 获取第三方应用凭证
     *
     * @param entity    请求体
     * @param receiveId 服务商Id
     */
    @Override
    public void getThirdPartyCredentials(WeChatThirdPartyAuthInfoEntity entity, String receiveId) {
        // 先从Redis拿预第三方应用凭证和预授权码的缓存
        if (ObjectUtils.isNotEmpty(entity)) {
            Object redisSuiteAccessTokenObject = null;
            Object redisThirdPartyPreAuthCodeObject = null;
            if (StringUtils.isNotBlank(entity.getSuiteAccessToken())) {
                redisSuiteAccessTokenObject = redisUtil.getSuiteAccessToken(entity.getSuiteAccessToken());
            }
            if (StringUtils.isNotBlank(entity.getSuitePreAuthCode())) {
                redisThirdPartyPreAuthCodeObject = redisUtil.getThirdPartyPreAuthCode(entity.getSuitePreAuthCode());
            }
            if (ObjectUtils.isEmpty(redisSuiteAccessTokenObject)) {
                // Redis中没有查到服务商应用凭证，代表过期了，需要重新获取凭证和预授权码
                log.info("Redis中服务商应用凭证已过期，需要重新获取服务商应用凭证和预授权码");
                getSuiteAccessTokenAndPreAuthCode(entity, receiveId);
                return;
            }
            if (ObjectUtils.isEmpty(redisThirdPartyPreAuthCodeObject)) {
                // Redis中没有查到预授权码，代表过期了 需要重新获取预授权码
                log.info("Redis中预授权码已过期，只需要重新获取预授权码");
                WeChatGetPreAuthCodeResponseDto dto;
                Result result = getPreAuthCode(entity.getSuiteAccessToken());
                try {
                    dto = objectMapper.readValue(objectMapper.writeValueAsString(result.getData()), WeChatGetPreAuthCodeResponseDto.class);
                    entity.setSuitePreAuthCode(dto.getPreAuthCode());
                    entity.setSuitePreAuthCodeExpiresIn(dto.getExpiresIn());
                    entity.setSuitePreAuthCodeCreateTime(new Date());
                    redisUtil.putByReceiveId(receiveId, entity, -1, null);
                } catch (JsonProcessingException e) {
                    throw new BusinessException(ErrorCode.JSON_PROCESSING_ERROR, e.getMessage());
                }
            }
        }
    }

    /**
     * 获取新凭证进行新增或者更新
     *
     * @param entity    请求体
     * @param receiveId 服务商Id
     */
    private void getSuiteAccessTokenAndPreAuthCode(WeChatThirdPartyAuthInfoEntity entity, String receiveId) {
        try {
            HttpRequest request = HttpRequest
                    .post("https://qyapi.weixin.qq.com/cgi-bin/service/get_suite_token")
                    .body(objectMapper.writeValueAsString(entity));
            try (HttpResponse response = request.execute()) {
                log.info("正在调用获取服务商应用凭证接口：{}，入参：{}", request.getUrl(), objectMapper.writeValueAsString(entity));
                String respBody = response.body();
                log.info("获取服务商应用凭证接口响应结果：{}", respBody);
                
                if (StringUtils.isNotBlank(respBody)) {
                    WeChatAppAuthResponseDto appAuthResponseDto = objectMapper.readValue(respBody, WeChatAppAuthResponseDto.class);
                    if (ObjectUtils.isNotEmpty(appAuthResponseDto.getErrorCode()) && !BaseCode.ZERO.getCode().equals(appAuthResponseDto.getErrorCode().toString())) {
                        if (String.valueOf(ErrorCode.WECHAT_IP_NOT_ALLOW.getStatus()).equals(appAuthResponseDto.getErrorCode().toString())) {
                            throw new BusinessException(String.valueOf(ErrorCode.WECHAT_IP_NOT_ALLOW.getStatus()), ErrorCode.WECHAT_IP_NOT_ALLOW.getDefaultMessage());
                        } else if (String.valueOf(ErrorCode.WECHAT_INVALID_SUITE_TICKET.getStatus()).equals(appAuthResponseDto.getErrorCode().toString())) {
                            throw new BusinessException(String.valueOf(ErrorCode.WECHAT_INVALID_SUITE_TICKET.getStatus()), ErrorCode.WECHAT_INVALID_SUITE_TICKET.getDefaultMessage());
                        } else if (String.valueOf(ErrorCode.WECHAT_INVALID_TOKEN.getStatus()).equals(appAuthResponseDto.getErrorCode().toString())) {
                            throw new BusinessException(String.valueOf(ErrorCode.WECHAT_INVALID_TOKEN.getStatus()), ErrorCode.WECHAT_INVALID_TOKEN.getDefaultMessage());
                        }
                    }

                    entity.setSuiteAccessToken(appAuthResponseDto.getSuiteAccessToken());
                    entity.setSuiteAccessTokenExpiresIn(appAuthResponseDto.getExpiresIn());
                    // 存入Redis
                    redisUtil.putBySuiteAccessToken(appAuthResponseDto.getSuiteAccessToken(), appAuthResponseDto, appAuthResponseDto.getExpiresIn(), TimeUnit.SECONDS);
                    // 获取预授权码
                    Result getPreAuthCodeResult = getPreAuthCode(appAuthResponseDto.getSuiteAccessToken());
                    if (getPreAuthCodeResult.isSuccess() && ObjectUtils.isNotEmpty(getPreAuthCodeResult.getData())) {
                        WeChatGetPreAuthCodeResponseDto preAuthCodeResponseDto = objectMapper.readValue(objectMapper.writeValueAsString(getPreAuthCodeResult.getData()), WeChatGetPreAuthCodeResponseDto.class);
                        entity.setSuitePreAuthCode(preAuthCodeResponseDto.getPreAuthCode());
                        entity.setSuitePreAuthCodeExpiresIn(preAuthCodeResponseDto.getExpiresIn());
                        entity.setSuitePreAuthCodeCreateTime(new Date());
                        entity.setSuiteAccessTokenCreateTime(new Date());
                        // 存入Redis
                        redisUtil.putByReceiveId(receiveId, entity, -1, null);
                        // 存入Redis
                        redisUtil.putByThirdPartyPreAuthCode(preAuthCodeResponseDto.getPreAuthCode(), preAuthCodeResponseDto, preAuthCodeResponseDto.getExpiresIn(), TimeUnit.SECONDS);
                    }
                }
            }
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw new BusinessException(String.valueOf(ErrorCode.JSON_PROCESSING_ERROR.getStatus()), ErrorCode.JSON_PROCESSING_ERROR.getDefaultMessage());
        }
    }

    /**
     * 获取预授权码
     * @param suiteAccessToken 第三方应用凭证
     * @return 响应体
     */
    @Override
    public Result getPreAuthCode(String suiteAccessToken) {
        try {
            HttpRequest request = HttpRequest
                    .get("https://qyapi.weixin.qq.com/cgi-bin/service/get_pre_auth_code")
                    .form("suite_access_token", suiteAccessToken);
            try (HttpResponse response = request.execute()) {
                log.info("调用微信获取预授权码接口：{}", request.getUrl());
                String respBody = response.body();
                log.info("微信响应结果：{}", respBody);
                if (StringUtils.isNotBlank(respBody)) {
                    WeChatGetPreAuthCodeResponseDto dto = objectMapper.readValue(respBody, WeChatGetPreAuthCodeResponseDto.class);
                    // 预授权码存入Redis
                    redisUtil.putByThirdPartyPreAuthCode(dto.getPreAuthCode(), dto, dto.getExpiresIn(), TimeUnit.SECONDS);
                    return Result.success(dto);
                }
                throw new BusinessException(ErrorCode.WECHAT_PRE_AUTH_CODE_FAILED.getCode(), "微信响应体为空");
            }
        } catch (BusinessException e) {
            log.error("获取第三方应用凭证失败：{}", e.getMessage(), e);
            throw new BusinessException(ErrorCode.WECHAT_PRE_AUTH_CODE_FAILED.getCode(), ErrorCode.WECHAT_PRE_AUTH_CODE_FAILED.getDefaultMessage());
        } catch (Exception e) {
            log.error("获取第三方应用凭证发生未知异常：{}", e.getMessage(), e);
            throw new BusinessException(ErrorCode.WECHAT_PRE_AUTH_CODE_FAILED.getCode(), ErrorCode.WECHAT_PRE_AUTH_CODE_FAILED.getDefaultMessage());
        }
    }

    /**
     * 获取企业永久授权码
     *
     * @param suiteAccessToken  第三方应用凭证
     * @param temporaryAuthCode 用户扫二维码授权之后，微信回调给的临时授权码
     * @param suiteId           服务商服务Id
     * @param platFormId        平台Id
     * @param baseInfoEntity    服务商基础信息
     * @param state             uuid
     */
    @Override
    public void getPermanentCode(String suiteAccessToken, String temporaryAuthCode, String suiteId, String platFormId, WeChatThirdPartyBaseInfoEntity baseInfoEntity, String state) {
        Map<String, Object> requestMap = HashMap.newHashMap(1);
        requestMap.put("auth_code", temporaryAuthCode);
        Object redisByUuidObj = redisUtil.getByUuid(state);
        String tenantId = "";
        String showName = "";
        String headImageUrl = "";
        if (ObjectUtils.isNotEmpty(redisByUuidObj)) {
            WeChatThirdPartyAuthInfoEntity redisAuthInfoEntity = objectMapper.convertValue(redisByUuidObj, WeChatThirdPartyAuthInfoEntity.class);
            tenantId = redisAuthInfoEntity.getTenantId();
            showName = redisAuthInfoEntity.getShowName();
            headImageUrl = redisAuthInfoEntity.getHeadImageUrl();
        }
        try {
            HttpRequest request = HttpRequest
                    .post("https://qyapi.weixin.qq.com/cgi-bin/service/v2/get_permanent_code?suite_access_token=" + suiteAccessToken)
                    .body(objectMapper.writeValueAsString(requestMap));
            try (HttpResponse response = request.execute()) {
                log.info("调用微信获取企业永久授权码接口：{}，入参：{}", request.getUrl(),  temporaryAuthCode);
                String respBody = response.body();
                log.info("微信响应结果：{}", respBody);
                WeChatGetPermanentCodeResponseDto dto = objectMapper.readValue(respBody, WeChatGetPermanentCodeResponseDto.class);
                WeChatCustomerCompanyInfoEntity customerCompanyInfoEntity = convertDtoToEntity(dto);
                customerCompanyInfoEntity.setTenantId(tenantId);
                customerCompanyInfoEntity.setPlatFormId(platFormId);
                WeChatCustomerCompanyInfoEntity existsCustomerCompanyInfoEntity = weChatCustomerCompanyInfoMapper
                        .selectOne(new QueryWrapper<WeChatCustomerCompanyInfoEntity>()
                                .eq("plat_form_id", platFormId)
                                .eq("tenant_id", tenantId)
                                .eq("del_flag", BaseCode.ONE.getCode())
                );
                if (ObjectUtils.isNotEmpty(customerCompanyInfoEntity)) {
                    // 存储到租户Id和im-service不同环境绑定的表中
                    MkefuEnvInfoEntity mkefuEnvInfoEntity = new MkefuEnvInfoEntity();
                    mkefuEnvInfoEntity.setShowName(showName);
                    mkefuEnvInfoEntity.setPlatFormId(platFormId);
                    mkefuEnvInfoEntity.setTenantId(tenantId);
                    mkefuEnvInfoMapper.insertOrUpdate(mkefuEnvInfoEntity);
                    if (ObjectUtils.isNotEmpty(existsCustomerCompanyInfoEntity)) {
                        // 设置ID用于更新操作
                        customerCompanyInfoEntity.setUpdateTime(new Date());
                        weChatCustomerCompanyInfoMapper.updateById(customerCompanyInfoEntity);
                        return;
                    }
                    if (weChatCustomerCompanyInfoMapper.insert(customerCompanyInfoEntity) > 0) {
                        Object redisByReceiveIdObj = redisUtil.getByReceiveId(baseInfoEntity.getReceiveId());
                        if (ObjectUtils.isNotEmpty(redisByReceiveIdObj)) {
                            WeChatThirdPartyAuthInfoEntity thirdPartyAuthInfoEntity = objectMapper.readValue(objectMapper.writeValueAsString(redisByReceiveIdObj), WeChatThirdPartyAuthInfoEntity.class);
                            String corpAccessToken = getCorpAccessToken(customerCompanyInfoEntity.getPermanentCode(), customerCompanyInfoEntity.getCorpId(), thirdPartyAuthInfoEntity.getSuiteAccessToken());
                            // 添加默认客服账号
                            String openKfId = addKfAccount(corpAccessToken, headImageUrl);
                            // 获取客服账号链接
                            String kfAccountUrl = getKfAccountLink(corpAccessToken, openKfId);
                            // 存储到客服信息表中
                            insertIntoKfAccountInfo(openKfId, kfAccountUrl, customerCompanyInfoEntity.getCorpId(), thirdPartyAuthInfoEntity.getSuiteId(), platFormId, tenantId);
                        }
                    }
                    // 通知im-service授权绑定成功
                    noticeImServiceSuccessBind(tenantId);
                    return;
                }
                throw new BusinessException(ErrorCode.WECHAT_GET_PERMANENT_CODE_FAILED.getCode(), ErrorCode.WECHAT_GET_PERMANENT_CODE_FAILED.getDefaultMessage());
            }
        } catch (Exception e) {
            log.error("获取企业永久授权码发生异常：{}", e.getMessage(), e);
        }
    }

    private void noticeImServiceSuccessBind(String tenantId) {
        WeChatMessageToImServiceRequestDto dto = new WeChatMessageToImServiceRequestDto();
        dto.setTenantId(tenantId);
        mkefuImLoadBalancerUtil.balanceByTenantId(dto, CustomConstant.NOTICE_BIND_SUCCESS);
    }

    /**
     * 获取租户对应的授权绑定链接
     * @param jsonTenantAuthInfoEntity 租户实体类
     * @return 授权绑定链接
     */
    @Override
    public String getAuthInfoUrlByTenantId(String jsonTenantAuthInfoEntity) {
        Object redisByReceiveIdObj = redisUtil.getByReceiveId(weChatKfConfigEntity.getReceiveId());
        WeChatThirdPartyAuthInfoEntity redisEntity = new WeChatThirdPartyAuthInfoEntity();
        String uuid = UUID.randomUUID().toString();
        
        try {
            TenantAuthInfoEntity authInfoEntity = objectMapper.readValue(jsonTenantAuthInfoEntity, TenantAuthInfoEntity.class);
            String headImageUrl = new String(authInfoEntity.getHeadImageUrl().getBytes(StandardCharsets.ISO_8859_1), StandardCharsets.UTF_8);
            
            String[] parts = authInfoEntity.getTenantId().split("-");
            String showName = parts[parts.length - 1];
            String tenantId = parts[0];
            WeChatGetPreAuthCodeResponseDto redisPreAuthCodeDto = new WeChatGetPreAuthCodeResponseDto();
            WeChatGetPreAuthCodeResponseDto reGetPreAuthCodeDto;
            if (ObjectUtils.isNotEmpty(redisByReceiveIdObj)) {
                redisEntity = objectMapper.convertValue(redisByReceiveIdObj, WeChatThirdPartyAuthInfoEntity.class);
                // 设置客服默认头像Url
                redisEntity.setHeadImageUrl(headImageUrl);
                redisEntity.setTenantId(tenantId);
                redisEntity.setShowName(showName);
                // 检查Redis中的凭证状态
                Object redisSuiteAccessToken = redisUtil.getSuiteAccessToken(redisEntity.getSuiteAccessToken());
                Object redisPreAuthCodeObj = redisUtil.getThirdPartyPreAuthCode(redisEntity.getSuitePreAuthCode());
                if (ObjectUtils.isNotEmpty(redisPreAuthCodeObj)) {
                    redisPreAuthCodeDto = objectMapper.convertValue(redisPreAuthCodeObj, WeChatGetPreAuthCodeResponseDto.class);
                }
                // 服务商token过期了，需要重新获取token和预授权码并组装授权链接
                if (ObjectUtils.isEmpty(redisSuiteAccessToken)) {
                    getSuiteAccessTokenAndPreAuthCode(redisEntity, weChatKfConfigEntity.getReceiveId());
                    redisEntity.setSuiteAuthUrl("https://work.weixin.qq.com/kf/third/auth/page?" + "suite_id" + "=" + redisEntity.getSuiteId()
                            + "&" + "pre_auth_code" + "=" + redisPreAuthCodeDto.getPreAuthCode()
                            + "&" + "state=" + uuid);
                    redisUtil.putByUuid(uuid, redisEntity, 2, TimeUnit.HOURS);
                    redisUtil.putByReceiveId(weChatKfConfigEntity.getReceiveId(), redisEntity, -1, null);
                    return redisEntity.getSuiteAuthUrl();
                }
                // 预授权码过期了，需要重新获取组装授权链接
                if (ObjectUtils.isEmpty(redisPreAuthCodeDto)) {
                    Result result = getPreAuthCode(redisEntity.getSuiteAccessToken());
                    reGetPreAuthCodeDto = objectMapper.readValue(objectMapper.writeValueAsString(result.getData()), WeChatGetPreAuthCodeResponseDto.class);
                    redisEntity.setTenantId(tenantId);
                    redisEntity.setShowName(showName);
                    redisEntity.setSuitePreAuthCode(reGetPreAuthCodeDto.getPreAuthCode());
                    redisEntity.setSuitePreAuthCodeExpiresIn(reGetPreAuthCodeDto.getExpiresIn());
                    redisEntity.setSuitePreAuthCodeCreateTime(new Date());
                    redisEntity.setSuiteAuthUrl("https://work.weixin.qq.com/kf/third/auth/page?" + "suite_id" + "=" + redisEntity.getSuiteId()
                            + "&" + "pre_auth_code" + "=" + redisEntity.getSuitePreAuthCode()
                            + "&" + "state=" + uuid);
                    redisUtil.putByReceiveId(weChatKfConfigEntity.getReceiveId(), redisEntity, -1, null);
                    redisUtil.putByUuid(uuid, redisEntity, 2, TimeUnit.HOURS);
                    return redisEntity.getSuiteAuthUrl();
                }
            }
            // 如果凭证都有效，直接返回授权URL
            if (StringUtils.isBlank(redisEntity.getSuiteAuthUrl())) {
                redisEntity.setTenantId(tenantId);
                redisEntity.setShowName(showName);
                redisEntity.setSuiteAuthUrl("https://work.weixin.qq.com/kf/third/auth/page?" + "suite_id" + "=" + redisEntity.getSuiteId()
                        + "&" + "pre_auth_code" + "=" + redisPreAuthCodeDto.getPreAuthCode()
                        + "&" + "state=" + uuid);
            }
            if (!redisPreAuthCodeDto.getPreAuthCode().equals(redisEntity.getSuiteAuthUrl())) {
                    redisEntity.setSuitePreAuthCode(redisPreAuthCodeDto.getPreAuthCode());
                    redisEntity.setSuitePreAuthCodeExpiresIn(redisPreAuthCodeDto.getExpiresIn());
                    redisEntity.setSuiteAuthUrl("https://work.weixin.qq.com/kf/third/auth/page?" + "suite_id" + "=" + redisEntity.getSuiteId()
                            + "&" + "pre_auth_code" + "=" + redisPreAuthCodeDto.getPreAuthCode()
                            + "&" + "state=" + uuid);
                }

            redisUtil.putByUuid(uuid, redisEntity, 2, TimeUnit.HOURS);
            return redisEntity.getSuiteAuthUrl();
        } catch (JsonProcessingException e) {
            log.error("获取第三方应用凭证失败：{}", e.getMessage(), e);
            throw new BusinessException(ErrorCode.JSON_PROCESSING_ERROR.getCode(), ErrorCode.JSON_PROCESSING_ERROR.getDefaultMessage());
        }
        
    }


    /**
     * 获取授权方企业凭证
     * @param jsonTenantAuthInfoEntity 租户信息
     * @return 授权方企业凭证
     */
    @Override
    public String getCorpAccessToken(String jsonTenantAuthInfoEntity) {
        try {
            TenantAuthInfoEntity tenantInfoEntity = objectMapper.readValue(jsonTenantAuthInfoEntity, TenantAuthInfoEntity.class);
            WeChatCustomerCompanyInfoEntity customerCompanyInfoEntity = weChatCustomerCompanyInfoMapper.selectOne(new QueryWrapper<WeChatCustomerCompanyInfoEntity>()
                    .eq("tenant_id", tenantInfoEntity.getTenantId())
            );
            if (ObjectUtils.isNotEmpty(customerCompanyInfoEntity)) {
                Object redisCorpAccessToken = redisUtil.getCorpAccessToken(customerCompanyInfoEntity.getCorpId());
                if (ObjectUtils.isNotEmpty(redisCorpAccessToken)) {
                    return objectMapper.convertValue(redisCorpAccessToken, WeChatCorpAccessTokenResponseDto.class).getCorpAccessToken();
                }
                Object redisByReceiveIdObj = redisUtil.getByReceiveId(weChatKfConfigEntity.getReceiveId());
                if (ObjectUtils.isNotEmpty(redisByReceiveIdObj)) {
                    WeChatThirdPartyAuthInfoEntity thirdPartyAuthInfoEntity = objectMapper.readValue(objectMapper.writeValueAsString(redisByReceiveIdObj), WeChatThirdPartyAuthInfoEntity.class);
                    return getCorpAccessToken(customerCompanyInfoEntity.getPermanentCode(), customerCompanyInfoEntity.getCorpId(), thirdPartyAuthInfoEntity.getSuiteAccessToken());
                }
            }
            throw new BusinessException(ErrorCode.WECHAT_TENANT_ID_NOT_FOUND);
        } catch (JsonProcessingException e) {
            throw new BusinessException(ErrorCode.JSON_PROCESSING_ERROR, e.getMessage());
        }
    }

    /**
     * 生成客服账号和客服链接
     * @param weChatKfAccountInfoEntity 客服信息
     * @return 客服聊天链接
     */
    @Override
    public String generatorKfAccount(WeChatKfAccountInfoEntity weChatKfAccountInfoEntity) {
        WeChatCustomerCompanyInfoEntity customerCompanyInfoEntity = weChatCustomerCompanyInfoMapper.selectOne(new QueryWrapper<WeChatCustomerCompanyInfoEntity>()
                .lambda()
                .eq(WeChatCustomerCompanyInfoEntity::getTenantId, weChatKfAccountInfoEntity.getTenantId()));
        Object redisCorpAccessTokenObj = redisUtil.getCorpAccessToken(customerCompanyInfoEntity.getCorpId());
        String corpAccessToken;
        if (ObjectUtils.isEmpty(redisCorpAccessTokenObj)) {
            // 授权方企业token不存在需要重新获取
            String receiveId = weChatKfConfigEntity.getReceiveId();
            Object redisByReceiveIdObj = redisUtil.getByReceiveId(receiveId);
            if (ObjectUtils.isEmpty(redisByReceiveIdObj)) {
                log.error("生成客服失败，请检查Redis中是否有服务商基础信息：{}", receiveId);
                throw new BusinessException(ErrorCode.WECHAT_THIRD_PARTY_AUTH_INFO_NOT_FOUND);
            }
            WeChatThirdPartyAuthInfoEntity authInfoEntity = objectMapper.convertValue(redisByReceiveIdObj, WeChatThirdPartyAuthInfoEntity.class);
            corpAccessToken = getCorpAccessToken(authInfoEntity.getPermanentCode(), customerCompanyInfoEntity.getCorpId(), authInfoEntity.getSuiteAccessToken());
        } else {
            WeChatCorpAccessTokenResponseDto weChatCorpAccessTokenResponseDto = objectMapper.convertValue(redisCorpAccessTokenObj, WeChatCorpAccessTokenResponseDto.class);
            corpAccessToken = weChatCorpAccessTokenResponseDto.getCorpAccessToken();
        }
        String openKfId = addKfAccount(corpAccessToken, weChatKfAccountInfoEntity);
        weChatKfAccountInfoEntity.setOpenKfId(openKfId);
        String kfAccountLink = getKfAccountLink(corpAccessToken, weChatKfAccountInfoEntity);
        weChatKfAccountInfoEntity.setKfAccountUrl(kfAccountLink);
        String platFormId = customerCompanyInfoEntity.getPlatFormId();
        WeChatThirdPartyBaseInfoEntity baseInfoEntity = weChatThirdPartyBaseInfoMapper
                .selectOne(new QueryWrapper<WeChatThirdPartyBaseInfoEntity>()
                        .lambda()
                        .eq(WeChatThirdPartyBaseInfoEntity::getPlatFormId, platFormId)
                );
        weChatKfAccountInfoEntity.setPlatFormId(platFormId);
        insertIntoKfAccountInfo(weChatKfAccountInfoEntity, customerCompanyInfoEntity.getCorpId(), baseInfoEntity.getSuiteId());
        return kfAccountLink;
    }

    /**
     * 获取授权方企业授权信息
     * @param jsonTenantAuthInfoEntity 租户信息
     * @return 认证名称
     */
    @Override
    public String getCorpAuthInfo(String jsonTenantAuthInfoEntity) {
        if (StringUtils.isBlank(jsonTenantAuthInfoEntity)) {
            throw new BusinessException(ErrorCode.INVALID_PARAM);
        }
        TenantAuthInfoEntity tenantAuthInfoEntity = objectMapper.convertValue(jsonTenantAuthInfoEntity, TenantAuthInfoEntity.class);
        List<WeChatCustomerCompanyInfoEntity> weChatCustomerCompanyInfoList = weChatCustomerCompanyInfoMapper.selectList(new QueryWrapper<WeChatCustomerCompanyInfoEntity>()
                .lambda().eq(WeChatCustomerCompanyInfoEntity::getTenantId, tenantAuthInfoEntity.getTenantId()));
        if (CollectionUtils.isEmpty(weChatCustomerCompanyInfoList)) {
            throw new BusinessException(ErrorCode.WECHAT_TENANT_ID_NOT_FOUND);
        }
        WeChatCustomerCompanyInfoEntity customerCompanyInfoEntity = weChatCustomerCompanyInfoList.getFirst();
        Map<String, Object> bodyMap = Map.of(
                "auth_corpid", customerCompanyInfoEntity.getCorpId(),
                "permanent_code", customerCompanyInfoEntity.getPermanentCode()
        );
        Object redisByReceiveIdOjb = redisUtil.getByReceiveId(weChatKfConfigEntity.getReceiveId());
        WeChatThirdPartyAuthInfoEntity authInfoEntity = new WeChatThirdPartyAuthInfoEntity();
        if (ObjectUtils.isNotEmpty(redisByReceiveIdOjb)) {
            authInfoEntity = objectMapper.convertValue(redisByReceiveIdOjb, WeChatThirdPartyAuthInfoEntity.class);
        }
        try {
            HttpRequest request = HttpRequest
                    .post("https://qyapi.weixin.qq.com/cgi-bin/service/get_auth_info?suite_access_token=" + authInfoEntity.getPermanentCode())
                    .body(objectMapper.writeValueAsString(bodyMap));
            try (HttpResponse response = request.execute()) {
                String respBody = response.body();
                JsonNode jsonNode = objectMapper.readTree(respBody);
                if (!jsonNode.has(CustomConstant.MEDIA_ID)) {
                    String errcode = jsonNode.at("/errcode").asText();
                    if (String.valueOf(ErrorCode.WECHAT_INVALID_TOKEN.getStatus()).equals(errcode)) {
                        throw new BusinessException(ErrorCode.WECHAT_INVALID_TOKEN);
                    }
                }
                return jsonNode.at("/auth_corp_info/corp_ex_name").asText();
            }
        } catch (Exception e) {
            throw new BusinessException(ErrorCode.INVALID_PARAM, e.getMessage());
        }
    }

    /**
     * 存储默认客服信息
     *
     * @param openKfId     客服账号ID
     * @param kfAccountUrl 客服账号链接
     * @param corpId       授权方企业ID
     * @param suiteId      服务商应用ID
     * @param platFormId   平台Id
     */
    private void insertIntoKfAccountInfo(String openKfId, String kfAccountUrl, String corpId, String suiteId, String platFormId, String tenantId) {
        WeChatKfAccountInfoEntity kfAccountInfoEntity = new WeChatKfAccountInfoEntity();
        kfAccountInfoEntity.setKfName(weChatKfConfigEntity.getName());
        kfAccountInfoEntity.setHeadImageUrl(weChatKfConfigEntity.getHeadImageUrl());
        kfAccountInfoEntity.setTenantId(tenantId);
        kfAccountInfoEntity.setOpenKfId(openKfId);
        kfAccountInfoEntity.setPlatFormId(platFormId);
        // 默认渠道微信扫码
        kfAccountInfoEntity.setPlatFormType("wechat");
        kfAccountInfoEntity.setKfAccountUrl(kfAccountUrl);
        kfAccountInfoEntity.setCorpId(corpId);
        kfAccountInfoEntity.setSuiteId(suiteId);
        weChatKfAccountInfoMapper.insertOrUpdate(kfAccountInfoEntity);
    }

    /**
     * 存储客服信息
     *
     * @param weChatKfAccountInfoEntity 客服信息
     * @param corpId       授权方企业ID
     * @param suiteId      服务商应用ID
     */
    private void insertIntoKfAccountInfo(WeChatKfAccountInfoEntity weChatKfAccountInfoEntity, String corpId, String suiteId) {
        weChatKfAccountInfoEntity.setCorpId(corpId);
        weChatKfAccountInfoEntity.setSuiteId(suiteId);
        weChatKfAccountInfoMapper.insertOrUpdate(weChatKfAccountInfoEntity);
    }

    /**
     * 获取客服账号默认链接
     * @param corpAccessToken 授权方企业令牌
     * @param openKfId 客服账号ID                      
     * @return 客服账号链接
     */
    public String getKfAccountLink(String corpAccessToken, String openKfId) {
        try {
            Map<String, Object> bodyMap = Map.of(
                    "open_kfid", openKfId,
                    "scene", "wechat"
            );
            String jsonBody = objectMapper.writeValueAsString(bodyMap);
            HttpRequest request = HttpRequest
                    .post("https://qyapi.weixin.qq.com/cgi-bin/kf/add_contact_way?access_token=" + corpAccessToken)
                    .body(jsonBody);
            try (HttpResponse response = request.execute()) {
                String body = response.body();
                return objectMapper.readTree(body).at("/url").asText();
            }
        } catch (JsonProcessingException e) {
            log.error("获取客服账号链接发生未知异常：{}", e.getMessage(), e);
            return "";
        }
    }

    /**
     * 获取客服账号链接
     * @param corpAccessToken 授权方企业令牌
     * @param weChatKfAccountInfoEntity 客服账号信息                     
     * @return 客服账号链接
     */
    public String getKfAccountLink(String corpAccessToken, WeChatKfAccountInfoEntity weChatKfAccountInfoEntity) {
        try {
            Map<String, Object> bodyMap = Map.of(
                    "open_kfid", weChatKfAccountInfoEntity.getOpenKfId(),
                    "scene", weChatKfAccountInfoEntity.getPlatFormType()
            );
            String jsonBody = objectMapper.writeValueAsString(bodyMap);
            HttpRequest request = HttpRequest
                    .post("https://qyapi.weixin.qq.com/cgi-bin/kf/add_contact_way?access_token=" + corpAccessToken)
                    .body(jsonBody);
            try (HttpResponse response = request.execute()) {
                String body = response.body();
                return objectMapper.readTree(body).at("/url").asText();
            }
        } catch (JsonProcessingException e) {
            log.error("获取客服账号链接发生未知异常：{}", e.getMessage(), e);
            return "";
        }
    }

    /**
     * 获取授权方企业令牌
     * @param permanentCode 永久授权码
     * @param corpId 授权方企业ID
     * @param suiteAccessToken 代开发企业应用凭证
     * @return 授权方企业令牌
     */
    public String getCorpAccessToken(String permanentCode, String corpId, String suiteAccessToken) {
        validateParameter(permanentCode, "永久授权码");
        validateParameter(corpId, "企业ID");
        validateParameter(suiteAccessToken, "应用凭证");

        try {
            Object redisCorpAccessTokenObject = redisUtil.getCorpAccessToken(corpId);
            if (ObjectUtils.isNotEmpty(redisCorpAccessTokenObject)) {
                log.info("使用Redis中已缓存的企业令牌：corpId={}", corpId);
                WeChatCorpAccessTokenResponseDto cachedDto = objectMapper.convertValue(
                        redisCorpAccessTokenObject,
                        WeChatCorpAccessTokenResponseDto.class);
                return cachedDto.getCorpAccessToken();
            }

            // 从API获取新令牌
            Map<String, Object> bodyMap = Map.of(
                    "auth_corpid", corpId,
                    "permanent_code", permanentCode
            );

            String requestUrl = "https://qyapi.weixin.qq.com/cgi-bin/service/get_corp_token?suite_access_token=" + suiteAccessToken;
            log.info("请求获取企业令牌：URL={}, corpId={}", requestUrl, corpId);

            HttpRequest request = HttpRequest
                    .post(requestUrl)
                    .body(objectMapper.writeValueAsString(bodyMap));

            try (HttpResponse response = request.execute()) {
                String body = response.body();
                log.info("微信获取企业令牌响应：{}", body);

                WeChatCorpAccessTokenResponseDto responseDto = objectMapper.readValue(body, WeChatCorpAccessTokenResponseDto.class);

                if (responseDto.getErrorCode() != null && responseDto.getErrorCode() != 0) {
                    String errorMsg = "获取企业令牌失败：" + responseDto.getErrorMsg();
                    log.error("获取企业令牌失败：errcode={}, errmsg={}", responseDto.getErrorCode(), responseDto.getErrorMsg());
                    throw new BusinessException(ErrorCode.WECHAT_API_ERROR.getCode(), errorMsg);
                }

                log.info("企业令牌缓存不存在，存入Redis：corpId={}, expiresIn={}", corpId, responseDto.getExpiresIn());
                redisUtil.putCorpAccessToken(
                        corpId,
                        responseDto,
                        responseDto.getExpiresIn(),
                        TimeUnit.SECONDS);
                return responseDto.getCorpAccessToken();
            }
        } catch (JsonProcessingException e) {
            log.error("处理JSON数据失败：{}", e.getMessage(), e);
            throw new BusinessException(ErrorCode.JSON_PROCESSING_ERROR, "处理JSON数据失败：" + e.getMessage());
        } catch (BusinessException e) {
            log.error("获取企业令牌时发生未知异常：{}", e.getMessage(), e);
            throw new BusinessException(ErrorCode.WECHAT_API_ERROR.getCode(), "获取企业令牌失败：" + e.getMessage());
        }
    }

    /**
     * 校验参数是否为空
     * @param value 参数值
     * @param name 参数名
     */
    private void validateParameter(String value, String name) {
        if (StringUtils.isBlank(value)) {
            String errorMessage = name + "不能为空";
            log.error("获取企业令牌失败：{}", errorMessage);
            throw new BusinessException(ErrorCode.INVALID_PARAM, errorMessage);
        }
    }


    /**
     * 添加默认客服账号
     *
     * @param corpAccessToken 授权方企业令牌
     * @param headImageUrl    客服默认头像Url
     * @return 客服账号的open_kfid
     */
    private String addKfAccount(String corpAccessToken, String headImageUrl) {
        // 参数校验
        validateParameter(corpAccessToken, "授权方企业令牌");
        validateParameter(headImageUrl, "客服头像URL");
        validateParameter(weChatKfConfigEntity.getName(), "客服名称");

        try {
            // 上传临时素材
            String mediaId = uploadTempMedia(corpAccessToken, headImageUrl);
            if (StringUtils.isBlank(mediaId)) {
                log.error("上传临时素材失败，获取到的mediaId为空");
                throw new BusinessException(ErrorCode.WECHAT_UPLOAD_TEMP_MEDIA_FAILED);
            }

            // 调用微信接口添加客服账号
            return callWeChatAddKfAccountApi(corpAccessToken, mediaId, weChatKfConfigEntity.getName());
        } catch (BusinessException e) {
            log.error("添加默认客服账号失败：{}", e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            log.error("添加默认客服账号发生未知异常：{}", e.getMessage(), e);
            throw new BusinessException(ErrorCode.WECHAT_GENERATOR_KF_ACCOUNT_FAILED, "添加客服账号失败：" + e.getMessage());
        }
    }

    /**
     * 调用微信接口添加客服账号
     *
     * @param corpAccessToken 企业令牌
     * @param mediaId         素材ID
     * @param kfName          客服名称
     * @return 客服账号的open_kfid
     */
    private String callWeChatAddKfAccountApi(String corpAccessToken, String mediaId, String kfName) {
        Map<String, Object> bodyMap = Map.of(
                "name", kfName,
                CustomConstant.MEDIA_ID, mediaId
        );
        try {
            String jsonBody = objectMapper.writeValueAsString(bodyMap);
            String requestUrl = "https://qyapi.weixin.qq.com/cgi-bin/kf/account/add?access_token=" + corpAccessToken;

            log.info("调用微信添加客服账号接口：URL={}, 请求参数：{}", requestUrl, jsonBody);

            HttpRequest request = HttpRequest
                    .post(requestUrl)
                    .body(jsonBody);

            try (HttpResponse response = request.execute()) {
                String responseBody = response.body();
                log.info("微信添加客服账号接口响应：{}", responseBody);

                if (StringUtils.isBlank(responseBody)) {
                    throw new BusinessException(ErrorCode.WECHAT_API_ERROR, "微信接口响应为空");
                }

                JsonNode jsonNode = objectMapper.readTree(responseBody);

                // 检查错误码
                if (jsonNode.has(CustomConstant.ERROR_CODE)) {
                    int errcode = jsonNode.at("/errcode").asInt();
                    if (errcode != 0) {
                        String errmsg = jsonNode.at("/errmsg").asText();
                        log.error("微信添加客服账号失败：errcode={}, errmsg={}", errcode, errmsg);
                        throw new BusinessException(ErrorCode.WECHAT_API_ERROR, 
                                "微信添加客服账号失败：" + errmsg);
                    }
                }

                // 获取open_kfid
                JsonNode openKfIdNode = jsonNode.at("/open_kfid");
                if (openKfIdNode.isMissingNode() || StringUtils.isBlank(openKfIdNode.asText())) {
                    log.error("微信接口返回的open_kfid为空，响应内容：{}", responseBody);
                    throw new BusinessException(ErrorCode.WECHAT_API_ERROR, "获取客服账号ID失败");
                }

                String openKfId = openKfIdNode.asText();
                log.info("成功添加微信客服账号，open_kfid：{}", openKfId);
                return openKfId;
            }
        } catch (JsonProcessingException e) {
            log.error("处理JSON数据失败：{}", e.getMessage(), e);
            throw new BusinessException(ErrorCode.JSON_PROCESSING_ERROR, "处理JSON数据失败：" + e.getMessage());
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("调用微信添加客服账号接口发生异常：{}", e.getMessage(), e);
            throw new BusinessException(ErrorCode.WECHAT_API_ERROR, "调用微信接口失败：" + e.getMessage());
        }
    }

    /**
     * 00-service调用生成客服账号和客服链接
     *
     * @param corpAccessToken 授权方企业令牌
     * @param weChatKfAccountInfoEntity 客服账号实体类
     * @return 客服账号的open_kfid
     */
    private String addKfAccount(String corpAccessToken, WeChatKfAccountInfoEntity weChatKfAccountInfoEntity) {
        // 添加客服之前先调用临时上传素材接口
        String mediaId = uploadTempMedia(corpAccessToken, weChatKfAccountInfoEntity.getHeadImageUrl());
        // 添加默认客服账号
        Map<String, Object> bodyMap = Map.of(
                "name", weChatKfAccountInfoEntity.getKfName(),
                CustomConstant.MEDIA_ID, mediaId
        );
        try {
            String jsonBody = objectMapper.writeValueAsString(bodyMap);
            HttpRequest request = HttpRequest
                    .post("https://qyapi.weixin.qq.com/cgi-bin/kf/account/add?access_token=" + corpAccessToken)
                    .body(jsonBody);
            try (HttpResponse response = request.execute()) {
                String body = response.body();
                log.info("调用微信添加客服账号接口：{}，body请求参数：{}，返回结果：{}", request.getUrl(), jsonBody, body);
                return objectMapper.readTree(body).at("/open_kfid").asText();
            }
        } catch (JsonProcessingException e) {
            throw new BusinessException(ErrorCode.JSON_PROCESSING_ERROR, e.getMessage(), e);
        }
    }

    /**
     * 默认头像上传
     * @param corpAccessToken 授权方企业token
     * @return 文件id
     */
    private String uploadTempMedia(String corpAccessToken, String headImageUrl) {
        
        try {
            File tempFile = downloadImageByUrl(headImageUrl);
            Map<String, Object> formDataMap = Map.of(
                    "name", "media",
                    "filename", tempFile
            );
            HttpRequest request = HttpRequest
                    .post("https://qyapi.weixin.qq.com/cgi-bin/media/upload?access_token=" + corpAccessToken + "&type=" + "image" + "&debug=1")
                    .header("content-Type", "multipart/form-data")
                    .form(formDataMap);
            try (HttpResponse response = request.execute()) {
                String body = response.body();
                log.info("调用微信上传临时素材接口：{}，form请求参数：{}，返回结果：{}", request.getUrl(), formDataMap, response.body());
                JsonNode jsonNode = objectMapper.readTree(body);
                if (!jsonNode.has(CustomConstant.MEDIA_ID)) {
                    String errcode = jsonNode.at("/errcode").asText();
                    if (String.valueOf(ErrorCode.WECHAT_INVALID_TOKEN.getStatus()).equals(errcode)) {
                        throw new BusinessException(ErrorCode.WECHAT_INVALID_TOKEN);
                    }
                }
                return jsonNode.at("/media_id").asText();
            }
        } catch (Exception e) {
            log.error("上传临时素材失败：{}", e.getMessage(), e);
            throw new BusinessException(ErrorCode.WECHAT_UPLOAD_TEMP_MEDIA_FAILED);
        }
    }

    /**
     * 从Url下载图片
     * @param headImageUrl 头像Url地址
     * @return 二进制文件
     */
    private File downloadImageByUrl(String headImageUrl) {
        if (headImageUrl == null || headImageUrl.trim().isEmpty()) {
            log.error("headImageUrl 为空或空字符串，无法下载图片");
            throw new IllegalArgumentException("headImageUrl 不能为 null 或空");
        }
        log.info("正在从 URL 下载图片: {}", headImageUrl);
        try {
            // 替换为推荐方式：使用 URI 创建并转换为 URL
            URI uri = URI.create(headImageUrl);
            URL url = uri.toURL();
            // 确保扩展名正确
            File tempFile = File.createTempFile("default_avatar", ".jpg");

            try (InputStream inputStream = url.openStream();
                 FileOutputStream out = new FileOutputStream(tempFile)) {
                byte[] buffer = new byte[1024];
                int bytesRead;
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    out.write(buffer, 0, bytesRead);
                }
                return tempFile;
            }
        } catch (MalformedURLException e) {
            log.error("转换 URL 时格式无效: {}", headImageUrl, e);
            throw new BusinessException(ErrorCode.DOWNLOAD_IMAGE_FAILED);
        } catch (IOException e) {
            log.error("下载图片时发生 IO 错误: {}", headImageUrl, e);
            throw new BusinessException(ErrorCode.DOWNLOAD_IMAGE_FAILED);
        } catch (Exception e) {
            log.error("下载图片时发生未知错误: {}", headImageUrl, e);
            throw new BusinessException(ErrorCode.DOWNLOAD_IMAGE_FAILED);
        }
    }

    /**
     * 将微信响应DTO转换为实体类
     * @param dto 微信响应DTO
     * @return 实体类
     */
    private WeChatCustomerCompanyInfoEntity convertDtoToEntity(WeChatGetPermanentCodeResponseDto dto) {
        WeChatCustomerCompanyInfoEntity customerCompanyInfoEntity = new WeChatCustomerCompanyInfoEntity();
        customerCompanyInfoEntity.setPermanentCode(dto.getPermanentCode());
        customerCompanyInfoEntity.setState(dto.getState());
        
        // 授权方企业信息
        Map<String, Object> authCorpInfoMap = dto.getAuthCorpInfoMap();
        if (authCorpInfoMap != null) {
            customerCompanyInfoEntity.setCorpId(String.valueOf(authCorpInfoMap.getOrDefault("corpid", "")));
            customerCompanyInfoEntity.setCorpName(String.valueOf(authCorpInfoMap.getOrDefault("corp_name", "")));
        }

        // 授权管理员的信息
        Map<String, Object> authUserInfoMap = dto.getAuthUserInfoMap();
        if (authUserInfoMap != null) {
            customerCompanyInfoEntity.setUserId(String.valueOf(authUserInfoMap.getOrDefault("userid", "")));
            customerCompanyInfoEntity.setOpenUserid(String.valueOf(authUserInfoMap.getOrDefault("open_userid", "")));
            customerCompanyInfoEntity.setName(String.valueOf(authUserInfoMap.getOrDefault("name", "")));
            customerCompanyInfoEntity.setAvatar(String.valueOf(authUserInfoMap.getOrDefault("avatar", "")));
        }

        // 推广二维码安装相关信息
        Map<String, Object> registerCodeInfoMap = dto.getRegisterCodeInfoMap();
        if (registerCodeInfoMap != null) {
            customerCompanyInfoEntity.setRegisterCode(String.valueOf(registerCodeInfoMap.getOrDefault("register_code", "")));
            customerCompanyInfoEntity.setRegisterState(String.valueOf(registerCodeInfoMap.getOrDefault("state", "")));
            customerCompanyInfoEntity.setTemplateId(String.valueOf(registerCodeInfoMap.getOrDefault("template_id", "")));
        }
        return customerCompanyInfoEntity;
    }
}
