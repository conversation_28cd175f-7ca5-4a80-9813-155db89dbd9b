package com.mkefu.common.constant;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 自定义常量类
 * <AUTHOR> 杨国锋
 * @since 2025-06-11
 */
@Data
@Schema(name = "自定义常量类", description = "CustomConstant")
public final class CustomConstant {

    /**
     * 私有构造函数，防止实例化
     */
    private CustomConstant() {
        throw new UnsupportedOperationException("CustomConstant常量类不应该实例化");
    }
    
    /**
     * 租户对象
     */
    public static final String HEADER_X_TENANTENTITY = "X-TenantEntity";

    /**
     * 时间戳秒
     */
    public static final String HEADER_X_TIMESTAMP = "X-Timestamp";

    /**
     * 签名
     */
    public static final String HEADER_X_SIGNATURE = "X-Signature";

    /**
     * 授权令牌
     */
    public static final String HEADER_AUTHORIZATION = "Authorization";

    /**
     * 接口方式 
     * self-hosted：自部署
     * official：官方
     */
    public static final String HEADER_X_APITYPE = "X-ApiType";

    /**
     * 平台类型
     * coze
     * dify 
     */
    public static final String HEADER_X_PLATFORMTYPE = "X-PlatFormType";

    /**
     * coze官方API 前缀路径
     */
    public static final String COZE_PREFIX_URL = "https://api.coze.cn/";

    /**
     * dify官方API 前缀路径
     */
    public static final String DIFY_OFFICIAL_PREFIX_URL = "https://api.dify.ai/";

    /**
     * dify本地部署API 前缀路径
     */
    public static final String DIFY_SELF_HOSTED_PREFIX_URL = "http://localhost:8080/";
    
    /**
     * 未匹配到知识库里的内容时的事件类型
     */
    public static final String SSE_EVENT_TYPE_NOT_MATCH = "question_notMatchAnswer";

    /**
     * Dify 模型限流
     */
    public static final String DIFY_TRAFFIC_LIMITING_MESSAGE = "experiencing heavy traffic and we're restricting traffic in order to maintain service quality";

    /**
     * 推送suite_ticket事件
     */
    public static final String SUITE_TICKET = "suite_ticket";

    /**
     * 授权通知事件
     */
    public static final String CREATE_AUTH = "create_auth";

    /**
     * 变更授权通知事件
     */
    public static final String CHANGE_AUTH = "change_auth";

    /**
     * 取消授权通知事件
     */
    public static final String CANCEL_AUTH = "cancel_auth";

    /**
     * 接收消息事件
     */
    public static final String KF_MSG_OR_EVENT = "kf_msg_or_event";

    /**
     * 事件类型
     */
    public static final String INFO_TYPE = "InfoType";

    /**
     * 数据回调
     */
    public static final String DATA_CALLBACK = "data_callback";

    /**
     * 指令回调
     */
    public static final String COMMAND_CALLBACK = "command_callback";

    /**
     * 双正斜杠
     */
    public static final String DOUBLE_SLASH = "//";

    /**
     * 文本消息类型
     */
    public static final String MESSAGE_TYPE_TEXT = "text";

    /**
     * 图片消息类型
     */
    public static final String MESSAGE_TYPE_IMAGE = "image";

    /**
     * 语音消息类型
     */
    public static final String MESSAGE_TYPE_VOICE = "voice";

    /**
     * 视频消息类型
     */
    public static final String MESSAGE_TYPE_VIDEO = "video";

    /**
     * 文件消息类型
     */
    public static final String MESSAGE_TYPE_FILE = "file";

    /**
     * 位置消息类型
     */
    public static final String MESSAGE_TYPE_LOCATION = "location";

    /**
     * 链接消息类型
     */
    public static final String MESSAGE_TYPE_LINK = "link";

    /**
     * 事件消息类型
     */
    public static final String MESSAGE_TYPE_EVENT = "event";

    /**
     * 名片消息类型
     */
    public static final String MESSAGE_TYPE_BUSINESS_CARD = "business_card";

    /**
     * 小程序消息类型
     */
    public static final String MESSAGE_TYPE_MINIPROGRAM = "miniprogram";

    /**
     * 菜单消息类型
     */
    public static final String MESSAGE_TYPE_MSG_MENU = "msgmenu";

    /**
     * 视频号商品消息类型
     */
    public static final String MESSAGE_TYPE_CHANNELS_SHOP_PRODUCT = "channels_shop_product";

    /**
     * 视频号订单消息类型
     */
    public static final String MESSAGE_TYPE_CHANNELS_SHOP_ORDER = "channels_shop_order";
    
    /**
     * 聊天记录消息类型
     */
    public static final String MESSAGE_TYPE_MERGED_MSG = "merged_msg";

    /**
     * 视频号消息类型
     */
    public static final String MESSAGE_TYPE_CHANNELS = "channels";

    /**
     * 会议消息类型
     */
    public static final String MESSAGE_TYPE_MEETING = "meeting";

    /**
     * 日历消息类型
     */
    public static final String MESSAGE_TYPE_CALENDAR = "calendar";

    /**
     * 笔记消息类型
     */
    public static final String MESSAGE_TYPE_NOTE = "note";

    /**
     * 素材Id
     */
    public static final String MEDIA_ID = "media_id";
    
    /**
     * 错误码
     */
    public static final String ERROR_CODE = "errcode";

    /**
     * 进入会话事件
     */
    public static final String ENTER_SESSION = "enter_session";

    /**
     * 转发消息给坐席端
     */
    public static final String SEND_TO_AGENT = "send_to_agent";

    /**
     * 授权绑定成功通知
     */
    public static final String NOTICE_BIND_SUCCESS = "notice_bind_success";
}
