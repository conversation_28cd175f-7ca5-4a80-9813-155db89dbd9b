package com.mkefu.chatflow.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR> 杨国锋
 * @since 2025-05-29
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "知识库信息实体类。")
public class CozeKnowledgeInfosEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "知识库 ID。")
    private String id;

    @Schema(description = "知识库名称")
    private String name;

}
