package com.mkefu.common.interceptor;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.mkefu.common.constant.BaseCode;
import com.mkefu.common.constant.ErrorCode;
import com.mkefu.common.exception.BusinessException;
import com.mkefu.common.util.RedisUtil;
import com.mkefu.system.entity.TenantAuthInfoEntity;
import com.mkefu.system.entity.query.TenantAuthInfoQuery;
import com.mkefu.system.mapper.TenantAuthInfoMapper;
import com.mkefu.system.service.TenantAuthInfoService;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import java.nio.charset.StandardCharsets;
import java.util.concurrent.TimeUnit;


/**
 * 租户授权码拦截器
 * <AUTHOR> Yang 杨国锋
 * @since 2025-05-22
 */
@Slf4j
@Tag(name = "租户授权码拦截器")
@Component
public class TenantAuthCodeInterceptor implements HandlerInterceptor {

    private final RedisUtil redisUtil;

    private final TenantAuthInfoMapper tenantAuthInfoMapper;

    private final ObjectMapper objectMapper;

    private final TenantAuthInfoService tenantAuthInfoService;

    public TenantAuthCodeInterceptor(RedisUtil redisUtil, TenantAuthInfoMapper tenantAuthInfoMapper, ObjectMapper objectMapper, TenantAuthInfoService tenantAuthInfoService) {
        this.redisUtil = redisUtil;
        this.tenantAuthInfoMapper = tenantAuthInfoMapper;
        this.objectMapper = objectMapper;
        this.tenantAuthInfoService = tenantAuthInfoService;
    }


    /**
     * 拦截请求，验证租户授权码
     * @param request 请求
     * @param response 响应
     * @param handler 处理器
     * @return 是否继续处理请求
     */
    @Override
    public boolean preHandle(@NotNull HttpServletRequest request,
                             @NotNull HttpServletResponse response,
                             @NotNull Object handler) {
        try {
            String headerValue = request.getHeader("X-TenantEntity");
            byte[] isoBytes = headerValue.getBytes(StandardCharsets.ISO_8859_1);
            String reDecodedFromIsoToUtf8 = new String(isoBytes, StandardCharsets.UTF_8);
            // 解析租户信息
            TenantAuthInfoEntity headerEntity = objectMapper.readValue(reDecodedFromIsoToUtf8, TenantAuthInfoEntity.class);
            int maxLength = 128;
            if (StringUtils.isNotBlank(headerEntity.getAgentId()) && headerEntity.getAgentId().length() > maxLength) {
                throw new BusinessException(ErrorCode.FIELD_TOO_LONG, "agentId字段超出最大长度128");
            }
            TenantAuthInfoEntity countEntity = tenantAuthInfoMapper.selectOne(new TenantAuthInfoQuery().init(headerEntity).toQueryWrapper());
            // 如果未对此租户下的智能体授权就调用新增
            if (ObjectUtils.isEmpty(countEntity)) {
                tenantAuthInfoService.generateTenantAuthCode(headerEntity);
            } else {
                String key = countEntity.getTenantId() + countEntity.getAgentId() + countEntity.getAgentName() + countEntity.getTenantName();
                Object redisObject = redisUtil.getAuthCode(key);
                if (ObjectUtils.isEmpty(redisObject)) {
                    redisUtil.putAuthCode(key, countEntity, 730, TimeUnit.DAYS);
                    log.info("当前Key={}, 已重新在Redis中缓存", BaseCode.TENANT_AUTH_INFO_PREFIX.getCode() + key);
                }
            }
            return true;
        } catch (Exception e) {
            log.error("租户授权码验证失败", e);
            throw new BusinessException(ErrorCode.TENANT_AUTH_FAILED, ErrorCode.TENANT_AUTH_FAILED.getDefaultMessage());
        }
    }
}
