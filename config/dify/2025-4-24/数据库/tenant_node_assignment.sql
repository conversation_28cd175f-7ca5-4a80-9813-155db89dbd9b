CREATE TABLE "public"."tenant_node_assignment"
(
    "tenant_id"   varchar(255) COLLATE "pg_catalog"."default",
    "instance_id" varchar(500) COLLATE "pg_catalog"."default" NOT NULL,
    "assigned_at" timestamp(6),
    CONSTRAINT "tenant_node_assignment_pkey" PRIMARY KEY ("instance_id")
)
;

ALTER TABLE "public"."tenant_node_assignment"
    OWNER TO "postgres";

COMMENT
ON COLUMN "public"."tenant_node_assignment"."tenant_id" IS '租户唯一标识';

COMMENT
ON COLUMN "public"."tenant_node_assignment"."instance_id" IS '节点实例 ID';

COMMENT
ON COLUMN "public"."tenant_node_assignment"."assigned_at" IS '分配时间';

COMMENT
ON TABLE "public"."tenant_node_assignment" IS '租户与节点的分配关表';
