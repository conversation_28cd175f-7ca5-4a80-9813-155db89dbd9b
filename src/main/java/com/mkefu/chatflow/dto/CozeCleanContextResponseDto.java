package com.mkefu.chatflow.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.Map;

/**
 * <AUTHOR> Yang 杨国锋
 * @since 2025-05-09
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "清除上下文响应实体类", description = "CozeCleanContextResponseDto")
public class CozeCleanContextResponseDto implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "调用状态码。0 表示调用成功，其他值表示调用失败，你可以通过 msg 字段判断详细的错误原因。")
    private Long  code;

    @Schema(description = "状态信息。API 调用失败时可通过此字段查看详细错误信息。")
    private String msg;

    @Schema(description = "上下文段落（section ）的详细信息。" +
            "Section 是一个独立的上下文段落，用于分隔不同的对话阶段或主题。Section 中包括上下文消息，当用户清除上下文时，系统会创建一个新的 Section，从而确保新的对话不受历史消息的影响。")
    private Section data;

    @Schema(description = "返回的详情信息。")
    private Map<String, Object> detail;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "上下文段落（section ）的详细信息。")
    public static class Section implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        @Schema(description = "Session ID，即清除上下文后新创建的上下文段落（section）的唯一标识符。" +
                "每个上下文段落对应一批独立的上下文消息。每次清除上下文时，系统会新建一个上下文段落用于存储新的上下文消息。")
        private String id;

        @Schema(description = "Conversation ID，即会话的唯一标识。")
        private String conversationId;
    }
}
