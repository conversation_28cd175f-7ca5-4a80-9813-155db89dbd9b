package com.mkefu.chatflow.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR> 杨国锋
 * @since 2025-04-29
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(name = "Dify获取下一轮建议问题列表响应DTO", description = "DifySuggestedResponseDto")
public class DifySuggestedResponseDto implements Serializable {

    @Schema(description = "结果")
    private String result;

    @Schema(description = "建议列表")
    private List<Object> data;

}
