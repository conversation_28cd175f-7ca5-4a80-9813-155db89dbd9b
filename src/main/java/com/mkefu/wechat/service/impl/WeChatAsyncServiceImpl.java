package com.mkefu.wechat.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.mkefu.common.annotation.LogInteraction;
import com.mkefu.common.constant.ErrorCode;
import com.mkefu.common.exception.BusinessException;
import com.mkefu.wechat.dto.WeChatMessageToImServiceRequestDto;
import com.mkefu.wechat.entity.WeChatAsyncMessageInfoEntity;
import java.util.HashMap;
import java.util.Map;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mkefu.common.constant.BaseCode;
import com.mkefu.common.constant.CustomConstant;
import com.mkefu.common.util.RedisUtil;
import com.mkefu.common.util.XmlUtils;
import com.mkefu.wechat.dto.WeChatAuthInfoResponseDto;
import com.mkefu.wechat.dto.WeChatSyncMsgResponseDto;
import com.mkefu.wechat.entity.*;
import com.mkefu.wechat.mapper.*;
import com.mkefu.wechat.service.WeChatAsyncService;
import com.mkefu.wechat.service.WeChatThirdPartyAuthInfoService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import com.mkefu.wechat.service.handler.MessageHandler;
import com.mkefu.wechat.service.handler.WeChatMessageHandlerFactory;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 微信异步处理服务实现类
 * <AUTHOR> Yang 杨国锋
 * @since 2025-06-20
 */
@Slf4j
@Service
public class WeChatAsyncServiceImpl implements WeChatAsyncService {

    private final XmlUtils xmlUtils;
    
    private final ObjectMapper objectMapper;
    
    private final RedisUtil redisUtil;
    
    private final WeChatThirdPartyAuthInfoMapper weChatThirdPartyAuthInfoMapper;

    private final WeChatThirdPartyBaseInfoMapper weChatThirdPartyBaseInfoMapper;
    
    private final WeChatThirdPartyAuthInfoService weChatThirdPartyAuthInfoService;
    
    private final WeChatCustomerCompanyInfoMapper weChatCustomerCompanyInfoMapper;
    
    private final WeChatThirdPartyAuthInfoServiceImpl weChatThirdPartyAuthInfoServiceImpl;
    
    private final WeChatKfAccountInfoMapper weChatKfAccountInfoMapper;

    private final WeChatMessageHandlerFactory messageHandlerFactory;

    public WeChatAsyncServiceImpl(XmlUtils xmlUtils, ObjectMapper objectMapper, RedisUtil redisUtil,
                                  WeChatThirdPartyAuthInfoMapper weChatThirdPartyAuthInfoMapper, WeChatThirdPartyBaseInfoMapper weChatThirdPartyBaseInfoMapper,
                                  WeChatThirdPartyAuthInfoService weChatThirdPartyAuthInfoService,
                                  WeChatCustomerCompanyInfoMapper weChatCustomerCompanyInfoMapper,
                                  WeChatThirdPartyAuthInfoServiceImpl weChatThirdPartyAuthInfoServiceImpl,
                                  WeChatKfAccountInfoMapper weChatKfAccountInfoMapper,
                                  WeChatMessageHandlerFactory messageHandlerFactory) {
        this.xmlUtils = xmlUtils;
        this.objectMapper = objectMapper;
        this.redisUtil = redisUtil;
        this.weChatThirdPartyAuthInfoMapper = weChatThirdPartyAuthInfoMapper;
        this.weChatThirdPartyBaseInfoMapper = weChatThirdPartyBaseInfoMapper;
        this.weChatThirdPartyAuthInfoService = weChatThirdPartyAuthInfoService;
        this.weChatCustomerCompanyInfoMapper = weChatCustomerCompanyInfoMapper;
        this.weChatThirdPartyAuthInfoServiceImpl = weChatThirdPartyAuthInfoServiceImpl;
        this.weChatKfAccountInfoMapper = weChatKfAccountInfoMapper;
        this.messageHandlerFactory = messageHandlerFactory;
    }


    /**
     * 异步处理infoType事件类型
     *
     * @param decryptedXml   解密后的XML数据
     * @param toUserName     服务商应用Id
     * @param platFormId     平台Id
     * @param baseInfoEntity 服务商基础信息
     */
    @Override
    @Async("weChatExecutor")
    public void handleWeChatEventAsync(String decryptedXml, String toUserName, String platFormId, WeChatThirdPartyBaseInfoEntity baseInfoEntity) {
        String infoType = "";
        String event = "";
        try {
            log.info("开始异步处理infoType事件，platFormId：{}，toUserName: {}", platFormId, toUserName);
            infoType = xmlUtils.getXmlInfoTypeValue(decryptedXml);
            if (StringUtils.isNotBlank(infoType)) {
                // 处理授权事件
                handleInfoType(decryptedXml, infoType, platFormId, baseInfoEntity);
                log.info("异步处理infoType事件完成，platFormId：{}，toUserName: {}", platFormId, toUserName);
                return;
            }
            // 处理业务事件
            Map<String, String> xmlValuesMap = xmlUtils.getXmlValues(decryptedXml, "Event");
            event = xmlValuesMap.get("Event");
            handleEventType(decryptedXml, event, toUserName, platFormId);
        } catch (Exception e) {
            if (StringUtils.isNotBlank(infoType)) {
                log.error("异步处理授权事件失败，infoType={}，platFormId：{}，toUserName: {}, error: {}", infoType, platFormId, toUserName, e.getMessage(), e);
            }
            if (StringUtils.isNotBlank(event)) {
                log.error("异步处理业务事件失败，event={}，platFormId：{}，toUserName: {}, error: {}", event, platFormId, toUserName, e.getMessage(), e);
            }
        }
    }

    /**
     * 处理业务事件
     *
     * @param decryptedXml 解密后的XML数据
     * @param event        事件类型
     * @param toUserName   授权方企业Id
     * @param platFormId   平台Id
     */
    @LogInteraction(description = "异步任务-处理业务事件")
    private void handleEventType(String decryptedXml, String event, String toUserName, String platFormId) {
        if (event.equals(CustomConstant.KF_MSG_OR_EVENT)) {
            WeChatCustomerCompanyInfoEntity customerCompanyInfoEntity = weChatCustomerCompanyInfoMapper.selectOne(new QueryWrapper<WeChatCustomerCompanyInfoEntity>()
                    .eq("plat_form_id", platFormId)
                    .eq("corp_id", toUserName)
                    .eq("del_flag", BaseCode.ONE)
            );
            WeChatThirdPartyBaseInfoEntity baseInfoEntity = weChatThirdPartyBaseInfoMapper.selectOne(new QueryWrapper<WeChatThirdPartyBaseInfoEntity>()
                    .eq("plat_form_id", platFormId)
            );
            Object redisByReceiveIdObj = redisUtil.getByReceiveId(baseInfoEntity.getReceiveId());
            if (ObjectUtils.isNotEmpty(redisByReceiveIdObj)) {
                WeChatThirdPartyAuthInfoEntity authInfoEntity = objectMapper.convertValue(redisByReceiveIdObj, WeChatThirdPartyAuthInfoEntity.class);
                String corpAccessToken = weChatThirdPartyAuthInfoServiceImpl.getCorpAccessToken(customerCompanyInfoEntity.getPermanentCode(), customerCompanyInfoEntity.getCorpId(), authInfoEntity.getSuiteAccessToken());
                handlerKfMsgOrEventType(decryptedXml, toUserName, corpAccessToken, platFormId, customerCompanyInfoEntity);
            }
        }
    }

    /**
     * 处理业务事件
     *
     * @param decryptedXml              解密后的XML数据
     * @param toUserName                授权方企业Id
     * @param corpAccessToken           授权方企业令牌
     * @param platFormId                平台Id
     * @param customerCompanyInfoEntity 授权方企业信息
     */
    private void handlerKfMsgOrEventType(String decryptedXml, String toUserName, String corpAccessToken, String platFormId, WeChatCustomerCompanyInfoEntity customerCompanyInfoEntity) {
        Map<String, String> xmlValuesMap = xmlUtils.getXmlValues(decryptedXml, "Token", "OpenKfId", "CreateTime");
        // 调用读取消息接口
        getWeChatSyncMsg(xmlValuesMap, corpAccessToken, toUserName, platFormId, customerCompanyInfoEntity);
    }

    /**
     * 读取消息
     *
     * @param xmlValuesMap              解析XML得到的参数Map
     * @param corpAccessToken           授权方企业令牌
     * @param toUserName                授权方企业Id
     * @param platFormId                平头Id
     * @param customerCompanyInfoEntity 授权方企业信息
     */
    private void getWeChatSyncMsg(Map<String, String> xmlValuesMap, String corpAccessToken, String toUserName, String platFormId, WeChatCustomerCompanyInfoEntity customerCompanyInfoEntity) {
        try {
            String openKfId = xmlValuesMap.get("OpenKfId");
            Object redisNextCursorByOpenKfIdObj = redisUtil.getNextCursorByOpenKfId(openKfId);
            String nextCursor = "";
            if (ObjectUtils.isNotEmpty(redisNextCursorByOpenKfIdObj)) {
                nextCursor = objectMapper.convertValue(redisNextCursorByOpenKfIdObj, WeChatAsyncMessageInfoEntity.class).getNextCursor();
            }
            Map<String, Object> params = Map.of(
                    "open_kfid", openKfId,
                    "cursor", nextCursor
            );
            String jsonBody = objectMapper.writeValueAsString(params);
            HttpRequest request = HttpRequest
                    .post("https://qyapi.weixin.qq.com/cgi-bin/kf/sync_msg?access_token=" + corpAccessToken)
                    .body(jsonBody);
            try (HttpResponse response = request.execute()) {
                String body = response.body();
                JsonNode jsonNode = objectMapper.readTree(body);
                if (jsonNode.has(CustomConstant.ERROR_CODE)) {
                    String errcode = jsonNode.at("/errcode").asText();
                    if (String.valueOf(ErrorCode.WECHAT_IP_NOT_ALLOW.getStatus()).equals(errcode)) {
                        throw new BusinessException(ErrorCode.WECHAT_IP_NOT_ALLOW);
                    } else if (String.valueOf(ErrorCode.WECHAT_INVALID_TOKEN.getStatus()).equals(errcode)) {
                        throw new BusinessException(ErrorCode.WECHAT_INVALID_TOKEN);
                    }
                }
                // 拿到所有类型的消息
                WeChatSyncMsgResponseDto weChatSyncMsgResponseDto = objectMapper.readValue(body, WeChatSyncMsgResponseDto.class);
                // NextCursor存储到表里，方便下次增量获取
                List<WeChatAsyncMessageInfoEntity> weChatAsyncMessageInfoList = weChatSyncMsgResponseDto.getWeChatAsyncMessageInfoList();
                String tenantId = customerCompanyInfoEntity.getTenantId();
                
                // 处理不同类型的微信消息
                WeChatAsyncMessageInfoEntity weChatAsyncMessageInfoEntity = 
                        handlerAsyncMessage(weChatSyncMsgResponseDto, weChatAsyncMessageInfoList, 
                        toUserName, tenantId, openKfId, platFormId);
                redisUtil.putNextCursorByOpenKfId(weChatAsyncMessageInfoEntity.getOpenKfId(), weChatAsyncMessageInfoEntity, 2, TimeUnit.HOURS);
                // 获取微信客服聊天链接
                Object redisByOpenKfIdObj;
                WeChatKfAccountInfoEntity weChatKfAccountInfoEntity = null;
                if (StringUtils.isNotBlank(weChatAsyncMessageInfoEntity.getPlatFormType())) {
                    weChatKfAccountInfoEntity = weChatKfAccountInfoMapper
                            .selectOne(new QueryWrapper<WeChatKfAccountInfoEntity>()
                                    .lambda()
                                    .eq(WeChatKfAccountInfoEntity::getOpenKfId, openKfId)
                                    .eq(WeChatKfAccountInfoEntity::getPlatFormType, weChatAsyncMessageInfoEntity.getPlatFormType())
                            );
                    redisUtil.putByOpenKfId(openKfId, weChatKfAccountInfoEntity, 30, TimeUnit.MINUTES);
                } else {
                    redisByOpenKfIdObj = redisUtil.getByOpenKfId(openKfId);
                    if (ObjectUtils.isNotEmpty(redisByOpenKfIdObj)) {
                        weChatKfAccountInfoEntity = objectMapper.convertValue(redisByOpenKfIdObj, WeChatKfAccountInfoEntity.class);
                        weChatAsyncMessageInfoEntity.setPlatFormType(weChatKfAccountInfoEntity.getPlatFormType());
                    }
                }
                // 调用mkefu-00-service 
                sendRequestTo00Service(corpAccessToken, weChatAsyncMessageInfoEntity, weChatKfAccountInfoEntity);
            }
        } catch (Exception e) {
            log.error("处理同步消息失败", e);
        }
        
    }

    /**
     * 处理不同类型的微信消息
     *
     * @param weChatSyncMsgResponseDto 同步消息响应结果
     * @param weChatAsyncMessageInfoList 消息列表
     * @param toUserName 授权方企业Id
     * @param tenantId 租户Id
    */
    private WeChatAsyncMessageInfoEntity handlerAsyncMessage(WeChatSyncMsgResponseDto weChatSyncMsgResponseDto, 
                                                             List<WeChatAsyncMessageInfoEntity> weChatAsyncMessageInfoList, 
                                                             String toUserName, String tenantId, String openKfId, String platFormId) {
        String externalUserId = "";
        String msgType = "";
        String msgId = "";
        String msgContent = "";
        String platFormType = "";
        Map<String, Object> map;

        // 获取事件消息
        Map<String, WeChatAsyncMessageInfoEntity> groupByMsgTypeMap = weChatAsyncMessageInfoList
                .stream()
                .collect(Collectors.toMap(WeChatAsyncMessageInfoEntity::getMsgType, Function.identity(), (oldValue, newValue) -> newValue));
        WeChatAsyncMessageInfoEntity eventEntity = groupByMsgTypeMap.getOrDefault("event", null);
        if (ObjectUtils.isNotEmpty(eventEntity)) {
            msgType = eventEntity.getMsgType();
            msgId = eventEntity.getMsgId();
            msgContent = eventEntity.getMsgContent();
            try {
                map = objectMapper.readValue(msgContent, Map.class);
                String eventType = map.get("event_type").toString();
                if (CustomConstant.ENTER_SESSION.equals(eventType)) {
                    platFormType = map.get("scene").toString();
                }
                externalUserId = map.get("external_userid").toString();
            } catch (JsonProcessingException e) {
                log.error("解析事件消息失败", e);
            }
        }
        for (WeChatAsyncMessageInfoEntity entity : groupByMsgTypeMap.values()) {
            if ("event".equals(entity.getMsgType())) {
                continue;
            }
            msgType = entity.getMsgType();
            msgId = entity.getMsgId();
            msgContent = entity.getMsgContent();
            externalUserId = entity.getExternalUserId();
        }
        // 更新或者新增表
        WeChatAsyncMessageInfoEntity weChatAsyncMessageInfoEntity = new WeChatAsyncMessageInfoEntity();
        weChatAsyncMessageInfoEntity.setTenantId(tenantId);
        weChatAsyncMessageInfoEntity.setPlatFormId(platFormId);
        weChatAsyncMessageInfoEntity.setCorpId(toUserName);
        weChatAsyncMessageInfoEntity.setNextCursor(weChatSyncMsgResponseDto.getNextCursor());
        weChatAsyncMessageInfoEntity.setOpenKfId(openKfId);
        weChatAsyncMessageInfoEntity.setExternalUserId(externalUserId);
        weChatAsyncMessageInfoEntity.setMsgType(msgType);
        weChatAsyncMessageInfoEntity.setPlatFormType(platFormType);
        weChatAsyncMessageInfoEntity.setMsgId(msgId);
        weChatAsyncMessageInfoEntity.setMsgContent(msgContent);
        return weChatAsyncMessageInfoEntity;
    }

    /**
     * 调用mkefu-00-service分配坐席
     *
     * @param corpAccessToken              授权方企业令牌
     * @param weChatAsyncMessageInfoEntity 异步消息信息
     * @param weChatKfAccountInfoEntity    微信客服信息
     */
    private void sendRequestTo00Service(String corpAccessToken, WeChatAsyncMessageInfoEntity weChatAsyncMessageInfoEntity, WeChatKfAccountInfoEntity weChatKfAccountInfoEntity) {
        WeChatMessageToImServiceRequestDto dto = new WeChatMessageToImServiceRequestDto();
        dto.setTenantId(weChatAsyncMessageInfoEntity.getTenantId());
        dto.setOpenKfId(weChatAsyncMessageInfoEntity.getOpenKfId());
        dto.setExternalUserId(weChatAsyncMessageInfoEntity.getExternalUserId());
        dto.setPlatFormType(weChatAsyncMessageInfoEntity.getPlatFormType());
        dto.setCorpAccessToken(corpAccessToken);
        dto.setKfAccountUrl(ObjectUtils.isNotEmpty(weChatKfAccountInfoEntity) ? weChatKfAccountInfoEntity.getKfAccountUrl() : "");
        dto.setMsgId(weChatAsyncMessageInfoEntity.getMsgId());
        dto.setMsgContent(weChatAsyncMessageInfoEntity.getMsgContent());
        dto.setMsgType(weChatAsyncMessageInfoEntity.getMsgType());
        dto.setCorpId(weChatAsyncMessageInfoEntity.getCorpId());
        // 使用工厂+策略模式获取微信对应消息类型的消息处理器并处理消息
        MessageHandler handler = messageHandlerFactory.getHandler(weChatAsyncMessageInfoEntity.getMsgType());
        handler.handle(dto, CustomConstant.SEND_TO_AGENT);
    }
    

    /**
     * 处理授权事件
     *
     * @param decryptedXml   解密后的XML数据
     * @param infoType       infoType事件类型
     * @param platFormId     平台Id
     * @param baseInfoEntity 服务商基础信息
     */
    @LogInteraction(description = "异步任务-处理授权事件")
    private void handleInfoType(String decryptedXml, String infoType, String platFormId, WeChatThirdPartyBaseInfoEntity baseInfoEntity) {
        // 解析XML拿到infoType事件类型
        log.info("开始处理InfoType={}的事件", infoType);
        if (StringUtils.isNotBlank(infoType)) {
            switch (infoType) {
                
                case CustomConstant.SUITE_TICKET:
                    // 处理推送suite_ticket事件
                    handleSuiteTicketInfoType(decryptedXml, platFormId, baseInfoEntity);
                    break;
                    
                case CustomConstant.CREATE_AUTH:
                    // 授权通知事件
                    handlerCreateAuthInfoType(decryptedXml, platFormId, baseInfoEntity);
                    break;
                    
                case CustomConstant.CHANGE_AUTH:
                    // 授权变更通知事件
                    handlerChangeAuthInfoType(decryptedXml, platFormId);
                    break;
                    
                case CustomConstant.CANCEL_AUTH:
                    // 取消授权通知事件
                    handlerCancelAuthInfoType(decryptedXml, platFormId);
                    break;
                    
                default:
                    log.warn("未知的infoType事件类型: {}", infoType);
            }
        }
    }

    /**
     * 处理取消授权通知事件
     *
     * @param decryptedXml 解密后的XML数据
     * @param platFormId   平台Id
     */
    private void handlerCancelAuthInfoType(String decryptedXml, String platFormId) {
        Map<String, String> xmlValuesMap = xmlUtils.getXmlValues(decryptedXml, "AuthCorpId");
        String corpId = xmlValuesMap.get("AuthCorpId");
        // 删除已经存储的被服务的企业的信息
        log.info("当前企业：{}已取消对服务商平台Id：{}的授权", corpId, platFormId);
        weChatCustomerCompanyInfoMapper.update(new UpdateWrapper<WeChatCustomerCompanyInfoEntity>()
                .eq("corp_id", corpId)
                .eq("plat_form_id", platFormId)
                .set("del_flag", BaseCode.ZERO.getCode())
        );
        redisUtil.removeCorpAccessToken(corpId);
    }

    /**
     * 处理授权变更通知事件
     *
     * @param decryptedXml 解密后的XML数据
     * @param platFormId   平台Id
     */
    private void handlerChangeAuthInfoType(String decryptedXml, String platFormId) {
        Map<String, String> xmlValuesMap = xmlUtils.getXmlValues(decryptedXml, "AuthCode", "State", "AuthCorpId", "SuiteId");
        String authCorpId = xmlValuesMap.get("AuthCorpId");
        String suiteId = xmlValuesMap.get("SuiteId");

        WeChatThirdPartyAuthInfoEntity existsEntity = weChatThirdPartyAuthInfoMapper
                .selectOne(new QueryWrapper<WeChatThirdPartyAuthInfoEntity>()
                        .eq("plat_form_id", platFormId)
                        .eq("suite_id", suiteId)
                );
        String suiteAccessToken = "";
        String permanentCode = "";
        if (ObjectUtils.isNotEmpty(existsEntity)) {
            suiteAccessToken = existsEntity.getSuiteAccessToken();
            permanentCode = existsEntity.getPermanentCode();
        }
        
        // 获取企业授权信息
        WeChatAuthInfoResponseDto weChatAuthInfoResponseDto = getWeChatAuthInfo(suiteAccessToken, permanentCode, authCorpId, suiteId);
        if (ObjectUtils.isEmpty(weChatAuthInfoResponseDto)) {
            log.warn("获取企业授权信息失败，平台Id：{}，服务商应用Id: {}, authCorpId: {}", platFormId, suiteId, authCorpId);
        }
        // 获取企业凭证并存到Redis
        getWeChatCorpToken(suiteAccessToken, permanentCode, authCorpId, suiteId);
        // 从Redis获取刚存进去的数据
        Map<String, Object> redisPermanentMap = redisUtil.getByWeChatCorpToken(permanentCode);
        String accessToken = redisPermanentMap.get("access_token").toString();
        // 获取应用权限详情
        Map<String, Object> weChatPermissionsMap = getWeChatPermissionsInfo(accessToken);
        log.info("授权变更响应参数：{}", weChatPermissionsMap);
    }
    
    /**
     * 获取应用权限详情
     * @param accessToken 凭证
     * @return 应用权限详情
     */
    private Map<String, Object> getWeChatPermissionsInfo(String accessToken) {
        try {
            HttpRequest request = HttpRequest
                    .post("https://qyapi.weixin.qq.com/cgi-bin/agent/get_permissions?access_token=" + accessToken);
            try (HttpResponse response = request.execute()) {
                log.info("获取应用权限详情：{}", request.getUrl());
                String responseBody = response.body();
                log.info("获取应用权限详情响应结果：{}", responseBody);
                return objectMapper.readValue(responseBody, new TypeReference<>(){});
            }
        } catch (Exception e) {
            log.error("获取应用权限详情失败，accessToken: {}, error: {}", accessToken, e.getMessage(), e);
            return HashMap.newHashMap(1);
        }
    }


    /**
     * 获取企业凭证
     * @param suiteAccessToken  套件凭证
     * @param permanentCode     永久授权码
     * @param authCorpId        授权方corpid
     * @param agentId           服务商应用Id
     */
    private void getWeChatCorpToken(String suiteAccessToken, String permanentCode, String authCorpId, String agentId) {
        try {
            Map<String, Object> requestMap = Map.of(
                    "auth_corpid", authCorpId,
                    "permanent_code", permanentCode
            );
            HttpRequest request = HttpRequest
                    .post("https://qyapi.weixin.qq.com/cgi-bin/service/get_corp_token?suite_access_token=" + suiteAccessToken)
                    .body(objectMapper.writeValueAsString(requestMap));
            try (HttpResponse response = request.execute()) {
                log.info("获取企业凭证：{}，请求参数：{}", request.getUrl(), objectMapper.writeValueAsString(requestMap));
                String responseBody = response.body();
                log.info("获取企业凭证响应结果：{}", responseBody);
                Map<String, Object> respMap = objectMapper.readValue(responseBody, new TypeReference<>(){});
                // 获取到的企业凭证存入Redis
                redisUtil.putByWeChatCorpToken(permanentCode, respMap, 7200, TimeUnit.SECONDS);
            }
        } catch (Exception e) {
            log.error("获取企业凭证失败，服务商应用Id: {}, authCorpId: {}, error: {}", agentId, authCorpId, e.getMessage(), e);
        }
    }


    /**
     * 获取企业授权信息
     * @param suiteAccessToken  套件凭证
     * @param permanentCode     永久授权码
     * @param authCorpId        授权方corpid
     * @param agentId           服务商应用Id
     * @return 授权信息
     */
    private WeChatAuthInfoResponseDto getWeChatAuthInfo(String suiteAccessToken, String permanentCode, String authCorpId, String agentId) {
        try {
            Map<String, Object> requestMap = Map.of(
                    "auth_corpid", authCorpId,
                    "permanent_code", permanentCode
            );
            HttpRequest request = HttpRequest
                    .post("https://qyapi.weixin.qq.com/cgi-bin/service/get_auth_info?suite_access_token=" + suiteAccessToken)
                    .body(objectMapper.writeValueAsString(requestMap));
            try (HttpResponse response = request.execute()) {
                log.info("调用微信获取企业授权信息：{}，body入参：{}", request.getUrl(), objectMapper.writeValueAsString(requestMap));
                String responseBody = response.body();
                log.info("调用微信获取企业授权信息响应内容：{}", responseBody);
                return objectMapper.readValue(responseBody, WeChatAuthInfoResponseDto.class);
            }
        } catch (Exception e) {
            log.error("获取企业授权信息失败，服务商应用Id: {}, error: {}", agentId, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 处理create_auth授权通知事件
     *
     * @param decryptedXml   解密后的XML数据
     * @param platFormId     平台Id
     * @param baseInfoEntity 服务商基础信息
     */
    private void handlerCreateAuthInfoType(String decryptedXml, String platFormId, WeChatThirdPartyBaseInfoEntity baseInfoEntity) {
        Map<String, String> xmlValuesMap = xmlUtils.getXmlValues(decryptedXml, "AuthCode", "State", "SuiteId");
        String suiteId = xmlValuesMap.get("SuiteId");

        Object redisByReceiveIdObj = redisUtil.getByReceiveId(baseInfoEntity.getReceiveId());
        if (ObjectUtils.isEmpty(redisByReceiveIdObj)) {
            log.error("Redis没有缓存的服务商数据，platFormId：{}，suiteId: {}，receiveId：{}", platFormId, suiteId, baseInfoEntity.getReceiveId());
            return;
        }
        try {
            WeChatThirdPartyAuthInfoEntity authInfoEntity = objectMapper.readValue(objectMapper.writeValueAsString(redisByReceiveIdObj), WeChatThirdPartyAuthInfoEntity.class);
            // 获取临时授权码
            String temporaryAuthCode = xmlValuesMap.getOrDefault("AuthCode", "");
            // 获取构造链接时生成的uuid
            String state = xmlValuesMap.getOrDefault("State", "");
            if (temporaryAuthCode.isEmpty()) {
                log.error("临时授权码为空，suiteId={}", suiteId);
                return;
            }
            // 调用获取企业永久授权码接口
            weChatThirdPartyAuthInfoService.getPermanentCode(authInfoEntity.getSuiteAccessToken(), temporaryAuthCode, suiteId, platFormId, baseInfoEntity, state);
            log.info("成功获取企业永久授权码，platFormId：{}，suiteId: {}", platFormId, suiteId);
        } catch (Exception e) {
            log.error("获取企业永久授权码失败，platFormId：{}，suiteId: {}", platFormId, suiteId, e);
        }
    }
    

    /**
     * 处理推送suite_ticket事件
     *
     * @param decryptedXml   解密后的XML数据
     * @param platFormId     平台Id
     * @param baseInfoEntity 服务商基础信息
     */
    private void handleSuiteTicketInfoType(String decryptedXml, String platFormId, WeChatThirdPartyBaseInfoEntity baseInfoEntity) {
        // 解析XML拿到对应数据
        Map<String, String> xmlValuesMap = xmlUtils.getXmlValues(decryptedXml, "SuiteId", "SuiteTicket");
        String suiteTicket = "";
        String suiteId = "";
        if (!xmlValuesMap.isEmpty()) {
            suiteTicket = xmlValuesMap.get("SuiteTicket");
            suiteId = xmlValuesMap.get("SuiteId");
        }
        WeChatThirdPartyAuthInfoEntity wechatThirdPartyAuthInfoEntity = new WeChatThirdPartyAuthInfoEntity();
        wechatThirdPartyAuthInfoEntity.setPlatFormId(platFormId);
        wechatThirdPartyAuthInfoEntity.setSuiteId(suiteId);
        wechatThirdPartyAuthInfoEntity.setSuiteSecret(baseInfoEntity.getSuiteSecret());
        wechatThirdPartyAuthInfoEntity.setSuiteTicket(suiteTicket);

        // 先查Redis中有没有
        String receiveId = baseInfoEntity.getReceiveId();
        Object redisByPlatFormIdAndSuiteIdObj = redisUtil.getByReceiveId(receiveId);
        
        if (ObjectUtils.isEmpty(redisByPlatFormIdAndSuiteIdObj)) {
            // 调用获取第三方企业凭证接口
            weChatThirdPartyAuthInfoService.getThirdPartyCredentials(wechatThirdPartyAuthInfoEntity, receiveId);
        } else {
            try {
                WeChatThirdPartyAuthInfoEntity entity = objectMapper.readValue(objectMapper.writeValueAsString(redisByPlatFormIdAndSuiteIdObj), WeChatThirdPartyAuthInfoEntity.class);
                entity.setSuiteTicket(suiteTicket);
                // 调用获取第三方企业凭证接口
                weChatThirdPartyAuthInfoService.getThirdPartyCredentials(entity, receiveId);
            } catch (Exception e) {
                log.error("redis中获取的第三方授权信息转换为实体类失败，platFormId：{}，suiteId: {}", platFormId, suiteId);
            }
        }
    }
}
