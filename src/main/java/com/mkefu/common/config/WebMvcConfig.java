package com.mkefu.common.config;

import com.mkefu.common.interceptor.SignatureInterceptor;
import com.mkefu.common.interceptor.TenantAuthCodeInterceptor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.web.servlet.config.annotation.AsyncSupportConfigurer;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.util.concurrent.ThreadPoolExecutor;

/**
 * WebMvc配置类，用于注册拦截器和异步支持
 * <AUTHOR>
 * @since 2025-04-10
 */
@Slf4j
@Configuration
public class WebMvcConfig implements WebMvcConfigurer {

    private final SignatureInterceptor signatureInterceptor;

    private final TenantAuthCodeInterceptor tenantAuthCodeInterceptor;

    public WebMvcConfig(SignatureInterceptor signatureInterceptor,
                        TenantAuthCodeInterceptor tenantAuthCodeInterceptor) {
        this.signatureInterceptor = signatureInterceptor;
        this.tenantAuthCodeInterceptor = tenantAuthCodeInterceptor;
    }

    /**
     * 注册拦截器
     * @param registry 拦截器注册器
     */
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 注册签名拦截器
        registry.addInterceptor(signatureInterceptor)
                .addPathPatterns("/api/**")
                .excludePathPatterns("/api/tenant/generateMethodSecretKey")
                .excludePathPatterns("/api/tenant/generateSignature")
                .excludePathPatterns("/api/wechat/kfCallBack/**")
                .excludePathPatterns("/v3/api-docs/**");

        // 注册租户授权码拦截器
        registry.addInterceptor(tenantAuthCodeInterceptor)
                .addPathPatterns("/api/**")
                .excludePathPatterns("/api/tenant/generateMethodSecretKey")
                .excludePathPatterns("/v3/api-docs/**")
                .excludePathPatterns("/api/tenant/generateSignature")
                .excludePathPatterns("/api/wechat/**")
                .excludePathPatterns("/api/redis");
    }


    /**
     * 配置Web层异步支持
     * @param configurer 异步支持配置器
     */
    @Override
    public void configureAsyncSupport(AsyncSupportConfigurer configurer) {
        // 创建专门用于Web异步处理的线程池
        ThreadPoolTaskExecutor webAsyncExecutor = new ThreadPoolTaskExecutor();
        // 核心线程数
        webAsyncExecutor.setCorePoolSize(8);
        // 最大线程数
        webAsyncExecutor.setMaxPoolSize(30);
        // 队列容量
        webAsyncExecutor.setQueueCapacity(150);
        // 线程名前缀
        webAsyncExecutor.setThreadNamePrefix("Web-Async-");
        // 线程空闲时间（秒）
        webAsyncExecutor.setKeepAliveSeconds(60);
        // 拒绝策略：由调用线程处理
        webAsyncExecutor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        // 等待所有任务结束后再关闭线程池
        webAsyncExecutor.setWaitForTasksToCompleteOnShutdown(true);
        // 等待时间（秒）
        webAsyncExecutor.setAwaitTerminationSeconds(60);
        // 初始化
        webAsyncExecutor.initialize();
        // 设置异步请求超时时间（毫秒）
        configurer.setDefaultTimeout(30000);
        // 设置异步任务执行器
        configurer.setTaskExecutor(webAsyncExecutor);
    }
}
