package com.mkefu.chatflow.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mkefu.chatflow.entity.query.LogisticsTraceQuery;

/**
 * 物流查询服务
 * <AUTHOR>
 * @since 2025-04-10
 */
public interface LogisticsService extends IService<LogisticsTraceQuery> {

    /**
     * 查询物流公司编码
     * @param logisticsNo 物流单号
     * @return 物流公司编码
     */
    String queryCode(String logisticsNo);

    /**
     * 查询物流轨迹
     * @param logisticsNo 物流单号
     * @param customerName 用户手机号后4位
     * @return 物流轨迹
     */
    String queryTrace(String logisticsNo, String customerName);
}
