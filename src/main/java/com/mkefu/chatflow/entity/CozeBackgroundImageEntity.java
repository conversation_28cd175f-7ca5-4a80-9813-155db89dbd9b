package com.mkefu.chatflow.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR> 杨国锋
 * @since 2025-05-29
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "智能体背景的图片实体类。", description = "CozeBackgroundImageEntity")
public class CozeBackgroundImageEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "Web 端背景图。")
    @JsonProperty("web_background_image")
    private CozeBackgroundImageDetail webBackgroundImage;

    @Schema(description = "移动端背景图。")
    @JsonProperty("mobile_background_image")
    private CozeBackgroundImageDetail mobileBackgroundImage;
}
