package com.mkefu.wechat.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.mkefu.common.config.RawJsonStringDeserializerConfig;
import com.mkefu.common.entity.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import java.io.Serial;
import java.io.Serializable;

/**
 * 微信客服消息同步信息实体类
 *
 * <AUTHOR> Yang 杨国锋
 * @since 2025-06-26
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Schema(name = "微信客服消息同步信息实体类", description = "WeChatAsyncMessageInfoEntity")
@TableName("wechat_async_message_info")
public class WeChatAsyncMessageInfoEntity extends BaseEntity implements Serializable {
    
    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "客服账号ID（msgType为event，该字段不返回）")
    @TableId(value = "open_kf_id", type = IdType.NONE)
    private String openKfId;
    
    @Schema(description = "租户Id")
    @TableField("tenant_id")
    private String tenantId;
    
    @Schema(description = "平台Id")
    @TableField("plat_form_id")
    private String platFormId;

    @Schema(description = "渠道类型")
    @TableField("plat_form_type")
    private String platFormType;

    @Schema(description = "消息Id")
    @TableField(exist = false)
    @JsonProperty("msgid")
    private String msgId;
    
    @Schema(description = "授权方企业Id")
    @TableField("corp_id")
    private String corpId;

    @Schema(description = "下次调用带上该值，则从当前的位置继续往后拉，以实现增量拉取。")
    @TableField("next_cursor")
    private String nextCursor;

    @Schema(description = "微信用户Id")
    @TableField("external_user_id")
    @JsonProperty("external_userid")
    private String externalUserId;

    @Schema(description = "消息类型")
    @TableField("msg_type")
    @JsonProperty("msgtype")
    private String msgType;
    
    @Schema(description = "不同类型的消息内容结构体")
    @JsonAlias({"text", "image", "voice", "video", "file", "textcard", "news", "mpnews", "miniprogrampage", "markdown", "wxcard", "location", "event"})
    @JsonDeserialize(using = RawJsonStringDeserializerConfig.class)
    @JsonTypeInfo(use = JsonTypeInfo.Id.NONE)
    private String msgContent;
}
