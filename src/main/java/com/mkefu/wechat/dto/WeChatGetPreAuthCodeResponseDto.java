package com.mkefu.wechat.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.mkefu.wechat.entity.WeChatCommonEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * 微信获取预授权码响应DTO
 *
 * <AUTHOR> 杨国锋
 * @since 2025-06-18
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Schema(name = "微信获取预授权码响应DTO", description = "WeChatGetPreAuthCodeResponseDto")
public class WeChatGetPreAuthCodeResponseDto extends WeChatCommonEntity implements Serializable {
    
    @Serial
    private static final long serialVersionUID = 1L;
    
    @Schema(description = "预授权码,最长为512字节")
    @JsonProperty("pre_auth_code")
    private String preAuthCode;
}
