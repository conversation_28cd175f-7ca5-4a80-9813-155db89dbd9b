package com.mkefu.system.contoller;

import com.mkefu.common.annotation.LogInteraction;
import com.mkefu.common.constant.ErrorCode;
import com.mkefu.common.exception.BusinessException;
import com.mkefu.common.response.Result;
import com.mkefu.system.entity.TenantAuthInfoEntity;
import com.mkefu.system.entity.query.TenantAuthInfoQuery;
import com.mkefu.system.service.TenantAuthInfoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 租户管理控制层
 * <AUTHOR>
 * @since 2025-04-10
 */
@Validated
@RestController
@RequestMapping("/api/tenant")
@Tag(name = "租户管理控制层")
public class TenantAuthInfoController {

    private final TenantAuthInfoService tenantAuthInfoService;

    public TenantAuthInfoController(TenantAuthInfoService tenantAuthInfoService) {
        this.tenantAuthInfoService = tenantAuthInfoService;
    }

    @Operation(summary = "生成接口私钥")
    @LogInteraction(description = "生成接口私钥")
    @PostMapping("/generateMethodSecretKey")
    public Result generateMethodSecretKey(@RequestBody TenantAuthInfoEntity entity) {
        if (ObjectUtils.isEmpty(entity)) {
            throw new BusinessException(ErrorCode.INVALID_PARAM, "参数不能为空！");
        }
        if (ObjectUtils.isEmpty(entity.getTenantId()) || ObjectUtils.isEmpty(entity.getTenantName())) {
            throw new BusinessException(ErrorCode.INVALID_PARAM, "租户ID和租户名称不能为空！");
        }
        return Result.success(tenantAuthInfoService.generateMethodSecretKey(entity));
    }

    @Operation(summary = "生成租户授权码")
    @LogInteraction(description = "生成租户授权码")
    @PostMapping("/generateTenantAuthCode")
    public Result generateTenantAuthCode(@Valid @RequestBody TenantAuthInfoEntity entity) {
        return Result.success(tenantAuthInfoService.generateTenantAuthCode(entity));
    }

    @Operation(summary = "查询租户对应授权码")
    @LogInteraction(description = "查询租户对应授权码")
    @GetMapping("/getAuthCodeByTenantId")
    public Result getAuthCodeByTenantId(@RequestBody TenantAuthInfoQuery query,
                                        @RequestHeader("X-Timestamp") String timestamp) {
        if (ObjectUtils.isEmpty(query)) {
            throw new BusinessException(ErrorCode.INVALID_PARAM, "参数不能为空！");
        }
        if (StringUtils.isBlank(query.getTenantId())) {
            throw new BusinessException(ErrorCode.INVALID_PARAM, "租户ID不能为空！");
        }
        return Result.success(tenantAuthInfoService.getAuthCodeByTenantId(query, timestamp));
    }

    @Operation(summary = "生成接口签名")
    @PostMapping("/generateSignature")
    @LogInteraction(description = "生成接口签名")
    public Result generateSignature(@NotNull(message = "请传入对象") @RequestBody Object object,
                                    @NotBlank(message = "请传入X-Timestamp") @RequestHeader("X-Timestamp") String timestamp,
                                    @NotBlank(message = "请传入tenantId") @RequestParam("tenantId") String tenantId) {
        return Result.success(tenantAuthInfoService.generateSignature(object, timestamp, tenantId));
    }
}
