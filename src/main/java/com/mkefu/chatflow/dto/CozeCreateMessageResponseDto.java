package com.mkefu.chatflow.dto;

import com.mkefu.chatflow.entity.OpenMessageApiEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.Map;

/**
 * <AUTHOR> 杨国锋
 * @since 2025-05-09
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "创建消息响应实体类", description = "CozeCreateMessageResponseDto")
public class CozeCreateMessageResponseDto implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "消息的详情")
    private OpenMessageApiEntity data;

    @Schema(description = "响应详情。")
    private Map<String, Object> detail;

    @Schema(description = "调用状态码。0 表示调用成功，其他值表示调用失败，你可以通过 msg 字段判断详细的错误原因。")
    private Long code;

    @Schema(description = "状态信息。API 调用失败时可通过此字段查看详细错误信息。")
    private String msg;

}
