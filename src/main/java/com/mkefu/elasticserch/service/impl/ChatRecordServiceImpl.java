package com.mkefu.elasticserch.service.impl;

import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.elasticsearch.core.IndexResponse;
import com.mkefu.common.exception.BusinessException;
import com.mkefu.elasticserch.entity.ChatRecordEntity;
import com.mkefu.elasticserch.service.ChatRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;

/**
 * 聊天记录服务实现类
 * <AUTHOR>
 * @since 2025-04-10
 */
@Slf4j
@Service
public class ChatRecordServiceImpl implements ChatRecordService {

    private final ElasticsearchClient elasticsearchClient;

    public ChatRecordServiceImpl(ElasticsearchClient elasticsearchClient) {
        this.elasticsearchClient = elasticsearchClient;
    }

    /**
     * 保存聊天记录
     * @param chatRecordEntity 实体类
     * @return 实体类
     */
    @Override
    public ChatRecordEntity saveChatRecord(ChatRecordEntity chatRecordEntity) {
        try {
            IndexResponse response = elasticsearchClient.index(indexRequest -> indexRequest
                    .index("chat_records")
                    .id(String.valueOf(chatRecordEntity.getId()))
                    .document(chatRecordEntity)
            );
            log.info("Saved chat record: {}, response: {}", chatRecordEntity, response);
            return chatRecordEntity;
        } catch (IOException e) {
            log.error("Failed to save chat record: {}", e.getMessage(), e);
            throw new BusinessException("无法保存数据到ES，请检查！", e.getMessage());
        }
    }
}
