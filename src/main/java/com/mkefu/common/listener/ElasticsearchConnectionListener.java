package com.mkefu.common.listener;

import co.elastic.clients.elasticsearch.ElasticsearchClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

/**
 * Elasticsearch 连接监听器，用于在应用程序启动时测试 Elasticsearch 连接。
 * <AUTHOR>
 * @since 2025-04-10
 */
@Slf4j
@Component
public class ElasticsearchConnectionListener implements ApplicationRunner {

    private final ElasticsearchClient client;

    public ElasticsearchConnectionListener(ElasticsearchClient client) {
        this.client = client;
    }

    /**
     * 在应用程序启动时测试 Elasticsearch 连接。
     * @param args 应用程序启动参数
     */
    @Override
    public void run(ApplicationArguments args) {
        log.info("=== 启动时测试 Elasticsearch 连接 ===");
        try {
            // 测试连接
            boolean ping = client.ping().value();
            if (ping) {
                log.info("Elasticsearch 连接成功！");
                // 获取集群信息
                String clusterInfo = client.info().toString();
                log.info("集群信息：{}", clusterInfo);
            } else {
                log.warn("Elasticsearch 连接测试失败，ping 返回 false。");
            }
        } catch (Exception e) {
            log.warn("无法连接到 Elasticsearch，应用程序将继续启动而不连接到 Elasticsearch");
        }
    }
}
