package com.mkefu.common.interceptor;

import com.baomidou.mybatisplus.extension.plugins.inner.InnerInterceptor;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.ParameterMapping;
import org.apache.ibatis.session.Configuration;
import org.apache.ibatis.session.ResultHandler;
import org.apache.ibatis.session.RowBounds;
import org.apache.ibatis.type.TypeHandlerRegistry;
import org.apache.ibatis.reflection.MetaObject;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * SQL 日志拦截器
 *
 * <AUTHOR>
 * @since 2025-04-10
 */
@Slf4j
public class SqlLogInterceptor implements InnerInterceptor {

    /**
     * SQL参数占位符正则表达式
     */
    private static final Pattern PARAMETER_PATTERN = Pattern.compile("\\?");

    /**
     * SQL命令关键字正则表达式 (用于识别 INSERT INTO, SELECT, UPDATE, DELETE 等命令)
     */
    private static final Pattern SQL_COMMAND_PATTERN = Pattern.compile("(?i)^(INSERT INTO|SELECT|UPDATE|DELETE).*");

    /**
     * 日期时间格式化器
     */
    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    /**
     * 更新操作前执行
     * 拦截 SQL 更新操作，打印格式化后的 SQL 日志。
     *
     * @param executor 执行器
     * @param ms 映射语句
     * @param parameter 参数
     */
    @Override
    public void beforeUpdate(Executor executor, MappedStatement ms, Object parameter) {
        BoundSql boundSql = ms.getBoundSql(parameter);
        Configuration configuration = ms.getConfiguration();
        String sql = getSql(configuration, boundSql, ms.getId());
        log.info("==> Executing SQL: \n{}", sql);
    }

    /**
     * 查询操作前执行
     * 拦截 SQL 查询操作，打印格式化后的 SQL 日志。
     *
     * @param executor 执行器
     * @param ms 映射语句
     * @param parameter 参数对象
     * @param rowBounds 行数限制
     * @param resultHandler 结果处理器
     * @param boundSql 绑定 SQL
     */
    @Override
    public void beforeQuery(Executor executor, MappedStatement ms, Object parameter, RowBounds rowBounds, ResultHandler resultHandler, BoundSql boundSql) {
        Configuration configuration = ms.getConfiguration();
        String sql = getSql(configuration, boundSql, ms.getId());
        log.info("==> Executing SQL: \n{}", sql);
    }

    /**
     * 获取格式化后的 SQL 字符串。
     *
     * @param configuration 配置对象
     * @param boundSql 绑定 SQL 对象
     * @param sqlId SQL 语句的唯一 ID (通常是 Mapper 接口方法的全限定名)
     * @return 格式化后的 SQL 字符串，包含 SQL ID。
     */
    private String getSql(Configuration configuration, BoundSql boundSql, String sqlId) {
        String sql = formatSql(configuration, boundSql);
        return '[' + sqlId + "]\n" + sql;
    }

    /**
     * 对原始 SQL 进行参数替换和基础格式化。
     *
     * @param configuration 配置对象
     * @param boundSql 绑定 SQL 对象
     * @return 替换参数后的 SQL 字符串。
     */
    private String formatSql(Configuration configuration, BoundSql boundSql) {
        // 先移除多余的空格，简化后续处理。此处使用 replaceAll 是合理的，因为 \s+ 是正则表达式，用于匹配一个或多个空白字符。
        String sql = boundSql.getSql().replaceAll("\\s+", " ");
        List<ParameterMapping> parameterMappings = boundSql.getParameterMappings();
        Object parameterObject = boundSql.getParameterObject();

        if (parameterMappings != null && !parameterMappings.isEmpty()) {
            sql = replaceSqlParameters(sql, configuration, boundSql, parameterMappings, parameterObject);
        }
        return formatSqlStructure(sql);
    }

    /**
     * 替换 SQL 语句中的参数占位符 '?'。
     * 此方法直接根据参数映射列表和参数对象获取值并进行替换，确保参数与占位符的匹配。
     *
     * @param sql 原始 SQL 字符串
     * @param configuration 配置对象
     * @param boundSql 绑定 SQL 对象
     * @param parameterMappings 参数映射列表
     * @param parameterObject 参数对象
     * @return 替换参数后的 SQL 字符串
     */
    private String replaceSqlParameters(String sql, Configuration configuration, BoundSql boundSql, List<ParameterMapping> parameterMappings, Object parameterObject) {
        TypeHandlerRegistry typeHandlerRegistry = configuration.getTypeHandlerRegistry();
        // 使用 StringBuilder 进行高效的字符串操作，避免大量中间字符串对象的创建
        StringBuilder resultSql = new StringBuilder(sql);
        // Matcher 需要在每次替换后重新创建，因为替换会改变字符串的长度和位置，旧的 Matcher 会失效
        Matcher matcher = PARAMETER_PATTERN.matcher(resultSql);
        int offset = 0; // 记录替换引起的字符串长度变化，用于调整后续查找的起始位置

        for (ParameterMapping parameterMapping : parameterMappings) {
            // 确保找到下一个 '?' 占位符
            if (matcher.find(offset)) {
                String propertyName = parameterMapping.getProperty();
                Object value;

                // 优先从 additionalParameters 中获取值 (例如用于批量操作的参数)
                if (boundSql.hasAdditionalParameter(propertyName)) {
                    value = boundSql.getAdditionalParameter(propertyName);
                }
                // 如果参数对象为空，则值为 null
                else if (parameterObject == null) {
                    value = null;
                }
                // 如果参数对象本身就可以被 TypeHandler 处理 (如 String, Integer 等基本类型或包装类型)
                else if (typeHandlerRegistry.hasTypeHandler(parameterObject.getClass())) {
                    value = parameterObject;
                }
                // 对于复杂对象，通过 MetaObject 获取其属性值
                else {
                    MetaObject metaObject = configuration.newMetaObject(parameterObject);
                    // 确保属性名存在，避免 NoSuchFieldException 等
                    if (metaObject.hasGetter(propertyName)) {
                        value = metaObject.getValue(propertyName);
                    } else {
                        value = null; // 属性不存在，设为 null
                        log.warn("Property '{}' not found in parameter object: {}", propertyName, parameterObject.getClass().getName());
                    }
                }

                String formattedValue = formatParameterValue(value);
                int start = matcher.start();
                int end = matcher.end();

                // 执行替换操作
                resultSql.replace(start, end, formattedValue);

                // 更新偏移量，因为字符串长度可能已经改变，以确保下一次查找从正确的位置开始
                offset = start + formattedValue.length();
                // 重新创建 Matcher，以适应 StringBuilder 内容的变化
                matcher = PARAMETER_PATTERN.matcher(resultSql);
            } else {
                // 如果参数映射数量多于 '?' 占位符，记录警告并停止处理
                log.warn("SQL 参数数量与占位符不匹配：SQL '{}' 中参数多于问号。未替换的属性: {}", sql, parameterMapping.getProperty());
                break;
            }
        }

        // 检查是否有未替换的问号，这通常不应该发生，除非 SQL 和参数映射不匹配
        if (matcher.find(offset)) {
            log.warn("SQL 参数数量与占位符不匹配：SQL '{}' 中问号多于参数。", resultSql);
        }

        return resultSql.toString();
    }

    /**
     * 格式化 SQL 语句的结构，使其更具可读性。
     * 根据 SQL 命令类型（UPDATE, INSERT INTO, SELECT, DELETE）进行不同的格式化。
     *
     * @param sql 替换参数后的 SQL 字符串
     * @return 格式化后的 SQL 字符串
     */
    private String formatSqlStructure(String sql) {
        StringBuilder formattedSql = new StringBuilder();
        String tempSql = sql.replaceAll("\\s+", " ").trim(); // 再次规范化空格

        // 针对 UPDATE 语句进行格式化
        if (tempSql.toUpperCase().startsWith("UPDATE")) {
            int setIndex = tempSql.toUpperCase().indexOf(" SET ");
            int whereIndex = tempSql.toUpperCase().indexOf(" WHERE ");

            if (setIndex != -1) {
                // 提取 "UPDATE ... SET " 部分。此 substring 调用用于截取字符串的特定起始部分，并非冗余。
                formattedSql.append(tempSql, 0, setIndex + 5).append("\n"); // 优化: 直接使用 append(CharSequence, int, int)

                // 提取并分割 SET 子句的键值对，由新方法处理
                String[] setPairs = extractSetClausePairs(tempSql, setIndex, whereIndex);

                // 对 SET 子句内的每个键值对进行换行
                for (int i = 0; i < setPairs.length; i++) {
                    formattedSql.append("  ").append(setPairs[i].trim());
                    if (i < setPairs.length - 1) {
                        formattedSql.append(",\n");
                    }
                }
                if (whereIndex != -1) {
                    // 提取 WHERE 子句内容。此 substring 调用用于截取字符串的特定起始到末尾部分，并非冗余。
                    formattedSql.append("\n").append(tempSql.substring(whereIndex)).append("\n");
                }
            } else {
                formattedSql.append(tempSql).append("\n"); // 如果没有 SET 子句，直接添加
            }
        }
        // 针对 INSERT INTO ... VALUES 语句进行格式化
        else if (tempSql.toUpperCase().startsWith("INSERT INTO") && tempSql.toUpperCase().contains("VALUES")) {
            int valuesIndex = tempSql.toUpperCase().indexOf("VALUES");
            if (valuesIndex != -1) {
                // 提取 "INSERT INTO ... " 部分。此 substring 调用用于截取字符串的特定起始部分，并非冗余。
                formattedSql.append(tempSql, 0, valuesIndex).append("\n"); // 优化: 直接使用 append(CharSequence, int, int)
                // 提取 VALUES 子句内容。此 substring 调用用于截取字符串的特定起始到末尾部分，并非冗余。
                String valuesPart = tempSql.substring(valuesIndex).trim();
                // 格式化 VALUES 部分，使其括号内的每个值或每个括号对换行
                // 使用 replaceFirst 替换第一个 '( ' 为换行和 '('
                valuesPart = valuesPart.replaceFirst("\\s*\\(", "\n (");
                // 将所有 "),(" 替换为 "),\n ("。注意：这里是字面量字符串替换，所以使用 replace() 而不是 replaceAll()。
                formattedSql.append(valuesPart.replace("),(", "),\n (")).append("\n");
            } else {
                formattedSql.append(tempSql).append("\n");
            }
        }
        // 对于其他 SQL 命令 (SELECT, DELETE 等)，进行简单的换行处理
        else if (SQL_COMMAND_PATTERN.matcher(tempSql).matches()) {
            formattedSql.append(tempSql.replaceFirst("\\s*\\(", "\n (")).append("\n");
        } else {
            formattedSql.append(tempSql).append("\n"); // 默认处理，每个语句一行
        }

        return formattedSql.toString().trim();
    }

    /**
     * 从 SQL 字符串中提取并分割 SET 子句的键值对。
     * 该方法封装了 SET 子句的解析逻辑，提高了 formatSqlStructure 的可读性。
     *
     * @param tempSql 规范化后的原始 SQL 字符串
     * @param setIndex " SET " 关键字的起始索引
     * @param whereIndex " WHERE " 关键字的起始索引 (如果存在)
     * @return 包含 SET 子句中每个键值对的字符串数组
     */
    private String[] extractSetClausePairs(String tempSql, int setIndex, int whereIndex) {
        String setClause;
        if (whereIndex != -1) {
            // 提取 SET 子句内容。此 substring 调用用于截取字符串的中间部分，并非冗余。
            setClause = tempSql.substring(setIndex + 5, whereIndex).trim();
        } else {
            // 提取 SET 子句内容（无 WHERE）。此 substring 调用用于截取字符串的特定起始到末尾部分，并非冗余。
            setClause = tempSql.substring(setIndex + 5).trim();
        }
        return setClause.split(",");
    }

    /**
     * 格式化参数值，根据其类型返回 SQL 友好的字符串表示。
     * 字符串和日期类型会添加单引号并进行转义。
     *
     * @param value 参数的原始值
     * @return 格式化后的参数值字符串
     */
    private String formatParameterValue(Object value) {
        if (value == null) {
            return "NULL";
        }
        return switch (value) {
            case String s -> "'" + escapeQuote(s) + "'";
            case Date date -> "'" + formatDate(date) + "'";
            case LocalDateTime dateTime -> "'" + dateTime.format(DATE_TIME_FORMATTER) + "'";
            default -> value.toString();
        };
    }

    /**
     * 转义字符串中的单引号，防止 SQL 注入。
     * 将单个单引号替换为两个单引号 (适用于 SQL 字符串字面量中的转义规则)。
     *
     * @param str 原始字符串
     * @return 转义后的字符串
     */
    private String escapeQuote(String str) {
        return str.replace("'", "''");
    }

    /**
     * 格式化日期对象为指定的日期时间字符串格式。
     *
     * @param date 日期对象
     * @return 格式化后的日期字符串 (yyyy-MM-dd HH:mm:ss)
     */
    private String formatDate(Date date) {
        return DATE_TIME_FORMATTER.format(
                LocalDateTime.ofInstant(date.toInstant(), java.time.ZoneId.systemDefault()));
    }
}
