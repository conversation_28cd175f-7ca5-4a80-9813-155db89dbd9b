package com.mkefu.common.listener;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationListener;

import org.springframework.stereotype.Component;

/**
 * 应用启动监听器
 * <AUTHOR>
 * @since 2025-04-10
 */
@Slf4j
@Component
public class ApplicationStartupListener implements ApplicationListener<ApplicationReadyEvent> {

    /**
     * 记录启动开始时间
     */
    private static final long START_TIME = System.currentTimeMillis();

    /**
     * 应用启动完成事件
     * @param event 事件
     */
    @Override
    public void onApplicationEvent(ApplicationReadyEvent event) {
        long endTime = event.getTimestamp();
        long durationMillis = endTime - START_TIME;
        long durationSeconds = durationMillis / 1000;
        log.info("应用启动完成！chatflow-service 已准备好处理请求。");
        log.info("启动时间: {}秒", durationSeconds);
        log.info("应用名称: {}", event.getSpringApplication().getMainApplicationClass().getName());
    }
}
