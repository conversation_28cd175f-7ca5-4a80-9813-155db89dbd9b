# mkefu-chatflow-service

## 项目简介

mkefu-chatflow-service 是一个聊天流服务，提供与多个AI平台的集成，包括Coze和Dify。该服务允许用户通过统一的API与不同的AI平台进行交互，支持消息发送、会话管理、历史记录查询等功能。

## 主要功能

- **多平台集成**：支持Coze和Dify平台的API集成
- **消息发送**：支持流式和非流式消息发送
- **会话管理**：创建、查询、删除会话
- **消息管理**：创建、查询、更新、删除消息
- **安全认证**：基于签名的API认证机制

## 技术栈

- Java 21
- Spring Boot
- MySQL 8.x
- Redis
- PostgreSQL (用于Dify集成)
- Elasticsearch (用于消息存储和搜索)

## 快速开始

### 前提条件

确保你的开发环境中已安装：
- Java 21
- Maven 3.8+
- MySQL 8.x
- Redis
- PostgreSQL (可选，仅用于Dify集成)

### 配置

1. 克隆项目到本地
2. 配置数据库连接（修改`application-local.yaml`）
3. 配置Redis连接（修改`application-local.yaml`）
4. 配置API密钥（如需使用第三方服务）

### 构建和运行

```bash
# 构建项目
mvn clean package

# 运行项目
java -jar target/mkefu-chatflow-service.jar
```

## API文档

启动服务后，可以通过以下地址访问API文档：
```
http://localhost:8080/swagger-ui.html
```

## 开发指南

详细的开发指南，包括构建说明、配置信息、测试指南以及其他开发相关的重要信息，请查看 [DEVELOPMENT.md](DEVELOPMENT.md) 文件。

## 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add some amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建Pull Request

## 许可证

本项目采用 [MIT 许可证](LICENSE)。
