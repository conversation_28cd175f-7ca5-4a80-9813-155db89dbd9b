package com.mkefu.chatflow.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * Dify发送对话传输对象
 * <AUTHOR> Yang 杨国锋
 * @since 2025-04-17
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "Dify发送对话传输对象", description = "DifyChatRequestDto")
public class DifyChatRequestDto implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "用户输入/提问内容", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "请输入提问内容")
    private String query;

    @Schema(description = "允许传入 App 定义的各变量值。 inputs 参数包含了多组键值对（Key/Value pairs），" +
            "每组的键对应一个特定变量，每组的值则是该变量的具体值。 " +
            "如果变量是文件类型，请指定一个包含以下 files 中所述键的对象。 默认 {}")
    private Object inputs;

    @Schema(description = "1.streaming 流式模式（推荐）。基于 SSE（Server-Sent Events）实现类似打字机输出方式的流式返回 " +
            "2. blocking 阻塞模式，等待执行完毕后返回结果。（请求若流程较长可能会被中断）。 由于 Cloudflare 限制，请求会在 100 秒超时无返回后中断"
            , requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty("response_mode")
    private String responseMode;

    @Schema(description = "用户标识，用于定义终端用户的身份，方便检索、统计。 由开发者定义规则，需保证用户标识在应用内唯一。", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "请输入用户标识")
    private String user;

    @Schema(description = "（选填）会话 ID，需要基于之前的聊天记录继续对话，必须传之前消息的 conversation_id")
    @JsonProperty("conversation_id")
    private String conversationId;

    @Schema(description = "文件列表，适用于传入文件结合文本理解并回答问题，仅当模型支持 Vision 能力时可用。" +
            "type (string) 支持类型：" +
            "document 具体类型包含：'TXT', 'MD', 'MARKDOWN', 'PDF', 'HTML', 'XLSX', 'XLS', 'DOCX', 'CSV', 'EML', 'MSG', 'PPTX', 'PPT', 'XML', 'EPUB'" +
            "image 具体类型包含：'JPG', 'JPEG', 'PNG', 'GIF', 'WEBP', 'SVG'" +
            "audio 具体类型包含：'MP3', 'M4A', 'WAV', 'WEBM', 'AMR'" +
            "video 具体类型包含：'MP4', 'MOV', 'MPEG', 'MPGA'" +
            "custom 具体类型包含：其他文件类型" +
            "transfer_method (string) 传递方式:" +
            "remote_url: 图片地址。" +
            "local_file: 上传文件。" +
            "url 图片地址。（仅当传递方式为 remote_url 时）。" +
            "upload_file_id 上传文件 ID。（仅当传递方式为 local_file 时）。")
    private List<Object> files;

    @Schema(description = "（选填）自动生成标题，默认 true。 若设置为 false，则可通过调用会话重命名接口并设置 auto_generate 为 true 实现异步生成标题。")
    @JsonProperty("auto_generate_name")
    private boolean autoGenerateName;


}
