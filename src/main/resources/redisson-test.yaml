singleServerConfig:
  address: redis://localhost:6379
  password: 1q2w3e4r.redis
  database: 3
  idleConnectionTimeout: 10000
  connectTimeout: 10000
  timeout: 3000
  retryAttempts: 5
  retryInterval: 3500
  subscriptionsPerConnection: 5
  subscriptionConnectionMinimumIdleSize: 1
  subscriptionConnectionPoolSize: 1
  connectionMinimumIdleSize: 1
  connectionPoolSize: 1
threads: 0
nettyThreads: 0
codec: !org.redisson.codec.JsonJacksonCodec {}
transportMode: NIO
