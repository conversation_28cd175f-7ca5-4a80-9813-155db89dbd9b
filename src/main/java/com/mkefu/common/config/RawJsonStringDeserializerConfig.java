package com.mkefu.common.config;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonToken;
import com.fasterxml.jackson.databind.*;

import java.io.IOException;

/**
 * 自定义 Jackson 反序列化器，用于将 JSON 中的任何结构（对象、数组、原始值）
 * 反序列化为 Java String 类型。它会将 JSON 节点的完整文本内容作为字符串返回。
 * 解决了将 JSON 对象反序列化到 String 字段时的 MismatchedInputException。
 *
 * <AUTHOR>
 * @since 2025-06-26
 */
public class RawJsonStringDeserializerConfig extends JsonDeserializer<String> {
    
    @Override
    public String deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
        JsonToken currentToken = p.getCurrentToken();
        if (currentToken == JsonToken.VALUE_STRING) {
            return p.getText();
        } else if (currentToken == JsonToken.START_OBJECT || currentToken == JsonToken.START_ARRAY) {
            return p.getCodec().readTree(p).toString();

        } else if (currentToken != null && currentToken.isScalarValue()) {
            return p.getText();
        }
        return null;
    }
    
}
