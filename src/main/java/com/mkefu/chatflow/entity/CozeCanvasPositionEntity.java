package com.mkefu.chatflow.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR> Yang 杨国锋
 * @since 2025-05-29
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "背景图定义位置和尺寸实体类", description = "CozeCanvasPositionEntity")
public class CozeCanvasPositionEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "裁剪区域顶部距离画布顶部的偏移量，单位为像素（px）。值越大，裁剪区域越向下移动。" +
            "top 和 left 的值不能超过画布的实际尺寸")
    private Double top;

    @Schema(description = "裁剪区域左侧距离画布左侧的偏移量，单位为像素（px）。值越大，裁剪区域越向右移动。")
    private Double left;

    @Schema(description = "裁剪区域的宽度，单位为像素（px）。此值决定了裁剪区域的水平范围，必须为正数。")
    private Double width;

    @Schema(description = "裁剪区域的高度，单位为像素（px）。此值决定了裁剪区域的垂直范围，必须为正数。")
    private Double height;
}
