package com.mkefu.wechat.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.mkefu.common.entity.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * 代开发企业配置页信息
 *
 * <AUTHOR> Yang 杨国锋
 * @since 2025-06-19
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("wechat_third_party_base_info")
@Schema(name = "代开发企业配置页信息", description = "WeChatThirdPartyBaseInfoEntity")
public class WeChatThirdPartyBaseInfoEntity extends BaseEntity implements Serializable {
    
    @Serial
    private static final long serialVersionUID = 1L;
    
    @Schema(description = "平台Id")
    @TableId(value = "plat_form_id", type = IdType.NONE)
    private String platFormId;

    @Schema(description = "服务商企业Id")
    @TableField("receive_id")
    private String receiveId;

    @Schema(description = "服务商应用Id")
    @TableField("suite_id")
    @JsonProperty("suite_id")
    private String suiteId;

    @Schema(description = "服务密钥")
    @TableField("suite_secret")
    @JsonProperty("suite_secret")
    private String suiteSecret;

    @Schema(description = "服务Token")
    @TableField("suite_token")
    @JsonProperty("suite_token")
    private String suiteToken;

    @Schema(description = "服务解密密钥")
    @TableField("suite_encoding_aes_key")
    @JsonProperty("suite_encoding_aes_key")
    private String suiteEncodingAesKey;
    
}
