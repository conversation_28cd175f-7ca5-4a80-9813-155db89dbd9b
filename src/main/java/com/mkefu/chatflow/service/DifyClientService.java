package com.mkefu.chatflow.service;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.mkefu.common.constant.CustomConstant;
import com.mkefu.common.constant.ErrorCode;
import com.mkefu.common.exception.BusinessException;
import com.mkefu.chatflow.entity.DifyNodesEntity;
import com.mkefu.chatflow.manager.DifyNodeManager;
import io.github.resilience4j.retry.Retry;
import io.github.resilience4j.retry.RetryRegistry;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * Dify 客户端服务，负责调用 Dify API
 * <AUTHOR> 杨国锋
 * @since 2025-04-23
 */
@Slf4j
@Service
public class DifyClientService {

    private final Optional<DifyNodeManager> nodeManagerOptional;

    private final Retry retry;

    /**
     * 构造函数，注入 DifyNodeManager 和 RetryRegistry
     * @param nodeManagerOptional Dify 节点管理器
     * @param retryRegistry Resilience4j 重试注册表
     */
    public DifyClientService(Optional<DifyNodeManager> nodeManagerOptional, RetryRegistry retryRegistry) {
        this.nodeManagerOptional = nodeManagerOptional;
        this.retry = retryRegistry.retry("difyApi");

        // 记录 DifyNodeManager 是否可用
        if (nodeManagerOptional.isEmpty()) {
            log.warn("DifyNodeManager bean 未被初始化，无法正常调用 Dify API。请检查数据库连接。");
        } else {
            log.info("DifyNodeManager bean 已经初始化，可以正常调用 Dify API。");
        }
    }

    /**
     * 调用 Dify API，支持多种 HTTP 方法和参数
     * @param tenantId 租户唯一标识
     * @param endpoint API 端点（如 /v1/endpoint）
     * @param httpMethod HTTP 方法（GET, POST, PUT, DELETE 等）
     * @param requestBody 请求体（JSON 字符串，POST/PUT 时使用）
     * @param queryParams 查询参数（如 ?key=value）
     * @param pathParams 路径占位符参数（如 {param} -> value）
     * @return API 响应体
     * @throws BusinessException 如果调用失败或 Dify 节点服务不可用
     */
    public String callDifyApi(String tenantId, String endpoint, String httpMethod, String requestBody,
                              Map<String, Object> queryParams, Map<String, Object> pathParams) {

        // 检查 DifyNodeManager 是否可用
        DifyNodeManager nodeManager = nodeManagerOptional.orElseThrow(
                () -> new BusinessException(ErrorCode.DIFY_SERVER_ERROR, "Dify 节点管理服务未启动，无法获取节点URL。")
        );

        // 获取租户分配的节点
        DifyNodesEntity node = nodeManager.getNodeUrl(tenantId);
        // 处理路径占位符
        String resolvedEndpoint = endpoint;
        if (pathParams != null && !pathParams.isEmpty()) {
            for (Map.Entry<String, Object> entry : pathParams.entrySet()) {
                resolvedEndpoint = resolvedEndpoint.replace("{" + entry.getKey() + "}", entry.getValue().toString());
            }
        }
        // 构造完整 URL
        String fullUrl = node.getUrl() + resolvedEndpoint;

        String finalResolvedEndpoint = resolvedEndpoint;
        return Retry.decorateSupplier(retry, () -> {
            // 根据 HTTP 方法构造请求
            HttpRequest request = switch (httpMethod.toUpperCase()) {
                case "GET" -> HttpRequest.get(fullUrl);
                case "POST" -> HttpRequest.post(fullUrl).body(requestBody);
                case "PUT" -> HttpRequest.put(fullUrl).body(requestBody);
                case "DELETE" -> HttpRequest.delete(fullUrl);
                default -> throw new BusinessException(ErrorCode.DIFY_SERVER_ERROR, "不支持的 HTTP 方法: " + httpMethod);
            };
            // 设置请求头
            request.header(CustomConstant.HEADER_AUTHORIZATION, "Bearer " + node.getApiKey())
                    .header("Content-Type", "application/json");
            // 添加查询参数
            if (queryParams != null && !queryParams.isEmpty()) {
                Map<String, Object> queryParamsAsObject = new HashMap<>(queryParams);
                request.form(queryParamsAsObject);
            }
            // 执行请求
            try (HttpResponse response = request.execute()) {
                if (response.isOk()) {
                    String responseBody = response.body();
                    log.info("租户: {} 调用 Dify API 成功, method: {}, endpoint: {}, response: {}",
                            tenantId, httpMethod, finalResolvedEndpoint, responseBody);
                    return responseBody;
                }
                throw new BusinessException(ErrorCode.DIFY_METHOD_ERROR, "API 调用失败，状态码: " + response.getStatus() + ", 响应体: " + response.body());
            } catch (Exception e) {
                log.error("租户: {} 调用 Dify API 失败, method: {}, endpoint: {}, message: {}",
                        tenantId, httpMethod, finalResolvedEndpoint, e.getMessage());
                throw new BusinessException(ErrorCode.DIFY_METHOD_ERROR, e.getMessage());
            }
        }).get();
    }
}
