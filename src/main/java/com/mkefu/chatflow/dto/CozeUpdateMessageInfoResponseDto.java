package com.mkefu.chatflow.dto;

import com.mkefu.chatflow.entity.CozeBaseResponseEntity;
import com.mkefu.chatflow.entity.MessageObjectEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR> 杨国锋
 * @since 2025-05-09
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "修改消息响应DTO", description = "CozeUpdateMessageInfoResponseDto")
@EqualsAndHashCode(callSuper = true)
public class CozeUpdateMessageInfoResponseDto extends CozeBaseResponseEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "修改后的消息详细信息")
    private MessageObjectEntity message;
}


