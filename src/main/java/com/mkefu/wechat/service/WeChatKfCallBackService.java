package com.mkefu.wechat.service;


/**
 * 微信客服回调接口层
 * <AUTHOR> Yang 杨国锋
 * @since 2025-06-17
 */
public interface WeChatKfCallBackService {

    /**
     * 校验密文和重新生成明文是否一致
     *
     * @param methodType   方法类型
     * @param msgSignature 加密签名
     * @param timestamp    时间戳
     * @param nonce        随机数
     * @param echoStr      加密的字符串，解密后需原样返回
     * @param platFormId   平台Id
     * @return 解密后的明文
     */
    String verifyDataCallBack(String methodType, String msgSignature, String timestamp, String nonce, String echoStr, String platFormId);

    /**
     * 处理POST回调
     *
     * @param requestBody  请求体
     * @param msgSignature 加密签名
     * @param timestamp    时间戳
     * @param nonce        随机数
     * @param platFormId   平台Id
     * @return 响应体
     */
    String handlePostCallBack(String requestBody, String msgSignature, String timestamp, String nonce, String platFormId);
}
