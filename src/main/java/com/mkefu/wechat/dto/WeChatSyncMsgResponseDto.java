package com.mkefu.wechat.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.mkefu.wechat.entity.WeChatAsyncMessageInfoEntity;
import com.mkefu.wechat.entity.WeChatCommonEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 读取微信消息响应DTO
 *
 * <AUTHOR> Yang 杨国锋
 * @since 2025-06-26
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Schema(name = "读取微信消息响应DTO", description = "WeChatSyncMsgResponseDto")
public class WeChatSyncMsgResponseDto extends WeChatCommonEntity implements Serializable {
    
    @Serial
    private static final long serialVersionUID = 1L;
    
    @Schema(description = "下次调用带上该值，则从当前的位置继续往后拉，以实现增量拉取。")
    @JsonProperty("next_cursor")
    private String nextCursor;

    @Schema(description = "是否还有更多数据。0-否；1-是。" +
            "不能通过判断msg_list是否空来停止拉取，可能会出现has_more为1，而msg_list为空的情况")
    @JsonProperty("has_more")
    private int hasMore;
    
    @Schema(description = "微信消息信息实体类")
    @JsonProperty("msg_list")
    private List<WeChatAsyncMessageInfoEntity> weChatAsyncMessageInfoList;
}
