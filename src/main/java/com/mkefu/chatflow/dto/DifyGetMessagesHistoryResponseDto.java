package com.mkefu.chatflow.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.mkefu.chatflow.entity.DifyMessageEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR> 杨国锋
 * @since 2025-05-13
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "Dify获取会话历史消息响应DTO", description = "DifyGetMessagesHistoryResponseDto")
public class DifyGetMessagesHistoryResponseDto implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "返回条数，若传入超过系统限制，返回系统限制数量")
    private int limit;

    @Schema(description = "是否存在下一页")
    @JsonProperty("has_more")
    private boolean hasMore;

    @Schema(description = "消息列表")
    private List<DifyMessageEntity> data;
}
