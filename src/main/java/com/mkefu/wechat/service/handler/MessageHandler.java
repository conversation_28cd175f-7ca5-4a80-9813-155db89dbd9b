package com.mkefu.wechat.service.handler;


import com.mkefu.wechat.dto.WeChatMessageToImServiceRequestDto;

/**
 * 消息处理器接口
 * 
 * <AUTHOR> 杨国锋
 * @since 2025-07-08
 */
@FunctionalInterface
public interface MessageHandler {

    /**
     * 处理特定类型的消息
     * 
     * @param dto 请求DTO
     * @param methodName imService的方法      
     */
    void handle(WeChatMessageToImServiceRequestDto dto, String methodName);
}
