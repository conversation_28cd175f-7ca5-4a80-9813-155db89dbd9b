package com.mkefu.common.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR> 杨国锋
 * @since 2025-06-17
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Configuration
@Schema(name = "企业微信配置实体类", description = "WeChatKfConfigEntity")
@ConfigurationProperties(prefix = "wechat.kf")
public class WeChatKfConfigEntity {
    
    @Schema(description = "默认客服名称")
    private String name;
    
    @Schema(description = "服务商企业Id")
    private String receiveId;

    @Schema(description = "默认客服头像Url")
    private String headImageUrl;
}
