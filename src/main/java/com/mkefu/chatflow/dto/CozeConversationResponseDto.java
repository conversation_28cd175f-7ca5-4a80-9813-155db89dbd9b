package com.mkefu.chatflow.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.Map;

/**
 * <AUTHOR> 杨国锋
 * @since 2025-05-08
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "创建会话响应实体类", description = "CozeConversationResponseDto")
public class CozeConversationResponseDto implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "状态码。" +
            "0 代表调用成功。")
    private int code;

    @Schema(description = "本次对话的基本信息")
    private Map<String, Object> data;

    @Schema(description = "状态信息。API 调用失败时可通过此字段查看详细错误信息。")
    private String msg;
}
