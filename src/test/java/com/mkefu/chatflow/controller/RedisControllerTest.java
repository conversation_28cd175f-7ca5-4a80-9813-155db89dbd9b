package com.mkefu.chatflow.controller;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mkefu.common.constant.ErrorCode;
import com.mkefu.common.exception.BusinessException;
import com.mkefu.system.entity.TenantAuthInfoEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * <AUTHOR> 杨国锋
 * @since 2025-05-16
 */
@Slf4j
@SpringBootTest
@Schema(description = "Redis操作控制层Test")
class RedisControllerTest {

    private final ObjectMapper objectMapper;

    private String authCode = "";

    RedisControllerTest(ObjectMapper objectMapper) {
        this.objectMapper = objectMapper;
    }

    @Test
    void getByKey() {
        try {
            HttpRequest request = HttpRequest
                    .get("http://localhost:8080/api/redis/getByKey")
                    .form("key", "test");
            try (HttpResponse response = request.execute()) {
                Assertions.assertTrue(response.isOk(), ErrorCode.REDIS_GET_ERROR.getDefaultMessage());
                JsonNode jsonNode = objectMapper.readTree(response.body());
                TenantAuthInfoEntity entity = objectMapper.treeToValue(jsonNode.at("/data"), TenantAuthInfoEntity.class);
                authCode = entity.getAuthCode();
                log.info("调用接口：{}，响应内容：{}", request.getUrl(), objectMapper.writeValueAsString(entity));
            }
        } catch (Exception e) {
            throw new BusinessException(ErrorCode.REDIS_GET_ERROR);
        }
    }

    @Test
    void updateKey() {
        try {
            TenantAuthInfoEntity entity = init();
            HttpRequest request = HttpRequest
                    .post("http://localhost:8080/api/redis/updateKey")
                    .body(objectMapper.writeValueAsString(entity));
            try (HttpResponse response = request.execute()) {
                Assertions.assertTrue(response.isOk(), ErrorCode.REDIS_UPDATE_ERROR.getDefaultMessage());
                JsonNode jsonNode = objectMapper.readTree(response.body());
                TenantAuthInfoEntity jsonEntity = objectMapper.treeToValue(jsonNode.at("/data"), TenantAuthInfoEntity.class);
                log.info("调用接口：{}，响应内容：{}", request.getUrl(), objectMapper.writeValueAsString(jsonEntity));
            }
        } catch (Exception e) {
            throw new BusinessException(ErrorCode.REDIS_UPDATE_ERROR);
        }
    }

    /**
     * 初始化
     * @return TenantAuthInfoEntity
     */
    private TenantAuthInfoEntity init() {
        TenantAuthInfoEntity entity = new TenantAuthInfoEntity();
        entity.setTenantId("L6666");
        entity.setTenantName("L6666");
        entity.setAuthCode(authCode);
        entity.setValidityPeriodTime(5400L);
        entity.setAgentId("coze");
        entity.setAgentName("coze平台");
        return entity;
    }
}
