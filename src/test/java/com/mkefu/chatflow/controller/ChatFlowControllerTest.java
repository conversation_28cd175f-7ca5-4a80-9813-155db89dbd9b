package com.mkefu.chatflow.controller;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mkefu.chatflow.dto.*;
import com.mkefu.chatflow.entity.MessageObjectEntity;
import com.mkefu.common.constant.BaseCode;
import com.mkefu.common.constant.CustomConstant;
import com.mkefu.common.constant.ErrorCode;
import com.mkefu.common.entity.ChatFlowConfigEntity;
import com.mkefu.common.exception.BusinessException;
import com.mkefu.common.util.SignatureUtil;
import com.mkefu.system.entity.TenantAuthInfoEntity;
import com.mkefu.system.service.impl.TenantAuthInfoServiceImpl;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> Yang 杨国锋
 * @since 2025-05-06
 */
@Slf4j
@SpringBootTest
@Schema(description = "对话流控制层测试类")
class ChatFlowControllerTest {

    private final TenantAuthInfoServiceImpl tenantAuthInfoServiceImpl;

    private final SignatureUtil signatureUtil;

    private final ObjectMapper objectMapper;
    
    private final ChatFlowConfigEntity chatFlowConfigEntity;

    private static String chatId = "";

    private static String conversationId = "";
    
    private static final String DIFY_AUTHORIZATION = "app-qt0a7YRnBe6UorddPHomsvpv";

    private final String timestamp = String.valueOf(System.currentTimeMillis() / 1000);

    private static String messageId = "";

    private static final String OFFICIAL = "official";

    private static final String TENANT_GROUP_A = "tenant_group_a";

    private static final String TENANT_GROUP_B = "tenant_group_b";

    @Autowired
    public ChatFlowControllerTest(TenantAuthInfoServiceImpl tenantAuthInfoServiceImpl,
                                  SignatureUtil signatureUtil,
                                  ObjectMapper objectMapper, ChatFlowConfigEntity chatFlowConfigEntity) {
        this.tenantAuthInfoServiceImpl = tenantAuthInfoServiceImpl;
        this.signatureUtil = signatureUtil;
        this.objectMapper = objectMapper;
        this.chatFlowConfigEntity = chatFlowConfigEntity;
    }

    private final TenantAuthInfoEntity tenantAuthInfoSecretEntity = new TenantAuthInfoEntity();

    private final TenantAuthInfoEntity tenantAuthCodeInfoEntity = new TenantAuthInfoEntity();

    @BeforeEach
    void setUp() {
        // 生成接口私钥
        tenantAuthInfoSecretEntity.setTenantId("L6666");
        tenantAuthInfoSecretEntity.setTenantName("L6666");
        TenantAuthInfoEntity authCodeEntity = tenantAuthInfoServiceImpl.generateMethodSecretKey(tenantAuthInfoSecretEntity);
        tenantAuthInfoSecretEntity.setAuthCode(authCodeEntity.getAuthCode());

        tenantAuthCodeInfoEntity.setTenantId(tenantAuthInfoSecretEntity.getTenantId());
        tenantAuthCodeInfoEntity.setTenantName(tenantAuthInfoSecretEntity.getTenantName());
        tenantAuthCodeInfoEntity.setAgentId("7482956231146684428");
        tenantAuthCodeInfoEntity.setAgentName("智能体1");
    }

    @Test
    void CozeChatMessages() {
        CozeChatRequestDto cozeChatRequestDto = getCozeChatRequestDto();
        // 生成签名
        String signature = generateSignature(cozeChatRequestDto, timestamp, tenantAuthInfoSecretEntity.getTenantId());
        try {
            HttpRequest chatRequest = HttpRequest.post("http://localhost:8080/api/chatflow/chatMessages")
                    .header(CustomConstant.HEADER_X_TIMESTAMP, timestamp)
                    .header(CustomConstant.HEADER_X_SIGNATURE, signature)
                    .header(CustomConstant.HEADER_X_APITYPE, "official")
                    .header(CustomConstant.HEADER_AUTHORIZATION, chatFlowConfigEntity.getCozeAuthorization())
                    .header(CustomConstant.HEADER_X_PLATFORMTYPE, "coze")
                    .header(CustomConstant.HEADER_X_TENANTENTITY, objectMapper.writeValueAsString(tenantAuthCodeInfoEntity))
                    .body(objectMapper.writeValueAsString(cozeChatRequestDto));
            log.info("请求参数：{}", objectMapper.writeValueAsString(cozeChatRequestDto));
            try (HttpResponse response = chatRequest.execute()) {
                Assertions.assertTrue(response.isOk(), "调用COZE官方API失败，" + response.body());
                String body = response.body();
                log.info("调用COZE官方API成功，输出内容为：{}", body);
                JsonNode jsonNode = objectMapper.readTree(body);
                JsonNode dataNode = jsonNode.at("/data");
                CozeMessageResponseDto dto = objectMapper.treeToValue(dataNode, CozeMessageResponseDto.class);
                List<MessageObjectEntity> answers = dto.getData().stream()
                        .filter(item -> "answer".equals(item.getType()))
                        .toList();
                chatId = answers.getFirst().getId();
                conversationId = answers.getFirst().getConversationId();
            }
        } catch (Exception e) {
            throw new RuntimeException(e.getMessage());
        }
    }

    @Test
    void CozeGetMessages() {
        // 调用查看对话消息详情获取输出内容
        Map<String, Object> requestParams = HashMap.newHashMap(2);
        requestParams.put("chatId", chatId);
        requestParams.put("conversationId", conversationId);
        // 生成签名
        String signature = generateSignature(requestParams, timestamp, tenantAuthInfoSecretEntity.getTenantId());
        try {
            HttpRequest messageRequest = HttpRequest.get("http://localhost:8080/api/chatflow/getMessages")
                    .header(CustomConstant.HEADER_X_TIMESTAMP, timestamp)
                    .header(CustomConstant.HEADER_X_SIGNATURE, signature)
                    .header(CustomConstant.HEADER_X_APITYPE, "official")
                    .header(CustomConstant.HEADER_AUTHORIZATION, chatFlowConfigEntity.getCozeAuthorization())
                    .header(CustomConstant.HEADER_X_PLATFORMTYPE, "coze")
                    .header(CustomConstant.HEADER_X_TENANTENTITY, objectMapper.writeValueAsString(tenantAuthCodeInfoEntity))
                    .form(requestParams);
            try (HttpResponse messageResponse = messageRequest.execute()) {
                Assertions.assertTrue(messageResponse.isOk(), "调用COZE官方API失败，" + messageResponse.body());
                String body = messageResponse.body();
                log.info("调用COZE官方API成功，输出内容为：{}", body);
            }
        } catch (Exception e) {
            throw new BusinessException(ErrorCode.COZE_ERROR, e.getMessage());
        }
    }

    /**
     * 发送消息-SSE流
     */
    @Test
    void CozeChatMessagesStream() {
        CozeChatRequestDto cozeChatRequestDto = getCozeChatRequestDtoStream();
        // 生成签名
        String signature = generateSignature(cozeChatRequestDto, timestamp, tenantAuthInfoSecretEntity.getTenantId());
        try {
            String requestBody = objectMapper.writeValueAsString(cozeChatRequestDto);
            HttpRequest request = HttpRequest.post("http://localhost:8080/api/chatflow/chatMessages")
                    .header(CustomConstant.HEADER_X_TIMESTAMP, timestamp)
                    .header(CustomConstant.HEADER_X_SIGNATURE, signature)
                    .header(CustomConstant.HEADER_X_APITYPE, "official")
                    .header(CustomConstant.HEADER_AUTHORIZATION, chatFlowConfigEntity.getCozeAuthorization())
                    .header(CustomConstant.HEADER_X_PLATFORMTYPE, "coze")
                    .header(CustomConstant.HEADER_X_TENANTENTITY, objectMapper.writeValueAsString(tenantAuthCodeInfoEntity))
                    .body(requestBody);
            try (HttpResponse httpResponse = request.execute()) {
                String body = httpResponse.body();
                log.info(body);
            }
        } catch (Exception e) {
            throw new RuntimeException(e.getMessage());
        }
    }

    @Test
    void cozeCancelChat() {
        CommonCancelChatRequestDto requestDto = new CommonCancelChatRequestDto();
        requestDto.setChatId(chatId);
        requestDto.setConversationId(conversationId);
        // 生成签名
        String signature = generateSignature(requestDto, timestamp, tenantAuthInfoSecretEntity.getTenantId());
        try {
            HttpRequest request = HttpRequest
                    .post("http://localhost:8080/api/chatflow/cancelChat")
                    .header(CustomConstant.HEADER_X_TIMESTAMP, timestamp)
                    .header(CustomConstant.HEADER_X_SIGNATURE, signature)
                    .header(CustomConstant.HEADER_X_APITYPE, "official")
                    .header(CustomConstant.HEADER_AUTHORIZATION, chatFlowConfigEntity.getCozeAuthorization())
                    .header(CustomConstant.HEADER_X_PLATFORMTYPE, "coze")
                    .header(CustomConstant.HEADER_X_TENANTENTITY, objectMapper.writeValueAsString(tenantAuthCodeInfoEntity))
                    .body(objectMapper.writeValueAsString(requestDto));
            try (HttpResponse response = request.execute()) {
                Assertions.assertTrue(response.isOk(), "调用COZE官方API失败，" + response.body());
                String body = response.body();
                log.info("调用mkefu-chatflow-service API：{}，响应内容：{}", request.getUrl(), body);
                JsonNode dataNode = objectMapper.readTree(body).at("/data");
                CommonCancelChatResponseDto dto = objectMapper.treeToValue(dataNode, CommonCancelChatResponseDto.class);
                log.info("调用Jackson转换成对应实体类：{}", objectMapper.writeValueAsString(dto));
            }
        } catch (Exception e) {
            throw new RuntimeException(e.getMessage());
        }
    }

    @Test
    void getConversation() {
        Map<String, Object> paramsMap = HashMap.newHashMap(2);
        paramsMap.put("conversationId", conversationId);
        paramsMap.put("chatId", chatId);
        // 生成签名
        String signature = generateSignature(paramsMap, timestamp, tenantAuthInfoSecretEntity.getTenantId());
        try {
            HttpRequest request = HttpRequest
                    .get("http://localhost:8080/api/chatflow/getConversation")
                    .header(CustomConstant.HEADER_X_TIMESTAMP, timestamp)
                    .header(CustomConstant.HEADER_X_SIGNATURE, signature)
                    .header(CustomConstant.HEADER_X_APITYPE, "official")
                    .header(CustomConstant.HEADER_AUTHORIZATION, chatFlowConfigEntity.getCozeAuthorization())
                    .header(CustomConstant.HEADER_X_PLATFORMTYPE, "coze")
                    .header(CustomConstant.HEADER_X_TENANTENTITY, objectMapper.writeValueAsString(tenantAuthCodeInfoEntity))
                    .form(paramsMap);
            try (HttpResponse response = request.execute()) {
                Assertions.assertTrue(response.isOk(), "调用COZE官方API失败，" + response.body());
                String body = response.body();
                log.info("调用mkefu-chatflow-service API：{}，响应内容：{}", request.getUrl(), body);
                JsonNode dataNode = objectMapper.readTree(body).at("/data");
                CozeConversationResponseDto dto = objectMapper.treeToValue(dataNode, CozeConversationResponseDto.class);
                log.info("调用Jackson转换成对应实体类：{}", objectMapper.writeValueAsString(dto));
            }
        } catch (Exception e) {
            throw new RuntimeException(e.getMessage());
        }
    }

    @Test
    void createConversation() {
        CozeConversationRequestDto dto = new CozeConversationRequestDto();
        dto.setBotId("7482956231146684428");
        CozeConversationRequestDto.EnterMessage enterMessage = new CozeConversationRequestDto.EnterMessage();
        enterMessage.setRole("user");
        enterMessage.setContent("谷歌的官方网站是多少？");
        enterMessage.setContentType("text");
        List<CozeConversationRequestDto.EnterMessage> messages = List.of(enterMessage);
        dto.setMessages(messages);
        // 生成签名
        String signature = generateSignature(dto, timestamp, tenantAuthInfoSecretEntity.getTenantId());
        try {
            HttpRequest request = HttpRequest
                    .post("http://localhost:8080/api/chatflow/createConversation")
                    .header(CustomConstant.HEADER_X_TIMESTAMP, timestamp)
                    .header(CustomConstant.HEADER_X_SIGNATURE, signature)
                    .header(CustomConstant.HEADER_X_APITYPE, "official")
                    .header(CustomConstant.HEADER_AUTHORIZATION, chatFlowConfigEntity.getCozeAuthorization())
                    .header(CustomConstant.HEADER_X_PLATFORMTYPE, "coze")
                    .header(CustomConstant.HEADER_X_TENANTENTITY, objectMapper.writeValueAsString(tenantAuthCodeInfoEntity))
                    .body(objectMapper.writeValueAsString(dto));
            try (HttpResponse response = request.execute()) {
                Assertions.assertTrue(response.isOk(), "调用COZE官方API失败，" + response.body());
                String body = response.body();
                log.info("调用mkefu-chatflow-service：{}，响应内容：{}", request.getUrl(), body);
                JsonNode dataNode = objectMapper.readTree(body).at("/data");
                Map<String, Object> objectMap = objectMapper.treeToValue(dataNode, new TypeReference<>() {});
                Map<String, Object> dataMap = objectMapper.readValue(objectMapper.writeValueAsString(objectMap.get("data")), new TypeReference<>() {});
                log.info("调用Jackson序列化，结果：{}", dataMap);
                conversationId = String.valueOf(dataMap.get("id"));
            }
        } catch (Exception e) {
            throw new RuntimeException(e.getMessage());
        }
    }

    @Test
    void CozeGetConversationList() {
        Map<String, Object> params = Map.of("botId", "7482956231146684428");
        // 生成签名
        String signature = generateSignature(params, timestamp, tenantAuthInfoSecretEntity.getTenantId());
        try {
            HttpRequest request = HttpRequest
                    .get("http://localhost:8080/api/chatflow/getConversationList")
                    .header(CustomConstant.HEADER_X_TIMESTAMP, timestamp)
                    .header(CustomConstant.HEADER_X_SIGNATURE, signature)
                    .header(CustomConstant.HEADER_X_APITYPE, "official")
                    .header(CustomConstant.HEADER_AUTHORIZATION, chatFlowConfigEntity.getCozeAuthorization())
                    .header(CustomConstant.HEADER_X_PLATFORMTYPE, "coze")
                    .header(CustomConstant.HEADER_X_TENANTENTITY, objectMapper.writeValueAsString(tenantAuthCodeInfoEntity))
                    .form(params);
            try (HttpResponse response = request.execute()) {
                Assertions.assertTrue(response.isOk(), "调用COZE官方API失败，" + response.body());
                String body = response.body();
                log.info("调用mkefu-chatflow-service：{}，响应内容：{}", request.getUrl(), body);
                JsonNode dataNode = objectMapper.readTree(body).at("/data");
                CozeConversationListResponseDto dto = objectMapper.treeToValue(dataNode, CozeConversationListResponseDto.class);
                log.info("调用Jackson序列化，结果：{}", objectMapper.writeValueAsString(dto));
                CozeConversationListResponseDto.ListConversationData data = dto.getData();
                List<String> conversationIds = new ArrayList<>();
                for (CozeConversationListResponseDto.ConversationData conversation : data.getConversations()) {
                    conversationIds.add(conversation.getId());
                }
                log.info("智能体ID：{}下的会话：{}", "7482956231146684428", objectMapper.writeValueAsString(conversationIds));
            }
        } catch (Exception e) {
            throw new RuntimeException(e.getMessage());
        }
    }

    @Test
    void getConversationInfo() {
        Map<String, Object> params = Map.of("conversationId", "7502284399082291235");
        // 生成签名
        String signature = generateSignature(params, timestamp, tenantAuthInfoSecretEntity.getTenantId());
        try {
            HttpRequest request = HttpRequest
                    .get("http://localhost:8080/api/chatflow/getConversationInfo")
                    .header(CustomConstant.HEADER_X_TIMESTAMP, timestamp)
                    .header(CustomConstant.HEADER_X_SIGNATURE, signature)
                    .header(CustomConstant.HEADER_X_APITYPE, "official")
                    .header(CustomConstant.HEADER_AUTHORIZATION, chatFlowConfigEntity.getCozeAuthorization())
                    .header(CustomConstant.HEADER_X_PLATFORMTYPE, "coze")
                    .header(CustomConstant.HEADER_X_TENANTENTITY, objectMapper.writeValueAsString(tenantAuthCodeInfoEntity))
                    .form(params);
            try (HttpResponse response = request.execute()) {
                Assertions.assertTrue(response.isOk(), "调用COZE官方API失败，" + response.body());
                String body = response.body();
                log.info("调用mkefu-chatflow-service：{}，响应内容：{}", request.getUrl(), body);
                JsonNode dataNode = objectMapper.readTree(body).at("/data");
                log.info("调用Jackson序列化，结果：{}", objectMapper.writeValueAsString(dataNode));
            }
        } catch (Exception e) {
            throw new RuntimeException(e.getMessage());
        }
    }

    @Test
    void cleanContext4() {
        Map<String, Object> params = Map.of("conversationId", "7502284399082291235");
        // 生成签名
        String signature = generateSignature(params, timestamp, tenantAuthInfoSecretEntity.getTenantId());
        try {
            HttpRequest request = HttpRequest
                    .get("http://localhost:8080/api/chatflow/cleanContext")
                    .header(CustomConstant.HEADER_X_TIMESTAMP, timestamp)
                    .header(CustomConstant.HEADER_X_SIGNATURE, signature)
                    .header(CustomConstant.HEADER_X_APITYPE, "official")
                    .header(CustomConstant.HEADER_AUTHORIZATION, chatFlowConfigEntity.getCozeAuthorization())
                    .header(CustomConstant.HEADER_X_PLATFORMTYPE, "coze")
                    .header(CustomConstant.HEADER_X_TENANTENTITY, objectMapper.writeValueAsString(tenantAuthCodeInfoEntity))
                    .form(params);
            try (HttpResponse response = request.execute()) {
                Assertions.assertTrue(response.isOk(), "调用COZE官方API失败，" + response.body());
                String body = response.body();
                log.info("调用mkefu-chatflow-service：{}，响应内容：{}", request.getUrl(), body);
                JsonNode dataNode = objectMapper.readTree(body).at("/data");
                CozeCleanContextResponseDto dto = objectMapper.treeToValue(dataNode, CozeCleanContextResponseDto.class);
                log.info("调用Jackson序列化，结果：{}", objectMapper.writeValueAsString(dto));
            }
        } catch (Exception e) {
            throw new RuntimeException(e.getMessage());
        }
    }

    @Test
    void createMessage5() {
        Map<String, Object> map = Map.of(
                "conversation_id", "7502284399082291235",
                "role", "user",
                "content", "百度的IP地址是多少？",
                "content_type", "text"
        );
        // 生成签名
        String signature = generateSignature(map, timestamp, tenantAuthInfoSecretEntity.getTenantId());
        try {
            HttpRequest request = HttpRequest
                    .post("http://localhost:8080/api/chatflow/createMessage")
                    .header(CustomConstant.HEADER_X_TIMESTAMP, timestamp)
                    .header(CustomConstant.HEADER_X_SIGNATURE, signature)
                    .header(CustomConstant.HEADER_X_APITYPE, "official")
                    .header(CustomConstant.HEADER_AUTHORIZATION, chatFlowConfigEntity.getCozeAuthorization())
                    .header(CustomConstant.HEADER_X_PLATFORMTYPE, "coze")
                    .header(CustomConstant.HEADER_X_TENANTENTITY, objectMapper.writeValueAsString(tenantAuthCodeInfoEntity))
                    .body(objectMapper.writeValueAsString(map));
            try (HttpResponse response = request.execute()) {
                Assertions.assertTrue(response.isOk(), "调用COZE官方API失败，" + response.body());
                String body = response.body();
                log.info("调用mkefu-chatflow-service：{}，响应内容：{}", request.getUrl(), body);
                JsonNode dataNode = objectMapper.readTree(body).at("/data");
                CozeCreateMessageResponseDto dto = objectMapper.treeToValue(dataNode, CozeCreateMessageResponseDto.class);
                log.info("调用Jackson序列化，结果：{}", objectMapper.writeValueAsString(dto));
            }
        } catch (Exception e) {
            throw new RuntimeException(e.getMessage());
        }
    }

    @Test
    void getMessageList() {
        Map<String, Object> map = Map.of(
                "conversation_id", "7502284399082291235"
        );
        // 生成签名
        String signature = generateSignature(map, timestamp, tenantAuthInfoSecretEntity.getTenantId());
        try {
            HttpRequest request = HttpRequest
                    .post("http://localhost:8080/api/chatflow/getMessageList")
                    .header(CustomConstant.HEADER_X_TIMESTAMP, timestamp)
                    .header(CustomConstant.HEADER_X_SIGNATURE, signature)
                    .header(CustomConstant.HEADER_X_APITYPE, "official")
                    .header(CustomConstant.HEADER_AUTHORIZATION, chatFlowConfigEntity.getCozeAuthorization())
                    .header(CustomConstant.HEADER_X_PLATFORMTYPE, "coze")
                    .header(CustomConstant.HEADER_X_TENANTENTITY, objectMapper.writeValueAsString(tenantAuthCodeInfoEntity))
                    .body(objectMapper.writeValueAsString(map));
            try (HttpResponse response = request.execute()) {
                Assertions.assertTrue(response.isOk(), "调用COZE官方API失败，" + response.body());
                String body = response.body();
                log.info("调用mkefu-chatflow-service：{}，响应内容：{}", request.getUrl(), body);
                JsonNode dataNode = objectMapper.readTree(body).at("/data");
                CozeGetMessageListResponseDto dto = objectMapper.treeToValue(dataNode, CozeGetMessageListResponseDto.class);
                log.info("调用Jackson序列化，结果：{}", objectMapper.writeValueAsString(dto));
            }
        } catch (Exception e) {
            throw new RuntimeException(e.getMessage());
        }
    }

    @Test
    void getMessageInfo() {
        Map<String, Object> map = Map.of(
                "conversationId", "7502284399082291235",
                "messageId", "7502327838406819859"
        );
        // 生成签名
        String signature = generateSignature(map, timestamp, tenantAuthInfoSecretEntity.getTenantId());
        try {
            HttpRequest request = HttpRequest
                    .get("http://localhost:8080/api/chatflow/getMessageInfo")
                    .header(CustomConstant.HEADER_X_TIMESTAMP, timestamp)
                    .header(CustomConstant.HEADER_X_SIGNATURE, signature)
                    .header(CustomConstant.HEADER_X_APITYPE, "official")
                    .header(CustomConstant.HEADER_AUTHORIZATION, chatFlowConfigEntity.getCozeAuthorization())
                    .header(CustomConstant.HEADER_X_PLATFORMTYPE, "coze")
                    .header(CustomConstant.HEADER_X_TENANTENTITY, objectMapper.writeValueAsString(tenantAuthCodeInfoEntity))
                    .form(map);
            try (HttpResponse response = request.execute()) {
                Assertions.assertTrue(response.isOk(), "调用COZE官方API失败，" + response.body());
                String body = response.body();
                log.info("调用mkefu-chatflow-service：{}，响应内容：{}", request.getUrl(), body);
                JsonNode dataNode = objectMapper.readTree(body).at("/data");
                CozeGetMessageInfoResponseDto dto = objectMapper.treeToValue(dataNode, CozeGetMessageInfoResponseDto.class);
                log.info("调用Jackson序列化，结果：{}", objectMapper.writeValueAsString(dto));
            }
        } catch (Exception e) {
            throw new RuntimeException(e.getMessage());
        }
    }

    @Test
    void updateMessageInfo() {
        Map<String, Object> map = Map.of(
                "conversationId", "7502284399082291235",
                "messageId", "7502327838406819859",
                "content", "谷歌的IP地址是多少？",
                "content_type", "text"
        );
        // 生成签名
        String signature = generateSignature(map, timestamp, tenantAuthInfoSecretEntity.getTenantId());
        try {
            HttpRequest request = HttpRequest
                    .post("http://localhost:8080/api/chatflow/updateMessageInfo")
                    .header(CustomConstant.HEADER_X_TIMESTAMP, timestamp)
                    .header(CustomConstant.HEADER_X_SIGNATURE, signature)
                    .header(CustomConstant.HEADER_X_APITYPE, "official")
                    .header(CustomConstant.HEADER_AUTHORIZATION, chatFlowConfigEntity.getCozeAuthorization())
                    .header(CustomConstant.HEADER_X_PLATFORMTYPE, "coze")
                    .header(CustomConstant.HEADER_X_TENANTENTITY, objectMapper.writeValueAsString(tenantAuthCodeInfoEntity))
                    .body(objectMapper.writeValueAsString(map));
            try (HttpResponse response = request.execute()) {
                Assertions.assertTrue(response.isOk(), "调用COZE官方API失败，" + response.body());
                String body = response.body();
                log.info("调用mkefu-chatflow-service：{}，响应内容：{}", request.getUrl(), body);
                JsonNode dataNode = objectMapper.readTree(body).at("/data");
                CozeUpdateMessageInfoResponseDto dto = objectMapper.treeToValue(dataNode, CozeUpdateMessageInfoResponseDto.class);
                log.info("调用Jackson序列化，结果：{}", objectMapper.writeValueAsString(dto));
            }
        } catch (Exception e) {
            throw new RuntimeException(e.getMessage());
        }
    }

    @Test
    void deleteMessageInfo() {
        Map<String, Object> map = Map.of(
                "conversationId", "7502284399082291235",
                "messageId", "7502327838406819859"
        );
        // 生成签名
        String signature = generateSignature(map, timestamp, tenantAuthInfoSecretEntity.getTenantId());
        try {
            HttpRequest request = HttpRequest
                    .post("http://localhost:8080/api/chatflow/deleteMessageInfo")
                    .header(CustomConstant.HEADER_X_TIMESTAMP, timestamp)
                    .header(CustomConstant.HEADER_X_SIGNATURE, signature)
                    .header(CustomConstant.HEADER_X_APITYPE, "official")
                    .header(CustomConstant.HEADER_AUTHORIZATION, chatFlowConfigEntity.getCozeAuthorization())
                    .header(CustomConstant.HEADER_X_PLATFORMTYPE, "coze")
                    .header(CustomConstant.HEADER_X_TENANTENTITY, objectMapper.writeValueAsString(tenantAuthCodeInfoEntity))
                    .body(objectMapper.writeValueAsString(map));
            try (HttpResponse response = request.execute()) {
                Assertions.assertTrue(response.isOk(), "调用COZE官方API失败，" + response.body());
                String body = response.body();
                log.info("调用mkefu-chatflow-service：{}，响应内容：{}", request.getUrl(), body);
                JsonNode dataNode = objectMapper.readTree(body).at("/data");
                CozeDeleteMessageInfoResponseDto dto = objectMapper.treeToValue(dataNode, CozeDeleteMessageInfoResponseDto.class);
                log.info("调用Jackson序列化，结果：{}", objectMapper.writeValueAsString(dto));
            }
        } catch (Exception e) {
            throw new RuntimeException(e.getMessage());
        }
    }

    private CozeChatRequestDto getCozeChatRequestDtoStream() {
        CozeChatRequestDto cozeChatRequestDto = new CozeChatRequestDto();
        cozeChatRequestDto.setBotId("7482956231146684428");
        cozeChatRequestDto.setUserId("testUser01");
        cozeChatRequestDto.setStream(true);
        List<CozeChatRequestDto.AdditionalMessage> additionalMessages = cozeChatRequestDto.getAdditionalMessages();
        CozeChatRequestDto.AdditionalMessage additionalMessage = new CozeChatRequestDto.AdditionalMessage();
        additionalMessage.setContent("犇犇犇犇犇犇犇");
        additionalMessage.setContentType("text");
        additionalMessage.setRole("user");
        additionalMessages.add(additionalMessage);
        return cozeChatRequestDto;
    }

    private static CozeChatRequestDto getCozeChatRequestDto() {
        CozeChatRequestDto cozeChatRequestDto = new CozeChatRequestDto();
        cozeChatRequestDto.setConversationId("7509082236692758537");
        cozeChatRequestDto.setBotId("7482956231146684428");
        cozeChatRequestDto.setUserId("testUser01");
        cozeChatRequestDto.setStream(false);
        cozeChatRequestDto.setAutoSaveHistory(true);
        List<CozeChatRequestDto.AdditionalMessage> additionalMessages = cozeChatRequestDto.getAdditionalMessages();
        CozeChatRequestDto.AdditionalMessage additionalMessage = new CozeChatRequestDto.AdditionalMessage();
        additionalMessage.setContent("hello1231321321");
        additionalMessage.setContentType("text");
        additionalMessage.setRole("user");
        additionalMessages.add(additionalMessage);
        return cozeChatRequestDto;
    }

    /**
     * 生成签名
     * @param object 对象
     * @param timestamp 时间戳
     * @param tenantId 租户ID
     * @return 签名
     */
    private String generateSignature(Object object, String timestamp, String tenantId) {
        try {
            return signatureUtil.generateSignature(object, timestamp, tenantId);
        } catch (Exception e) {
            throw new RuntimeException(e.getMessage());
        }
    }

    @Test
    void DifyChatMessages() {
        // 模拟调用chatMessages接口
        DifyChatRequestDto difyChatRequestDto = new DifyChatRequestDto();
        difyChatRequestDto.setUser("小美");
        difyChatRequestDto.setQuery("nihao");
        difyChatRequestDto.setAutoGenerateName(true);
        difyChatRequestDto.setResponseMode(BaseCode.BLOCKING.getCode());
        // 生成签名
        try {
            // 生成签名
            String signature = generateSignature(difyChatRequestDto, timestamp, tenantAuthInfoSecretEntity.getTenantId());
            HttpRequest request = HttpRequest.post("http://localhost:8080/api/chatflow/chatMessages")
                    .header(CustomConstant.HEADER_X_TIMESTAMP, timestamp)
                    .header(CustomConstant.HEADER_X_SIGNATURE, signature)
                    .header(CustomConstant.HEADER_AUTHORIZATION, chatFlowConfigEntity.getDifyAuthorization())
                    .header(CustomConstant.HEADER_X_APITYPE, OFFICIAL)
                    .header(CustomConstant.HEADER_X_PLATFORMTYPE, "dify")
                    .header(CustomConstant.HEADER_X_TENANTENTITY, objectMapper.writeValueAsString(tenantAuthCodeInfoEntity))
                    .body(objectMapper.writeValueAsString(difyChatRequestDto));
            try (HttpResponse response = request.execute()) {
                Assertions.assertTrue(response.isOk(), ErrorCode.DIFY_SERVER_ERROR.getDefaultMessage());
                JsonNode jsonNode = objectMapper.readTree(response.body());
                messageId = jsonNode.get("data").get("message_id").asText();
                conversationId = jsonNode.get("data").get("conversation_id").asText();
                System.out.println(messageId);
            }
        } catch (Exception e) {
            throw new RuntimeException(e.getMessage());
        }
    }

    @Test
    void DifyChatMessagesStream() {
        // 模拟调用chatMessages接口
        DifyChatRequestDto difyChatRequestDto = new DifyChatRequestDto();
        difyChatRequestDto.setUser("YangGuofeng");
        difyChatRequestDto.setQuery("hfihekfhsdkfsd");
        difyChatRequestDto.setAutoGenerateName(true);
        difyChatRequestDto.setResponseMode(BaseCode.STREAMING.getCode());
        try {
            // 生成签名
            String signature = generateSignature(difyChatRequestDto, timestamp, tenantAuthInfoSecretEntity.getTenantId());
            HttpRequest request = HttpRequest.post("http://localhost:8080/api/chatflow/chatMessages")
                    .header(CustomConstant.HEADER_X_TIMESTAMP, timestamp)
                    .header(CustomConstant.HEADER_X_SIGNATURE, signature)
                    .header(CustomConstant.HEADER_AUTHORIZATION, chatFlowConfigEntity.getDifyAuthorization())
                    .header(CustomConstant.HEADER_X_APITYPE, OFFICIAL)
                    .header(CustomConstant.HEADER_X_PLATFORMTYPE, "dify")
                    .header(CustomConstant.HEADER_X_TENANTENTITY, objectMapper.writeValueAsString(tenantAuthCodeInfoEntity))
                    .body(objectMapper.writeValueAsString(difyChatRequestDto));
            try (HttpResponse response = request.execute()) {
                Assertions.assertTrue(response.isOk(), ErrorCode.DIFY_SERVER_ERROR.getDefaultMessage());
                log.info(response.body());
            }
        } catch (Exception e) {
            throw new RuntimeException(e.getMessage());
        }
    }

    @Test
    void getSuggested() {
        // 模拟调用getSuggested接口
        DifySuggestedRequestDto dto = new DifySuggestedRequestDto();
        dto.setUser("小美");
        dto.setMessageId(messageId);

        try {
            // 生成签名
            String signature = generateSignature(dto, timestamp, tenantAuthInfoSecretEntity.getTenantId());
            HttpRequest request = HttpRequest.post("http://localhost:8080/api/chatflow/getSuggested")
                    .header(CustomConstant.HEADER_X_TIMESTAMP, timestamp)
                    .header(CustomConstant.HEADER_X_SIGNATURE, signature)
                    .header(CustomConstant.HEADER_AUTHORIZATION, chatFlowConfigEntity.getDifyAuthorization())
                    .header(CustomConstant.HEADER_X_APITYPE, OFFICIAL)
                    .header(CustomConstant.HEADER_X_PLATFORMTYPE, "dify")
                    .header(CustomConstant.HEADER_X_TENANTENTITY, objectMapper.writeValueAsString(tenantAuthCodeInfoEntity))
                    .body(objectMapper.writeValueAsString(dto));
            try (HttpResponse response = request.execute()) {
                Assertions.assertTrue(response.isOk(), ErrorCode.DIFY_SERVER_ERROR.getDefaultMessage());
                JsonNode jsonNode = objectMapper.readTree(response.body());
                JsonNode arryNode = jsonNode.get("data").get("data");
                List<String> suggestedList = objectMapper.readValue(arryNode.traverse(), new TypeReference<>() {});
                System.out.println(suggestedList);
            }
        } catch (Exception e) {
            throw new BusinessException(ErrorCode.DIFY_SERVER_ERROR, e.getMessage());
        }
    }

    @Test
    void getConversationList() {
        Map<String, Object> params = HashMap.newHashMap(4);
        params.put("user", "小美");
        params.put("sortBy", "-created_at");
        try {
            // 生成签名
            String signature = generateSignature(params, timestamp, tenantAuthInfoSecretEntity.getTenantId());
            HttpRequest request = HttpRequest
                    .post("http://localhost:8080/api/chatflow/getConversationList")
                    .header(CustomConstant.HEADER_X_TIMESTAMP, timestamp)
                    .header(CustomConstant.HEADER_X_SIGNATURE, signature)
                    .header(CustomConstant.HEADER_AUTHORIZATION, chatFlowConfigEntity.getDifyAuthorization())
                    .header(CustomConstant.HEADER_X_APITYPE, OFFICIAL)
                    .header(CustomConstant.HEADER_X_PLATFORMTYPE, "dify")
                    .header(CustomConstant.HEADER_X_TENANTENTITY, objectMapper.writeValueAsString(tenantAuthCodeInfoEntity))
                    .body(objectMapper.writeValueAsString(params));
            try (HttpResponse response = request.execute()) {
                Assertions.assertTrue(response.isOk(), ErrorCode.DIFY_SERVER_ERROR.getDefaultMessage());
                JsonNode jsonNode = objectMapper.readTree(response.body());
                JsonNode atData = jsonNode.at("/data");
                DifyGetConversationListResponseDto dto = objectMapper.treeToValue(atData, DifyGetConversationListResponseDto.class);
                log.info("返回结果：{}", objectMapper.writeValueAsString(dto));
            }
        } catch (Exception e) {
            throw new BusinessException(ErrorCode.DIFY_SERVER_ERROR, e.getMessage());
        }
    }

    @Test
    void DifyGetMessages() {
        Map<String, Object> params = HashMap.newHashMap(2);
        params.put("conversationId", conversationId);
        params.put("user", "杨国锋");
        // 生成签名
        String signature = generateSignature(params, timestamp, tenantAuthInfoSecretEntity.getTenantId());
        try {
            HttpRequest request = HttpRequest
                    .post("http://localhost:8080/api/chatflow/getMessages")
                    .header(CustomConstant.HEADER_X_TIMESTAMP, timestamp)
                    .header(CustomConstant.HEADER_X_SIGNATURE, signature)
                    .header(CustomConstant.HEADER_AUTHORIZATION, chatFlowConfigEntity.getDifyAuthorization())
                    .header(CustomConstant.HEADER_X_APITYPE, OFFICIAL)
                    .header(CustomConstant.HEADER_X_PLATFORMTYPE, "dify")
                    .header(CustomConstant.HEADER_X_TENANTENTITY, objectMapper.writeValueAsString(tenantAuthCodeInfoEntity))
                    .body(objectMapper.writeValueAsString(params));
            try (HttpResponse response = request.execute()) {
                Assertions.assertTrue(response.isOk(), ErrorCode.DIFY_SERVER_ERROR.getDefaultMessage());
                JsonNode jsonNode = objectMapper.readTree(response.body());
                JsonNode atData = jsonNode.at("/data");
                DifyGetMessagesHistoryResponseDto dto = objectMapper.treeToValue(atData, DifyGetMessagesHistoryResponseDto.class);
                log.info("返回结果：{}", objectMapper.writeValueAsString(dto));
            }
        } catch (Exception e) {
            throw new BusinessException(ErrorCode.DIFY_SERVER_ERROR, e.getMessage());
        }
    }

    @Test
    void deleteConversationInfo() {
        Map<String, Object> params = HashMap.newHashMap(2);
        params.put("conversationId", "885dd85f-1f1d-4cb0-9cfd-0e835ccc76cf");
        params.put("user", "杨国锋");
        // 生成签名
        String signature = generateSignature(params, timestamp, tenantAuthInfoSecretEntity.getTenantId());
        try {
            HttpRequest request = HttpRequest
                    .post("http://localhost:8080/api/chatflow/deleteConversationInfo")
                    .header(CustomConstant.HEADER_X_TIMESTAMP, timestamp)
                    .header(CustomConstant.HEADER_X_SIGNATURE, signature)
                    .header(CustomConstant.HEADER_AUTHORIZATION, chatFlowConfigEntity.getDifyAuthorization())
                    .header(CustomConstant.HEADER_X_APITYPE, OFFICIAL)
                    .header(CustomConstant.HEADER_X_PLATFORMTYPE, "dify")
                    .header(CustomConstant.HEADER_X_TENANTENTITY, objectMapper.writeValueAsString(tenantAuthCodeInfoEntity))
                    .body(objectMapper.writeValueAsString(params));
            try (HttpResponse response = request.execute()) {
                Assertions.assertTrue(response.isOk(), ErrorCode.DIFY_SERVER_ERROR.getDefaultMessage());
                JsonNode jsonNode = objectMapper.readTree(response.body());
                JsonNode atData = jsonNode.at("/data");
                log.info("返回结果：{}", objectMapper.writeValueAsString(atData));
            }
        } catch (Exception e) {
            throw new BusinessException(ErrorCode.DIFY_SERVER_ERROR, e.getMessage());
        }
    }

    @Test
    void cozeGetAgentInfo() {
        // 生成签名
        String signature = generateSignature(tenantAuthCodeInfoEntity, timestamp, tenantAuthInfoSecretEntity.getTenantId());
        try {
            HttpRequest request = HttpRequest
                    .get("http://localhost:8080/api/chatflow/getAgentInfo")
                    .header(CustomConstant.HEADER_X_TIMESTAMP, timestamp)
                    .header(CustomConstant.HEADER_X_SIGNATURE, signature)
                    .header(CustomConstant.HEADER_AUTHORIZATION, chatFlowConfigEntity.getCozeAuthorization())
                    .header(CustomConstant.HEADER_X_APITYPE, OFFICIAL)
                    .header(CustomConstant.HEADER_X_PLATFORMTYPE, "coze")
                    .header(CustomConstant.HEADER_X_TENANTENTITY, objectMapper.writeValueAsString(tenantAuthCodeInfoEntity));
            try (HttpResponse response = request.execute()) {
                Assertions.assertTrue(response.isOk(), ErrorCode.COZE_ERROR.getDefaultMessage());
                JsonNode jsonNode = objectMapper.readTree(response.body());
                JsonNode atData = jsonNode.at("/data");
                log.info("返回结果：{}", objectMapper.writeValueAsString(atData));
            }
        } catch (Exception e) {
            throw new BusinessException(ErrorCode.COZE_ERROR, e.getMessage());
        }
    }

    @Test
    void difyGetAgentInfo() {
        String str = "Authorization=" + DIFY_AUTHORIZATION;
        // 生成签名
        String signature = generateSignature(str, timestamp, tenantAuthInfoSecretEntity.getTenantId());
        try {
            HttpRequest request = HttpRequest
                    .get("http://localhost:8080/api/chatflow/getAgentInfo")
                    .header(CustomConstant.HEADER_X_TIMESTAMP, timestamp)
                    .header(CustomConstant.HEADER_X_SIGNATURE, signature)
                    .header(CustomConstant.HEADER_AUTHORIZATION, chatFlowConfigEntity.getDifyAuthorization())
                    .header(CustomConstant.HEADER_X_APITYPE, OFFICIAL)
                    .header(CustomConstant.HEADER_X_PLATFORMTYPE, "dify")
                    .header(CustomConstant.HEADER_X_TENANTENTITY, objectMapper.writeValueAsString(tenantAuthCodeInfoEntity));
            try (HttpResponse response = request.execute()) {
                Assertions.assertTrue(response.isOk(), ErrorCode.COZE_ERROR.getDefaultMessage());
                JsonNode jsonNode = objectMapper.readTree(response.body());
                JsonNode atData = jsonNode.at("/data");
                log.info("返回结果：{}", objectMapper.writeValueAsString(atData));
            }
        } catch (Exception e) {
            throw new BusinessException(ErrorCode.COZE_ERROR, e.getMessage());
        }
    }

    @Test
    void difyCancelChat() {
        CommonCancelChatRequestDto dto = new CommonCancelChatRequestDto();
        dto.setUserId("小美");
        dto.setChatId("***********");
        // 生成签名
        String signature = generateSignature(dto, timestamp, tenantAuthInfoSecretEntity.getTenantId());
        try {
            HttpRequest request = HttpRequest
                    .get("http://localhost:8080/api/chatflow/cancelChat")
                    .header(CustomConstant.HEADER_X_TIMESTAMP, timestamp)
                    .header(CustomConstant.HEADER_X_SIGNATURE, signature)
                    .header(CustomConstant.HEADER_AUTHORIZATION, chatFlowConfigEntity.getDifyAuthorization())
                    .header(CustomConstant.HEADER_X_APITYPE, OFFICIAL)
                    .header(CustomConstant.HEADER_X_PLATFORMTYPE, "dify")
                    .header(CustomConstant.HEADER_X_TENANTENTITY, objectMapper.writeValueAsString(tenantAuthCodeInfoEntity))
                    .body(objectMapper.writeValueAsString(dto));
            try (HttpResponse response = request.execute()) {
                Assertions.assertTrue(response.isOk(), ErrorCode.COZE_ERROR.getDefaultMessage());
                JsonNode jsonNode = objectMapper.readTree(response.body());
                JsonNode atData = jsonNode.at("/data");
                log.info("返回结果：{}", objectMapper.writeValueAsString(atData));
            }
        } catch (Exception e) {
            throw new BusinessException(ErrorCode.COZE_ERROR, e.getMessage());
        }
    }
}
