package com.mkefu.common.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 基础实体类
 * <AUTHOR>
 * @since 2025-04-10
 */
@Data
@Schema(description = "基础实体类")
public class BaseEntity implements Serializable {

    @TableField("create_time")
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @TableField("create_id")
    @Schema(description = "创建人ID")
    private String createId;

    @TableField("create_name")
    @Schema(description = "创建人名字")
    private String createName;

    @TableField("update_time")
    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    @TableField("update_id")
    @Schema(description = "更新人ID")
    private String updateId;

    @TableField("update_name")
    @Schema(description = "更新人名字")
    private String updateName;

    @TableField("del_flag")
    @Schema(description = "删除标志 0-已删除 1-未删除")
    private String delFlag;

    @TableField("remark")
    @Schema(description = "备注")
    private String remark;
}
