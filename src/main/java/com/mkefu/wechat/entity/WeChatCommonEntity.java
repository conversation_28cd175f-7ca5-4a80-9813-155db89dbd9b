package com.mkefu.wechat.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * 微信公共实体类
 * <AUTHOR> 杨国锋
 * @since 2025-06-18
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "微信公共实体类", description = "WeChatCommonEntity")
public class WeChatCommonEntity implements Serializable {
    
    @Serial
    private static final long serialVersionUID = 1L;
    
    @Schema(description = "错误码 0-正常")
    @JsonProperty("errcode")
    private Integer errorCode;
    
    @Schema(description = "错误信息")
    @JsonProperty("errmsg")
    private String errorMsg;
    
    @Schema(description = "有效期（秒）")
    @JsonProperty("expires_in")
    private Integer expiresIn;
}
