package com.mkefu.common.entity.query;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 通用的查询基类，供业务 Query 类继承
 * <AUTHOR>
 * @since 2025-04-10
 */
@Data
public class BaseQuery<T> {

    @Schema(description = "当前页码（分页）")
    private Long pageNum = 1L;

    @Schema(description = "每页数量（分页）")
    private Long pageSize = 10L;

    @Schema(description = "排序方向（true 为升序，false 为降序）")
    private Boolean asc = true;

    @Schema(description = "创建时间范围 - 开始")
    private LocalDateTime createTimeStart;

    @Schema(description = "创建时间范围 - 结束")
    private LocalDateTime createTimeEnd;

    @Schema(description = "状态（通用字段，0: 禁用, 1: 启用）")
    private String delFlag;

    /**
     * 转换为 MyBatis-Plus 的分页对象
     * @return IPage
     */
    public IPage<T> toPage() {
        return new Page<>(pageNum, pageSize);
    }

    /**
     * 转换为 MyBatis-Plus 的 QueryWrapper
     * 子类可以重写此方法，添加自定义查询条件
     * @return QueryWrapper
     */
    public QueryWrapper<T> toQueryWrapper() {
        return new QueryWrapper<>();
    }

    /**
     * 转换为 MyBatis-Plus 的 LambdaQueryWrapper
     * 子类可以重写此方法，添加自定义查询条件
     * @return LambdaQueryWrapper
     */
    public LambdaQueryWrapper<T> toLambdaQueryWrapper() {
        return new LambdaQueryWrapper<T>().apply(toQueryWrapper().getSqlSegment());
    }
}
