package com.mkefu.common.util;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mkefu.common.constant.CustomConstant;
import com.mkefu.common.constant.ErrorCode;
import com.mkefu.common.entity.ImServiceConfigEntity;
import com.mkefu.common.exception.BusinessException;
import com.mkefu.wechat.dto.WeChatMessageToImServiceRequestDto;
import com.mkefu.wechat.entity.FsMkefuServerInfoEntity;
import com.mkefu.wechat.entity.MkefuEnvInfoEntity;
import com.mkefu.wechat.mapper.FsMkefuServerInfoMapper;
import com.mkefu.wechat.mapper.MkefuEnvInfoMapper;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * mkefu-00-service负载均衡工具类
 *
 * <AUTHOR> Yang 杨国锋
 * @since 2025-07-15
 */
@Slf4j
@Component
public class MkefuImLoadBalancerUtil {
    
    private final MkefuEnvInfoMapper mkefuEnvInfoMapper;
    
    private final FsMkefuServerInfoMapper fsMkefuServerInfoMapper;
    
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    private final ImServiceConfigEntity imServiceConfigEntity;

    public MkefuImLoadBalancerUtil(MkefuEnvInfoMapper mkefuEnvInfoMapper, FsMkefuServerInfoMapper fsMkefuServerInfoMapper, ImServiceConfigEntity imServiceConfigEntity) {
        this.mkefuEnvInfoMapper = mkefuEnvInfoMapper;
        this.fsMkefuServerInfoMapper = fsMkefuServerInfoMapper;
        this.imServiceConfigEntity = imServiceConfigEntity;
    }

    private final Map<String, FsMkefuServerInfoEntity> nodesMap = new ConcurrentHashMap<>();

    /**
     * 初始化00Service的节点并存入JVM缓存
     */
    @PostConstruct
    public void initImService() {
        List<FsMkefuServerInfoEntity> fsMkefuServerInfoList = fsMkefuServerInfoMapper
                .selectList(new QueryWrapper<>());
        for (FsMkefuServerInfoEntity entity : fsMkefuServerInfoList) {
            nodesMap.put(entity.getShowName(), entity);
        }
        log.info("mkefu-im-service服务初始化完成，节点数量：{}", nodesMap.size());
    }
    
    /**
     * 根据租户ID，获取负载均衡的机器地址
     * @param dto 请求Im服务DTO对象
     */
    public void balanceByTenantId(WeChatMessageToImServiceRequestDto dto, String methodName) {
        List<String> showNameList = mkefuEnvInfoMapper
                .selectList(new QueryWrapper<MkefuEnvInfoEntity>()
                        .lambda()
                        .eq(MkefuEnvInfoEntity::getTenantId, dto.getTenantId()))
                .stream()
                .map(MkefuEnvInfoEntity::getShowName)
                .distinct()
                .toList();
        if (CollUtil.isNotEmpty(showNameList)) {
            String showName = showNameList.getFirst();
            switch (methodName) {
                case CustomConstant.SEND_TO_AGENT:
                    sendToAgent(dto, nodesMap.get(showName));
                    break;
                case CustomConstant.NOTICE_BIND_SUCCESS:
                    noticeBindSuccess(dto, nodesMap.get(showName));
                    break;
                default:
                    throw new BusinessException(ErrorCode.IM_SERVICE_METHOD_NAME_NULL);
            }
        }
    }

    /**
     * 授权绑定成功通知
     * @param dto 请求DTO
     * @param fsMkefuServerInfoEntity 不同ImService环境
     */
    private void noticeBindSuccess(WeChatMessageToImServiceRequestDto dto, FsMkefuServerInfoEntity fsMkefuServerInfoEntity) {
        try {
            String requestBody = objectMapper.writeValueAsString(dto);
            String serverUrl = fsMkefuServerInfoEntity.getServerUrl() + "/v1/wechat/sendToAgent";
            String encBody = AesEncryptionUtil.encryptAesString(requestBody, imServiceConfigEntity.getAesKey(), imServiceConfigEntity.getAesIv());
            log.info("加密前请求参数：{}", requestBody);
            log.info("请求mkefu-im-service服务，请求地址：{}，加密后请求参数：{}", serverUrl, encBody);
            HttpRequest request = HttpRequest
                    .post(serverUrl)
                    .header("Content-Type", "text/plain")
                    .body(encBody);
            try (HttpResponse response = request.execute()) {
                String responseBody = response.body();
                log.info("请求mkefu-im-service服务，响应结果：{}", responseBody);
            }
        } catch (Exception e) {
            log.error("请求mkefu-im-service服务失败", e);
        }
    }

    /**
     * 请求ImService
     * @param dto 请求DTO
     * @param fsMkefuServerInfoEntity 不同ImService环境
     */
    private void sendToAgent(WeChatMessageToImServiceRequestDto dto, FsMkefuServerInfoEntity fsMkefuServerInfoEntity) {
        try {
            String requestBody = objectMapper.writeValueAsString(dto);
            String serverUrl = fsMkefuServerInfoEntity.getServerUrl() + "/v1/wechat/sendToAgent";
            String encBody = AesEncryptionUtil.encryptAesString(requestBody, imServiceConfigEntity.getAesKey(), imServiceConfigEntity.getAesIv());
            log.info("加密前请求参数：{}", requestBody);
            log.info("请求mkefu-im-service服务，请求地址：{}，加密后请求参数：{}", serverUrl, encBody);
            HttpRequest request = HttpRequest
                    .post(serverUrl)
                    .header("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36")
                    .header("Content-Type", "text/plain")
                    .body(encBody);
            try (HttpResponse response = request.execute()) {
                String responseBody = response.body();
                log.info("请求mkefu-im-service服务，响应结果：{}", responseBody);
            }
        } catch (Exception e) {
            log.error("请求mkefu-im-service服务失败", e);
        }
    }
}
