package com.mkefu.common.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR> Yang 杨国锋
 * @since 2025-06-12
 */
@Data
@Configuration
@NoArgsConstructor
@AllArgsConstructor
@ConfigurationProperties(prefix = "chatflow-config")
public class ChatFlowConfigEntity {
    
    @Schema(description = "未匹配到知识库里的内容时的回复-中文")
    private String notMatchChinese;
    
    @Schema(description = "未匹配到知识库里的内容时的回复-中文")
    private String notMatchEnglish;

    @Schema(description = "Dify 授权令牌")
    private String difyAuthorization;
    
    @Schema(description = "Coze 授权令牌")
    private String cozeAuthorization;


    public boolean containsAnswer(String str) {
        Set<String> notMatchedAnswerMap = new HashSet<>();
        notMatchedAnswerMap.add(notMatchChinese);
        notMatchedAnswerMap.add(notMatchEnglish);
        return notMatchedAnswerMap.contains(str);
    }
}
