package com.mkefu.wechat.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.mkefu.common.constant.CustomConstant;
import com.mkefu.common.constant.ErrorCode;
import com.mkefu.common.entity.WeChatKfConfigEntity;
import com.mkefu.common.exception.BusinessException;
import com.mkefu.common.util.WXBizJsonMsgCrypt;
import com.mkefu.common.util.WXBizMsgCrypt;
import com.mkefu.common.util.XmlUtils;
import com.mkefu.wechat.entity.WeChatThirdPartyBaseInfoEntity;
import com.mkefu.wechat.service.WeChatAsyncService;
import com.mkefu.wechat.service.WeChatKfCallBackService;
import com.mkefu.wechat.service.WeChatThirdPartyBaseInfoService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * 微信客服回调实现层
 * <AUTHOR> 杨国锋
 * @since 2025-06-17
 */
@Slf4j
@Service
public class WeChatKfCallBackServiceImpl implements WeChatKfCallBackService {
    
    private final WeChatAsyncService weChatAsyncService;
    
    private final WeChatThirdPartyBaseInfoService weChatThirdPartyBaseInfoService;
    
    private final XmlUtils xmlUtils;
    
    private final WeChatKfConfigEntity weChatKfConfigEntity;

    public WeChatKfCallBackServiceImpl(WeChatAsyncService weChatAsyncService, WeChatThirdPartyBaseInfoService weChatThirdPartyBaseInfoService, XmlUtils xmlUtils, WeChatKfConfigEntity weChatKfConfigEntity) {
        this.weChatAsyncService = weChatAsyncService;
        this.weChatThirdPartyBaseInfoService = weChatThirdPartyBaseInfoService;
        this.xmlUtils = xmlUtils;
        this.weChatKfConfigEntity = weChatKfConfigEntity;
    }

    /**
     * 校验密文和重新生成明文是否一致
     *
     * @param methodType   方法类型
     * @param msgSignature 加密签名
     * @param timestamp    时间戳
     * @param nonce        随机数
     * @param echoStr      加密的字符串，解密后需原样返回
     * @param platFormId   平台Id
     * @return 解密后的明文
     */
    @Override
    public String verifyDataCallBack(String methodType, String msgSignature, String timestamp, String nonce, String echoStr, String platFormId) {
        try {
            WeChatThirdPartyBaseInfoEntity baseInfoEntity = weChatThirdPartyBaseInfoService.getOne(new QueryWrapper<WeChatThirdPartyBaseInfoEntity>()
                    .eq("plat_form_id", platFormId)
                    .eq("receive_id", weChatKfConfigEntity.getReceiveId())
            );
            if (ObjectUtils.isEmpty(baseInfoEntity)) {
                throw new BusinessException(ErrorCode.WECHAT_THIRD_PARTY_AUTH_INFO_NOT_FOUND.getCode(), ErrorCode.WECHAT_THIRD_PARTY_AUTH_INFO_NOT_FOUND.getDefaultMessage());
            }
            WXBizJsonMsgCrypt wxBizJsonMsgCrypt = new WXBizJsonMsgCrypt(baseInfoEntity.getSuiteToken(), baseInfoEntity.getSuiteEncodingAesKey(), weChatKfConfigEntity.getReceiveId());
            String sEchoStr = wxBizJsonMsgCrypt.verifyURL(msgSignature, timestamp, nonce, echoStr);
            if (CustomConstant.DATA_CALLBACK.equalsIgnoreCase(methodType)) {
                log.info("数据回调，verifyUrl echostr: {}", sEchoStr);
            } else {
                log.info("指令回调，verifyUrl echostr: {}", sEchoStr);
            }
            return sEchoStr;
        } catch (Exception e) {
            throw new IllegalArgumentException("执行回调方法失败：{" + e.getMessage() + "}");
        }
    }

    /**
     * 处理POST回调
     *
     * @param requestBody  请求体
     * @param msgSignature 加密签名
     * @param timestamp    时间戳
     * @param nonce        随机数
     * @param platFormId   平台Id
     * @return 响应体
     */
    @Override
    public String handlePostCallBack(String requestBody, String msgSignature, String timestamp, String nonce, String platFormId) {
        try {
            Map<String, String> xmlValuesMap = xmlUtils.getXmlValues(requestBody, "ToUserName");
            // 服务商企业或服务Id
            String toUserName = xmlValuesMap.get("ToUserName");
            // 指令回调的接收方是服务商自己，所以用服务商的CorpId
            WeChatThirdPartyBaseInfoEntity baseInfoEntity = weChatThirdPartyBaseInfoService.getOne(new QueryWrapper<WeChatThirdPartyBaseInfoEntity>()
                    .eq("plat_form_id", platFormId)
            );
            WXBizMsgCrypt wxBizMsgCrypt = new WXBizMsgCrypt(baseInfoEntity.getSuiteToken(), baseInfoEntity.getSuiteEncodingAesKey(), toUserName);
            String decryptedXml = wxBizMsgCrypt.DecryptMsg(msgSignature, timestamp, nonce, requestBody);
            log.info("指令回调解密后内容: {}", decryptedXml);
            // 异步处理infoType事件类型
            weChatAsyncService.handleWeChatEventAsync(decryptedXml, toUserName, platFormId, baseInfoEntity);
            return "success";
        } catch (Exception e) {
            log.error("处理指令回调失败：{}", e.getMessage(), e);
            return null;
        }
    }
}
