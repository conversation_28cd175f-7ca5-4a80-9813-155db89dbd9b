package com.mkefu.chatflow.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * Dify 节点信息实体，存储节点的访问地址、状态和性能指标
 * <AUTHOR> Yang 杨国锋
 * @since 2025-04-23
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("dify_nodes")
@Schema(name = "Dify节点信息实体类", description = "DifyNodesEntity")
public class DifyNodesEntity implements Serializable {

    @TableId("instance_id")
    @Schema(description = "Dify 节点的唯一标识")
    private String instanceId;

    @TableField("url")
    @Schema(description = "Dify 节点的访问地址")
    private String url;

    @TableField("active")
    @Schema(description = "节点是否处于活动状态")
    private boolean active;

    @TableField("latency")
    @Schema(description = "延迟字段，单位：毫秒")
    private double latency;

    @TableField("load")
    @Schema(description = "负载字段，范围：0-1")
    private double load;

    @TableField("api_key")
    @Schema(description = "API Key")
    private String apiKey;

    @TableField("created_at")
    @Schema(description = "节点创建时间")
    private LocalDateTime createdAt;

    @TableField("updated_at")
    @Schema(description = "节点更新时间")
    private LocalDateTime updatedAt;
}
