package com.mkefu.wechat.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.mkefu.wechat.entity.WeChatCommonEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * 获取第三方应用凭证响应对象
 *
 * <AUTHOR> 杨国锋
 * @since 2025-06-18
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Schema(name = "获取第三方应用凭证响应对象", description = "WeChatAppAuthResponseDto")
public class WeChatAppAuthResponseDto extends WeChatCommonEntity implements Serializable {
    
    @Serial
    private static final long serialVersionUID = 1L;
    
    @Schema(description = "第三方或者代开发应用access_token,最长为512字节")
    @JsonProperty("suite_access_token")
    private String suiteAccessToken;
}
