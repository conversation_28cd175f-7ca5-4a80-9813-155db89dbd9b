package com.mkefu.chatflow.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR> 杨国锋
 * @since 2025-05-29
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "智能体配置的插件实体类")
public class CozePluginInfoEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "插件唯一标识。")
    @JsonProperty("plugin_id")
    private String pluginId;

    @Schema(description = "插件名称。")
    private String name;

    @Schema(description = "插件头像。")
    @JsonProperty("icon_url")
    private String iconUrl;

    @Schema(description = "插件描述。")
    private String description;

    @Schema(description = "插件的工具列表信息。")
    @JsonProperty("api_info_list")
    private List<CozeApiInfoEntity> apiInfoList;
}
