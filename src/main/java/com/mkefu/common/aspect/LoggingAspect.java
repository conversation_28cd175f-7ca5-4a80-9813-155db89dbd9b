package com.mkefu.common.aspect;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.mkefu.common.annotation.LogInteraction;
import com.mkefu.common.constant.BaseCode;
import com.mkefu.system.entity.InteractionLogEntity;
import com.mkefu.system.service.InteractionLogService;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.method.HandlerMethod;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * 切面类，用于记录方法的执行时间和参数
 * <AUTHOR>
 * @since 2025-04-10
 */
@Aspect
@Component
@Slf4j
public class LoggingAspect {

    private final InteractionLogService interactionLogService;

    private final ObjectMapper objectMapper;

    public LoggingAspect(InteractionLogService interactionLogService, ObjectMapper objectMapper) {
        this.interactionLogService = interactionLogService;
        this.objectMapper = objectMapper;
    }

    /**
     * 切面方法，用于记录方法的执行时间和参数
     * @param joinPoint 切入点
     * @param logInteraction 注解
     * @return 方法执行结果
     * @throws Throwable 如果方法执行时发生异常，则抛出该异常
     */
    @Around("@annotation(logInteraction)")
    public Object logExecutionTime(ProceedingJoinPoint joinPoint, LogInteraction logInteraction) throws Throwable {
        long startTime = System.currentTimeMillis();
        String methodName = joinPoint.getSignature().getName();
        Object[] args = joinPoint.getArgs();
        String description = logInteraction.description();

        // 获取方法签名和参数名
        Map<String, Object> paramMap = getStringObjectMap(joinPoint, args);

        // 将参数 Map 转换为 JSON 字符串
        String argumentsJson;
        try {
            argumentsJson = objectMapper.writeValueAsString(paramMap);
        } catch (Exception e) {
            log.error("无法转换参数为 JSON: {}", e.getMessage(), e);
            argumentsJson = Arrays.toString(args);
        }

        // 获取客户端 IP 地址
        String ipAddress = getClientIpAddress();

        log.info("开始执行方法: {} (Description: {}) with 参数: {}, IP: {}", methodName, description, argumentsJson, ipAddress);

        InteractionLogEntity interactionLogEntity = new InteractionLogEntity();
        interactionLogEntity.setMethodName(methodName);
        interactionLogEntity.setArguments(argumentsJson);
        interactionLogEntity.setTimestamp(LocalDateTime.now());
        interactionLogEntity.setDescription(description);
        interactionLogEntity.setIpAddress(ipAddress);
        interactionLogEntity.setCreateId("admin");
        interactionLogEntity.setCreateName("超级管理员");

        try {
            Object result = joinPoint.proceed();
            long endTime = System.currentTimeMillis();
            String resultString;
            try {
                resultString = result != null ? objectMapper.writeValueAsString(result) : "null";
            } catch (Exception e) {
                log.error("无法转换结果为 JSON: {}", e.getMessage(), e);
                resultString = result.toString();
            }
            interactionLogEntity.setResult(resultString);
            interactionLogEntity.setExecutionTime(endTime - startTime);
            interactionLogService.save(interactionLogEntity);
            log.info("方法 {} 执行完成，耗时 {} ms, 返回: {}", methodName, (endTime - startTime), result);
            return result;
        } catch (Throwable t) {
            interactionLogEntity.setException(t.getMessage());
            interactionLogService.save(interactionLogEntity);
            log.error("Exception in method {}: {}", methodName, t.getMessage(), t);
            throw t;
        }
    }

    /**
     * 获取方法签名和参数名
     * @param joinPoint 切入点
     * @param args 方法参数
     * @return 客户端 IP 地址
     */
    private static Map<String, Object> getStringObjectMap(ProceedingJoinPoint joinPoint, Object[] args) {
        MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
        String[] parameterNames = methodSignature.getParameterNames();

        // 将参数名和参数值组合成 key-value 形式
        Map<String, Object> paramMap = HashMap.newHashMap(16);
        for (int i = 0; i < parameterNames.length; i++) {
            Object arg = args[i];
            switch (arg) {
                case HttpServletRequest request -> {
                    Map<String, String> requestInfo = HashMap.newHashMap(3);
                    requestInfo.put("remoteAddr", request.getRemoteAddr());
                    requestInfo.put("method", request.getMethod());
                    requestInfo.put("requestURI", request.getRequestURI());
                    // 只提取 HttpServletRequest 的关键信息
                    paramMap.put(parameterNames[i], requestInfo);
                }
                case HttpServletResponse ignored ->
                    // 跳过 HttpServletResponse 的序列化
                        paramMap.put(parameterNames[i], "[HttpServletResponse]");
                case HandlerMethod handlerMethod -> {
                    Map<String, String> handlerInfo = HashMap.newHashMap(2);
                    handlerInfo.put("beanType", handlerMethod.getBeanType().getSimpleName());
                    handlerInfo.put("methodName", handlerMethod.getMethod().getName());
                    // 只提取 HandlerMethod 的关键信息
                    paramMap.put(parameterNames[i], handlerInfo);
                }
                case null, default -> paramMap.put(parameterNames[i], arg);
            }
        }
        return paramMap;
    }

    /**
     * 获取客户端 IP 地址
     * @return 客户端 IP 地址
     */
    private String getClientIpAddress() {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes == null) {
            log.warn("Unable to get HttpServletRequest, returning default IP");
            return BaseCode.UNKNOWN.getCode();
        }

        HttpServletRequest request = attributes.getRequest();
        String ipAddress = request.getHeader("X-Forwarded-For");

        if (ipAddress == null || ipAddress.isEmpty() || BaseCode.UNKNOWN.getCode().equalsIgnoreCase(ipAddress)) {
            ipAddress = request.getHeader("Proxy-Client-IP");
        }
        if (ipAddress == null || ipAddress.isEmpty() || BaseCode.UNKNOWN.getCode().equalsIgnoreCase(ipAddress)) {
            ipAddress = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ipAddress == null || ipAddress.isEmpty() || BaseCode.UNKNOWN.getCode().equalsIgnoreCase(ipAddress)) {
            ipAddress = request.getHeader("HTTP_CLIENT_IP");
        }
        if (ipAddress == null || ipAddress.isEmpty() || BaseCode.UNKNOWN.getCode().equalsIgnoreCase(ipAddress)) {
            ipAddress = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (ipAddress == null || ipAddress.isEmpty() || BaseCode.UNKNOWN.getCode().equalsIgnoreCase(ipAddress)) {
            ipAddress = request.getRemoteAddr();
        }

        if (ipAddress != null && ipAddress.contains(",")) {
            ipAddress = ipAddress.split(",")[0].trim();
        }

        return ipAddress != null ? ipAddress : BaseCode.UNKNOWN.getCode();
    }
}
