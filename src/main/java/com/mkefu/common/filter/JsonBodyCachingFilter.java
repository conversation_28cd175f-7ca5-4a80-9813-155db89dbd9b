package com.mkefu.common.filter;


import com.mkefu.common.constant.BaseCode;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletRequestWrapper;
import jakarta.servlet.http.HttpServletResponse;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.io.IOUtils;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;

/**
 * 缓存 JSON 请求体的过滤器
 * <AUTHOR>
 * @since 2025-4-14
 */
@Slf4j
@Component
@Order(1)
public class JsonBodyCachingFilter extends OncePerRequestFilter {

    @Override
    protected void doFilterInternal(HttpServletRequest request,
                                    @NonNull HttpServletResponse response,
                                    @NonNull FilterChain filterChain)
            throws ServletException, IOException {
        String contentType = request.getContentType();
        if (contentType != null && contentType.toLowerCase().contains(BaseCode.JSON.getCode())) {
            byte[] body = IOUtils.toByteArray(request.getInputStream());
            String jsonBody = new String(body, StandardCharsets.UTF_8);
            request.setAttribute("cachedJsonBody", jsonBody);

            // 只打印前 300 个字符
            if (log.isDebugEnabled()) {
                String preview = jsonBody.length() > 300 ? jsonBody.substring(0, 300) + "..." : jsonBody;
                log.debug("JSON 请求体缓存: uri={}, size={}, preview={}", request.getRequestURI(), body.length, preview);
            }

            // 包装请求体
            CachedBodyHttpServletRequest cachedRequest = new CachedBodyHttpServletRequest(request, body);
            filterChain.doFilter(cachedRequest, response);

        } else {
            if (log.isDebugEnabled()) {
                log.debug("非 JSON 请求，跳过缓存: uri={}, contentType={}", request.getRequestURI(), contentType);
            }
            filterChain.doFilter(request, response);
        }
    }

    /**
     * 自定义请求包装器，支持重复读取 InputStream
     */
    private static class CachedBodyHttpServletRequest extends HttpServletRequestWrapper {
        private final byte[] cachedBody;

        public CachedBodyHttpServletRequest(HttpServletRequest request, byte[] cachedBody) {
            super(request);
            this.cachedBody = cachedBody;
        }

        @Override
        public jakarta.servlet.ServletInputStream getInputStream() {
            return new CachedBodyServletInputStream(cachedBody);
        }
    }

    /**
     * 自定义 ServletInputStream，支持重复读取
     */
    private static class CachedBodyServletInputStream extends jakarta.servlet.ServletInputStream {
        private final ByteArrayInputStream inputStream;

        public CachedBodyServletInputStream(byte[] cachedBody) {
            this.inputStream = new ByteArrayInputStream(cachedBody);
        }

        @Override
        public boolean isFinished() {
            return inputStream.available() == 0;
        }

        @Override
        public boolean isReady() {
            return true;
        }

        @Override
        public void setReadListener(jakarta.servlet.ReadListener readListener) {
            // 不需要实现
        }

        @Override
        public int read() {
            return inputStream.read();
        }
    }
}
