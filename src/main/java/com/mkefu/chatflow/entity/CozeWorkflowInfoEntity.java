package com.mkefu.chatflow.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR> 杨国锋
 * @since 2025-05-29
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "智能体配置的工作流实体类。", description = "CozeWorkflowInfoEntity")
public class CozeWorkflowInfoEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "工作流的唯一标识。")
    private String id;

    @Schema(description = "工作流的名称。")
    private String name;

    @Schema(description = "工作流的头像地址。")
    @JsonProperty("icon_url")
    private String iconUrl;

    @Schema(description = "工作流的描述。")
    private String description;
}
