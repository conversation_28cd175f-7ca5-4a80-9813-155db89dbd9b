package com.mkefu.elasticserch.controller;

import com.mkefu.common.response.Result;
import com.mkefu.elasticserch.entity.ChatRecordEntity;
import com.mkefu.elasticserch.service.ChatRecordService;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 聊天记录控制层
 * <AUTHOR>
 * @since 2025-04-10
 */
@Validated
@RestController
@Tag(name = "聊天记录控制层")
@RequestMapping("/api/chatRecord")
public class ChatRecordController {

    private final ChatRecordService chatRecordService;

    public ChatRecordController(ChatRecordService chatRecordService) {
        this.chatRecordService = chatRecordService;
    }

    @PostMapping("/saveRecord")
    public Result addChatRecord(@RequestBody ChatRecordEntity chatRecordEntity) {
        return Result.success(chatRecordService.saveChatRecord(chatRecordEntity));
    }
}
