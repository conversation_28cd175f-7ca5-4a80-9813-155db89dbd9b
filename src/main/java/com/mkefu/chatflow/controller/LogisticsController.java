package com.mkefu.chatflow.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.mkefu.common.annotation.LogInteraction;
import com.mkefu.common.constant.ErrorCode;
import com.mkefu.common.exception.BusinessException;
import com.mkefu.common.response.Result;
import com.mkefu.chatflow.service.LogisticsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotBlank;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * 物流查询控制层
 * <AUTHOR>
 * @since 2025-04-10
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/api/logistics")
@Tag(name = "物流查询服务")
public class LogisticsController {

    private final ObjectMapper objectMapper;

    private final LogisticsService logisticsService;

    public LogisticsController(ObjectMapper objectMapper, LogisticsService logisticsService) {
        this.objectMapper = objectMapper;
        this.logisticsService = logisticsService;
    }

    @Operation(summary = "查询物流公司编码")
    @GetMapping("/queryCode")
    @LogInteraction(description = "查询物流公司编码")
    public Result queryCode(@NotBlank(message = "物流单号不能为空") @RequestParam("logisticsNo") String logisticsNo) {
        log.info("收到物流查询请求：单号={}", logisticsNo);
        String result = logisticsService.queryCode(logisticsNo);
        try {
            return Result.success(objectMapper.readValue(result, Map.class));
        } catch (Exception e) {
            throw new RuntimeException(e.getMessage());
        }
    }

    @Operation(summary = "查询物流轨迹")
    @GetMapping("/queryTrace")
    @LogInteraction(description = "查询物流轨迹")
    public Result queryTrace(@NotBlank(message = "物流单号不能为空") @RequestParam("logisticsNo") String logisticsNo,
                             @RequestParam("customerPhone") String customerPhone) {
        log.info("收到物流查询请求：单号={}", logisticsNo);
        String result = logisticsService.queryTrace(logisticsNo, customerPhone);
        try {
            return Result.success(objectMapper.readValue(result, Map.class));
        } catch (Exception e) {
            throw new RuntimeException(e.getMessage());
        }
    }
}
