package com.mkefu.chatflow.service.impl;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.mkefu.chatflow.dto.*;
import com.mkefu.chatflow.dto.CommonCancelChatRequestDto;
import com.mkefu.common.annotation.LogInteraction;
import com.mkefu.common.constant.BaseCode;
import com.mkefu.common.constant.CustomConstant;
import com.mkefu.common.constant.ErrorCode;
import com.mkefu.common.entity.ChatFlowConfigEntity;
import com.mkefu.common.exception.BusinessException;
import com.mkefu.chatflow.service.DifyChatService;
import com.mkefu.chatflow.service.DifyClientService;
import com.mkefu.common.response.Result;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.FluxSink;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;


/**
 *
 * <AUTHOR> Yang 杨国锋
 * @since 2025-04-17
 */
@Slf4j
@Service
public class DifyChatImpl implements DifyChatService {

    private final ObjectMapper objectMapper;

    private final DifyClientService difyClientService;

    private final OkHttpClient okHttpClient;

    private static final String ANSWER = "answer";
    
    private final ChatFlowConfigEntity chatFlowConfigEntity;

    public DifyChatImpl(ObjectMapper objectMapper, DifyClientService difyClientService, ChatFlowConfigEntity chatFlowConfigEntity) {
        this.objectMapper = objectMapper;
        this.difyClientService = difyClientService;
        this.chatFlowConfigEntity = chatFlowConfigEntity;
        this.okHttpClient = new OkHttpClient();
    }

    /**
     * 发起对话消息-SSE流
     * @param dto 数据传输对象
     * @param accessToken 令牌
     * @return 响应内容
     */
    @Override
    @LogInteraction(description = "发起对话-SSE流-DIFY官方API")
    public Flux<String> chatMessagesStream(DifyChatRequestDto dto, String accessToken) {
        return Flux.create(sink -> {
            Request request = buildRequest(dto, accessToken);
            executeAsyncRequest(request, sink);
        });
    }

    /**
     * 发起对话消息-阻塞
     * @param dto 数据传输对象
     * @param accessToken 租户组
     * @return 自定义响应对象  app-qt0a7YRnBe6UorddPHomsvpv
     */
    @Override
    @LogInteraction(description = "发起对话-阻塞式-DIFY官方API")
    public DifyChatResponseDto chatMessages(DifyChatRequestDto dto, String accessToken) {
        try {
            HttpRequest request = HttpRequest
                    .post(CustomConstant.DIFY_OFFICIAL_PREFIX_URL + "v1/chat-messages")
                    .header(CustomConstant.HEADER_AUTHORIZATION, "Bearer " + accessToken)
                    .body(objectMapper.writeValueAsString(dto));
            try (HttpResponse response = request.execute()) {
                log.info("调用DIFY官方API：{}，请求参数：{}", request.getUrl(), objectMapper.writeValueAsString(dto));
                String body = response.body();
                log.info("调用DIFY官方API成功，输出内容为：{}", body);
                DifyChatResponseDto respDto = objectMapper.readValue(body, DifyChatResponseDto.class);
                if (chatFlowConfigEntity.containsAnswer(respDto.getAnswer())) {
                    respDto.setResolved(false);
                }
                return respDto;
            }
        } catch (Exception e) {
            log.error("调用 DIFY API 异常: {}", e.getMessage());
            throw new BusinessException("调用DIFY官方API，接口异常：{" + "}" + CustomConstant.DIFY_OFFICIAL_PREFIX_URL + "v1/chat-messages", e.getMessage());
        }
    }

    /**
     * 获取下一轮建议问题列表
     * @param dto 数据传输对象
     * @param accessToken 令牌
     * @return 响应内容
     */
    @Override
    public DifySuggestedResponseDto getSuggested(DifySuggestedRequestDto dto, String accessToken) {
        try {
            HttpRequest request = HttpRequest
                    .get(CustomConstant.DIFY_OFFICIAL_PREFIX_URL + "v1/messages/" + dto.getMessageId() + "/suggested")
                    .header(CustomConstant.HEADER_AUTHORIZATION, "Bearer " + accessToken)
                    .form("user", dto.getUser());
            try (HttpResponse response = request.execute()) {
                log.info("调用DIFY官方API：{}，请求参数：{}", request.getUrl(), objectMapper.writeValueAsString(dto));
                String body = response.body();
                return objectMapper.readValue(body, DifySuggestedResponseDto.class);
            }
        } catch (JsonProcessingException e) {
            throw new BusinessException(ErrorCode.DIFY_METHOD_ERROR, e.getMessage());
        }
    }

    /**
     * 获取会话列表
     * @param accessToken 令牌
     * @param params 参数
     * @return 响应内容
     */
    @Override
    public DifyGetConversationListResponseDto getConversationList(String accessToken, Map<String, Object> params) {
        try {
            HttpRequest request = HttpRequest
                    .get(CustomConstant.DIFY_OFFICIAL_PREFIX_URL + "v1/conversations")
                    .header(CustomConstant.HEADER_AUTHORIZATION, "Bearer " + accessToken)
                    .form(params);
            try (HttpResponse response = request.execute()) {
                log.info("调用DIFY官方API：{}，请求参数：{}", request.getUrl(), objectMapper.writeValueAsString(params));
                String body = response.body();
                log.info("调用DIFY官方API成功，输出内容为：{}", body);
                return objectMapper.readValue(body, new TypeReference<>() {});
            }
        } catch (Exception e) {
            throw new BusinessException(ErrorCode.DIFY_METHOD_ERROR, e.getMessage());
        }
    }

    /**
     * 获取会话历史消息
     * @param accessToken 令牌
     * @param params 参数
     * @return 响应内容
     */
    @Override
    public DifyGetMessagesHistoryResponseDto getMessages(String accessToken, Map<String, Object> params) {
        Object conversationId = params.get("conversationId");
        params.remove("conversationId");
        params.put("conversation_id", conversationId);
        try {
            HttpRequest request = HttpRequest
                    .get(CustomConstant.DIFY_OFFICIAL_PREFIX_URL + "v1/messages")
                    .header(CustomConstant.HEADER_AUTHORIZATION, "Bearer " + accessToken)
                    .form(params);
            try (HttpResponse response = request.execute()) {
                log.info("调用DIFY官方API：{}，请求参数：{}", request.getUrl(), objectMapper.writeValueAsString(params));
                String body = response.body();
                log.info("调用DIFY官方API成功，输出内容为：{}", body);
                return objectMapper.readValue(body, DifyGetMessagesHistoryResponseDto.class);
            }
        } catch (Exception e) {
            throw new BusinessException(ErrorCode.DIFY_METHOD_ERROR, e.getMessage());
        }
    }

    /**
     * 删除会话
     * @param map 参数
     * @param accessToken 令牌
     * @return 响应内容
     */
    @Override
    public String deleteConversationInfo(Map<String, Object> map, String accessToken) {
        if (ObjectUtils.isEmpty(map)) {
            throw new BusinessException(ErrorCode.INVALID_PARAM, "请传入Map结构参数");
        }
        String conversationId = map.get("conversationId").toString();
        map.remove("conversationId");
        try {
            HttpRequest request = HttpRequest
                    .delete(CustomConstant.DIFY_OFFICIAL_PREFIX_URL + "v1/conversations/" + conversationId)
                    .header(CustomConstant.HEADER_AUTHORIZATION, "Bearer " + accessToken)
                    .body(objectMapper.writeValueAsString(map));
            try (HttpResponse response = request.execute()) {
                String body = response.body();
                log.info("调用DIFY官方API：{}，body参数：{}", request.getUrl(), objectMapper.writeValueAsString(body));
                return body;
            }
        } catch (Exception e) {
            throw new BusinessException(ErrorCode.DIFY_METHOD_ERROR, e.getMessage());
        }
    }

    /**
     * 获取智能体信息
     * @param accessToken 令牌
     * @return 响应内容
     */
    @Override
    @LogInteraction(description = "获取智能体信息-DIFY官方API")
    public DifyAgentInfoResponseDto getAgentInfo(String accessToken) {
        try {
            HttpRequest request = HttpRequest
                    .get(CustomConstant.DIFY_OFFICIAL_PREFIX_URL + "v1/info")
                    .header(CustomConstant.HEADER_AUTHORIZATION, "Bearer " + accessToken);
            try (HttpResponse response = request.execute()) {
                String body = response.body();
                JsonNode jsonNode = objectMapper.readTree(body);
                boolean isSuccess = jsonNode.has("status");
                String status = jsonNode.at("/status").asText();
                String message = jsonNode.at("/message").asText();
                if (isSuccess) {
                    if (String.valueOf(ErrorCode.DIFY_UNAUTHORIZED_ERROR.getStatus()).equalsIgnoreCase(status)) {
                        throw new BusinessException(ErrorCode.DIFY_UNAUTHORIZED_ERROR.getCode(), message);
                    }
                    throw new BusinessException(ErrorCode.DIFY_UNKNOWN_ERROR, ErrorCode.DIFY_UNKNOWN_ERROR.getDefaultMessage());
                }
                log.info("调用DIFY官方API：{}，Header参数：{}，响应内容：{}", request.getUrl(), accessToken, body);
                return objectMapper.readValue(body, DifyAgentInfoResponseDto.class);
            }
        } catch (Exception e) {
            throw new BusinessException(ErrorCode.DIFY_METHOD_ERROR, e.getMessage());
        }
    }

    /**
     * 取消对话
     * @param dto 数据传输对象
     * @param accessToken 令牌
     * @return 响应内容
     */
    @Override
    @LogInteraction(description = "取消进行中的对话-仅流式-DIFY官方API")
    public CommonCancelChatResponseDto cancelChat(CommonCancelChatRequestDto dto, String accessToken) {
        Map<String, Object> map = HashMap.newHashMap(1);
        map.put("user", dto.getUserId());
        try {
            HttpRequest request = HttpRequest
                    .post(CustomConstant.DIFY_OFFICIAL_PREFIX_URL + "v1/chat-messages/" + dto.getChatId() + "/stop")
                    .header(CustomConstant.HEADER_AUTHORIZATION, "Bearer " + accessToken)
                    .body(objectMapper.writeValueAsString(map));
            try (HttpResponse response = request.execute()) {
                String body = response.body();
                log.info("调用DIFY官方API：{}，请求参数：{}，响应内容：{}", request.getUrl(), objectMapper.writeValueAsString(dto), body);
                return objectMapper.readValue(body, CommonCancelChatResponseDto.class);
            }
        } catch (Exception e) {
            throw new BusinessException(ErrorCode.DIFY_METHOD_ERROR, e.getMessage());
        }
    }

    /**
     * 处理返回的数据存储到ES和MySQL
     */
    private void saveToDb() {

    }

    /**
     * 构建 SSE 请求
     */
    private Request buildRequest(DifyChatRequestDto dto, String accessToken) {
        try {
            String requestBody = objectMapper.writeValueAsString(dto);
            RequestBody body = RequestBody.create(requestBody, MediaType.parse("application/json"));
            return new Request.Builder()
                    .url(CustomConstant.DIFY_OFFICIAL_PREFIX_URL + "v1/chat-messages")
                    .post(body)
                    .header(CustomConstant.HEADER_AUTHORIZATION, "Bearer " + accessToken)
                    .header("Content-Type", "application/json")
                    .header("Accept", "text/event-stream")
                    .header("Cache-Control", "no-cache")
                    .header("Connection", "keep-alive")
                    .build();
        } catch (Exception e) {
            throw new BusinessException("构建 DIFY SSE 请求失败", e.getMessage());
        }
    }

    /**
     * 执行异步请求并处理响应
     */
    private void executeAsyncRequest(Request request, FluxSink<String> sink) {
        okHttpClient.newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(@NonNull Call call, @NonNull IOException e) {
                log.error("调用 DIFY SSE API 失败: {}", e.getMessage());
                sink.error(new BusinessException("调用 DIFY SSE API 失败", e.getMessage()));
            }

            @Override
            public void onResponse(@NonNull Call call, @NonNull Response response) {
                processResponse(response, sink);
            }
        });
    }

    /**
     * 处理 HTTP 响应
     */
    private void processResponse(Response response, FluxSink<String> sink) {
        try (ResponseBody responseBody = response.body()) {
            if (responseBody == null) {
                log.error("响应体为空");
                sink.error(new BusinessException("响应体为空", ""));
                return;
            }
            processSseStream(responseBody, sink);
        } catch (Exception e) {
            log.error("处理 SSE 流失败: {}", e.getMessage());
            sink.error(new BusinessException("处理 SSE 流失败", e.getMessage()));
        } finally {
            if (!sink.isCancelled()) {
                sink.complete();
            }
        }
    }

    /**
     * 处理 SSE 流
     */
    private void processSseStream(ResponseBody responseBody, FluxSink<String> sink) {
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(responseBody.byteStream(), StandardCharsets.UTF_8))) {
            String line;
            StringBuilder dataBuilder = new StringBuilder();

            while ((line = reader.readLine()) != null) {
                line = line.trim();
                if (line.isEmpty()) {
                    // 空行表示一个事件结束，处理拼接的data内容
                    if (!dataBuilder.isEmpty()) {
                        handleEventData(dataBuilder.toString(), sink);
                        dataBuilder.setLength(0);
                    }
                    continue;
                }

                if (line.startsWith("data:")) {
                    // 拼接data行内容，去掉前缀"data:"后空格
                    dataBuilder.append(line.substring(5).trim());
                } else {
                    // 非data行，尝试解析为错误事件
                    try {
                        JsonNode jsonNode = objectMapper.readTree(line);
                        String message = jsonNode.at("/message").asText(null);
                        String code = jsonNode.at("/status").asText(null);
                        if (message != null && code != null) {
                            handleErrorEvent(code, message, sink);
                            // 遇到错误事件后结束处理
                            return;
                        }
                    } catch (Exception e) {
                        // 非JSON格式，忽略或记录日志
                        log.warn("非JSON格式的非data行: {}", line);
                    }
                }
            }

            // 流结束时，如果还有未处理的数据，处理一次
            if (!dataBuilder.isEmpty()) {
                handleEventData(dataBuilder.toString(), sink);
            }
        } catch (Exception e) {
            sink.error(e);
            log.error("处理Dify SSE流时报错了：{}", e.getMessage());
        } finally {
            if (!sink.isCancelled()) {
                sink.complete();
            }
        }
    }

    /**
     * 处理正常事件数据
     */
    private void handleEventData(String eventData, FluxSink<String> sink) {
        try {
            JsonNode jsonNode = objectMapper.readTree(eventData);
            String eventType = jsonNode.has("event") ? jsonNode.get("event").asText() : null;
            if (eventType != null) {
                processEvent(eventType, eventData, sink);
            } else {
                // 非预期格式，直接推送原始数据
                sink.next(eventData);
            }
        } catch (Exception e) {
            // JSON解析异常，推送原始数据或记录日志
            sink.next(eventData);
            log.error("处理Dify SSE流 正常事件时报错了：{}", e.getMessage());
        }
    }

    /**
     * 处理错误事件
     */
    private void handleErrorEvent(String code, String message, FluxSink<String> sink) throws JsonProcessingException {
        sink.next("event:error");
        if (BaseCode.FOUR_HUNDRED.getCode().equals(code)) {
            if (ErrorCode.DIFY_PROVIDER_QUOTA_EXCEEDED.getDefaultMessage().equals(message)) {
                sink.next(objectMapper.writeValueAsString(Result.error(ErrorCode.DIFY_PROVIDER_QUOTA_EXCEEDED.getCode(), message)));
                log.warn("DIFY SSE 流调用返回错误事件：{}", message);
            } else if (ErrorCode.DIFY_MODEL_CURRENTLY_NOT_SUPPORT.getDefaultMessage().equals(message)) {
                sink.next(objectMapper.writeValueAsString(Result.error(ErrorCode.DIFY_MODEL_CURRENTLY_NOT_SUPPORT.getCode(), message)));
                log.warn("DIFY SSE 流调用返回错误事件：{}", message);
            }
        } else if (BaseCode.FOUR_ZERO_FOUR.getCode().equals(code)) {
            sink.next(objectMapper.writeValueAsString(Result.error(ErrorCode.DIFY_CONVERSATION_NOT_FOUND.getCode(), "当前对话不存在")));
            log.warn("DIFY SSE 流调用返回错误事件：当前对话不存在");
        }
    }

    /**
     * 处理 SSE 事件
     */
    private void processEvent(String eventType, String data, FluxSink<String> sink) {
        try {
            JsonNode eventData = objectMapper.readTree(data);
            String eventDataJson = objectMapper.writeValueAsString(eventData);
            switch (eventType) {
                case "message":
                    if (eventData.has(ANSWER)) {
                        String content = eventData.get(ANSWER).asText();
                        if (chatFlowConfigEntity.containsAnswer(content)) {
                            ObjectNode objectNode = (ObjectNode) eventData;
                            objectNode.put("event", CustomConstant.SSE_EVENT_TYPE_NOT_MATCH);
                            String modifiedJsonNode = objectMapper.writeValueAsString(objectNode);
                            sink.next(modifiedJsonNode);
                            log.info("当前问题在知识库未匹配上：{}", CustomConstant.SSE_EVENT_TYPE_NOT_MATCH);
                            log.debug("当前问题在知识库未匹配上，消息ID：{}", eventData.at("/message_id"));
                            sink.complete();
                            break;
                        }
                        sink.next(eventDataJson);
                        log.info("收到消息增量: {}", content);
                    }
                    break;

                case "message_end":
                    sink.next(eventDataJson);
                    log.info("已收到全部信息：{}", eventType);
                    break;

                case "workflow_finished":
                    log.info("SSE 事件完成：{}", eventType);
                    break;
                case "error":
                    log.error("SSE 错误事件：{}，请查看报错信息：{}", eventType, eventDataJson);
                    String errorMessage = eventData.at("/message").asText();
                    sink.next("event:error");
                    if (CustomConstant.DIFY_TRAFFIC_LIMITING_MESSAGE.contains(errorMessage)) {
                        sink.next(String.valueOf(Result.error(ErrorCode.DIFY_TRAFFIC_LIMITING.getCode(), ErrorCode.DIFY_TRAFFIC_LIMITING.getDefaultMessage())));
                    } else {
                        sink.next(String.valueOf(Result.error(ErrorCode.DIFY_UNKNOWN_ERROR.getCode(), ErrorCode.DIFY_UNKNOWN_ERROR.getDefaultMessage())));
                    }
                    sink.complete();
                    break;
                default:
                    log.debug("未处理事件类型: {}", eventType);
            }
        } catch (Exception e) {
            log.error("处理 SSE 事件失败，事件类型: {}, 数据: {}", eventType, data, e);
        }
    }
}
