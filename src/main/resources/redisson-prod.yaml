singleServerConfig:
  address: redis://localhost:6379
  password: null
  database: 0
  idleConnectionTimeout: 10000
  connectTimeout: 10000
  timeout: 3000
  retryAttempts: 3
  retryInterval: 1500
  subscriptionsPerConnection: 5
  subscriptionConnectionMinimumIdleSize: 1
  subscriptionConnectionPoolSize: 1  # 最小化订阅连接池
  connectionMinimumIdleSize: 1       # 最小化连接池
  connectionPoolSize: 1              # 最小化连接池
threads: 0  # 使用默认线程数
nettyThreads: 0  # 使用默认 Netty 线程数
codec: !org.redisson.codec.JsonJacksonCodec {}
transportMode: NIO
