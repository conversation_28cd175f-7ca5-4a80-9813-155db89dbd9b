package com.mkefu.chatflow.service.impl;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mkefu.chatflow.dto.*;
import com.mkefu.chatflow.dto.CommonCancelChatRequestDto;
import com.mkefu.chatflow.entity.MessageObjectEntity;
import com.mkefu.common.annotation.LogInteraction;
import com.mkefu.common.constant.CustomConstant;
import com.mkefu.common.constant.ErrorCode;
import com.mkefu.common.entity.ChatFlowConfigEntity;
import com.mkefu.common.exception.BusinessException;
import com.mkefu.chatflow.service.CozeChatService;
import com.mkefu.common.response.Result;
import com.mkefu.system.entity.TenantAuthInfoEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import reactor.core.publisher.Flux;
import reactor.core.publisher.FluxSink;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ThreadLocalRandom;

/**
 * <AUTHOR> Yang 杨国锋
 * @since 2025-05-06
 */
@Slf4j
@Service
@Schema(description = "coze聊天服务实现类")
public class CozeChatServiceImpl implements CozeChatService {

    private final ObjectMapper objectMapper;

    private final OkHttpClient okHttpClient;
    
    private final ChatFlowConfigEntity chatFlowConfigEntity;

    private static final String CONTENT = "content";

    /**
     * 最大重试次数10次
     */
    private static final int MAX_RETRIES = 5;

    /**
     * 初始延迟 4 秒
     */
    private static final long INITIAL_DELAY_MS = 2500;

    /**
     * 最小延迟 0.5 秒
     */
    private static final long MIN_DELAY_MS = 500;

    /**
     * 每次延迟减少为上次的 65%
     */
    private static final double JITTER_FACTOR = 0.65;

    public CozeChatServiceImpl(ObjectMapper objectMapper, ChatFlowConfigEntity chatFlowConfigEntity) {
        this.objectMapper = objectMapper;
        this.chatFlowConfigEntity = chatFlowConfigEntity;
        this.okHttpClient = new OkHttpClient();
    }

    /**
     * 发起对话-SSE流
     * @param dto 数据传输对象
     * @param accessToken 访问令牌
     * @return 响应内容
     */
    @Override
    @LogInteraction(description = "发起对话-SSE流-COZE官方API")
    public Flux<String> chatMessagesStream(CozeChatRequestDto dto, String accessToken) {
        return Flux.create(sink -> {
            Request request = buildRequest(dto, accessToken);
            executeAsyncRequest(request, sink);
        });
    }

    /**
     * 发起对话-阻塞式
     * @param dto 数据传输对象
     * @param accessToken 访问令牌
     * @return 响应内容
     */
    @Override
    @LogInteraction(description = "发起对话-阻塞式-COZE官方API")
    public CozeMessageResponseDto chatMessages(CozeChatRequestDto dto, String accessToken) {
        try {
            String url = CustomConstant.COZE_PREFIX_URL + "v3/chat";
            if (StringUtils.isNotBlank(dto.getConversationId())) {
                url += "?conversation_id=" + dto.getConversationId();
            }
            HttpRequest request = HttpRequest
                    .post(url)
                    .header(CustomConstant.HEADER_AUTHORIZATION, "Bearer " + accessToken)
                    .body(objectMapper.writeValueAsString(dto));
            try (HttpResponse response = request.execute()) {
                log.info("调用COZE官方API：{}，请求参数：{}", request.getUrl(), objectMapper.writeValueAsString(dto));
                String body = response.body();
                log.info("调用COZE官方API成功，输出内容为：{}", body);
                JsonNode jsonNode = objectMapper.readTree(body);

                // 判断code 给出对应报错提示
                String code = jsonNode.at("/code").asText();
                if (String.valueOf(ErrorCode.COZE_EXCEED_MAX_RPM.getStatus()).equalsIgnoreCase(code)) {
                    throw new BusinessException(ErrorCode.COZE_EXCEED_MAX_RPM, ErrorCode.COZE_EXCEED_MAX_RPM.getDefaultMessage());
                } else if (String.valueOf(ErrorCode.COZE_NO_ENOUGH_CREDIT.getStatus()).equalsIgnoreCase(code)) {
                    throw new BusinessException(ErrorCode.COZE_NO_ENOUGH_CREDIT, ErrorCode.COZE_NO_ENOUGH_CREDIT.getDefaultMessage());
                } else if (String.valueOf(ErrorCode.COZE_RESOURCE_NOT_FOUND.getStatus()).equalsIgnoreCase(code)) {
                    throw new BusinessException(ErrorCode.COZE_RESOURCE_NOT_FOUND, ErrorCode.COZE_RESOURCE_NOT_FOUND.getDefaultMessage());
                } else if (String.valueOf(ErrorCode.COZE_ACCESS_DENIED.getStatus()).equalsIgnoreCase(code)) {
                    throw new BusinessException(ErrorCode.COZE_ERROR, ErrorCode.COZE_ACCESS_DENIED.getDefaultMessage());
                } else if (String.valueOf(ErrorCode.COZE_TOKEN_EXPIRED.getStatus()).equalsIgnoreCase(code)) {
                    throw new BusinessException(ErrorCode.COZE_TOKEN_EXPIRED, ErrorCode.COZE_TOKEN_EXPIRED.getDefaultMessage());
                } else if (String.valueOf(ErrorCode.COZE_TOKEN_INVALID.getStatus()).equalsIgnoreCase(code)) {
                    throw new BusinessException(ErrorCode.COZE_TOKEN_INVALID, ErrorCode.COZE_TOKEN_INVALID.getDefaultMessage());
                }
                String conversationId = jsonNode.at("/data/conversation_id").asText();
                String chatId = jsonNode.at("/data/id").asText();

                // 缓存随机数实例
                ThreadLocalRandom random = ThreadLocalRandom.current();
                for (int retry = 0; retry < MAX_RETRIES; retry++) {

                    if (retry < MAX_RETRIES - 1) {
                        CozeConversationResponseDto conversation = getConversation(accessToken, conversationId, chatId);
                        boolean completed = "completed".equals(conversation.getData().get("status").toString());
                        if (completed) {
                            log.info("成功获取消息: retry={}", retry + 1);
                            return getMessages(accessToken, conversationId, chatId);
                        }
                        // 延迟递减，每次减半，最低不低于 MIN_DELAY_MS
                        long delay = Math.max(INITIAL_DELAY_MS >> retry, MIN_DELAY_MS);

                        // 加入抖动
                        long jitter = calculateJitter(delay, random);
                        long sleepTime = delay + jitter;

                        log.info("智能体暂未完成处理，第 {} 次重试，将暂停 {} 毫秒", retry + 1, sleepTime);
                        try {
                            Thread.sleep(sleepTime);
                        } catch (InterruptedException e) {
                            Thread.currentThread().interrupt();
                            log.error("轮询被中断: retry={}", retry, e);
                            throw new BusinessException(ErrorCode.COZE_CHAT_IN_PROGRESS, "轮询查看对话详情接口被中断");
                        }
                    } else {
                        log.error("智能体最终未完成处理，已尝试 {} 次", retry);
                    }
                }
                return new CozeMessageResponseDto();
            }
        } catch (Exception e) {
            log.error("调用 COZE API 异常: {}", e.getMessage());
            throw new BusinessException("调用COZE官方API，接口异常：{" + "}" + CustomConstant.COZE_PREFIX_URL + "v3/chat", e.getMessage());
        }
    }

    /**
     * 计算抖动值
     * @param delay 基础延迟
     * @param random 随机数生成器
     * @return 抖动值
     */
    private long calculateJitter(long delay, ThreadLocalRandom random) {
        return (long) (delay * (random.nextDouble() * JITTER_FACTOR));
    }

    /**
     * 查看对话消息详情
     * @param accessToken 访问令牌
     * @param conversationId 会话Id
     * @param chatId 消息Id
     * @return 相应内容
     */
    @Override
    public CozeMessageResponseDto getMessages(String accessToken, String conversationId, String chatId) {
        Map<String, Object> params = HashMap.newHashMap(2);
        params.put("conversation_id", conversationId);
        params.put("chat_id", chatId);
        try {
            HttpRequest request = HttpRequest
                    .get(CustomConstant.COZE_PREFIX_URL + "v3/chat/message/list")
                    .header(CustomConstant.HEADER_AUTHORIZATION, "Bearer " + accessToken)
                    .form(params);
            try (HttpResponse response = request.execute()) {
                String body = response.body();
                log.info("调用COZE官方API：{}，响应结果：{}", request.getUrl(), body);
                CozeMessageResponseDto dto = objectMapper.readValue(body, CozeMessageResponseDto.class);
                List<MessageObjectEntity> answers = dto.getData().stream()
                        .filter(item -> "answer".equals(item.getType()))
                        .toList();
                
                for (MessageObjectEntity answer : answers) {
                    // 判断是否在知识库找到答案
                    if (chatFlowConfigEntity.containsAnswer(answer.getContent())) {
                        dto.setResolved(false);
                        break;
                    }
                }
                if (CollectionUtils.isEmpty(answers)) {
                    throw new BusinessException(ErrorCode.COZE_ERROR, "未匹配到任何答案");
                }
                dto.setData(answers);
                return dto;
            }
        } catch (Exception e) {
            throw new BusinessException(ErrorCode.COZE_ERROR, "调用COZE官方API失败：「" + e.getMessage() + "」");
        }
    }

    /**
     * 查看对话详情
     * @param accessToken 访问令牌
     * @param conversationId 会话Id
     * @param chatId 消息Id
     * @return 响应内容
     */
    @Override
    public CozeConversationResponseDto getConversation(String accessToken, String conversationId, String chatId) {
        Map<String, Object> params = HashMap.newHashMap(2);
        params.put("conversation_id", conversationId);
        params.put("chat_id", chatId);
        try {
            HttpRequest request = HttpRequest
                    .get(CustomConstant.COZE_PREFIX_URL + "v3/chat/retrieve")
                    .header(CustomConstant.HEADER_AUTHORIZATION, "Bearer " + accessToken)
                    .form(params);
            try (HttpResponse response = request.execute()) {
                String body = response.body();
                log.info("调用COZE官方API：{}，响应内容：{}", request.getUrl(), body);
                return objectMapper.readValue(body, CozeConversationResponseDto.class);
            }
        } catch (Exception e) {
            throw new BusinessException(ErrorCode.COZE_ERROR);
        }
    }

    /**
     * 取消进行中的对话-COZE官方API
     * @param dto 取消进行中的对话请求实体类
     * @param accessToken 访问令牌
     * @return 响应内容
     */
    @Override
    @LogInteraction(description = "取消进行中的对话-仅流式-COZE官方API")
    public CommonCancelChatResponseDto cancelChat(CommonCancelChatRequestDto dto, String accessToken) {
        Map<String, Object> bodyMap = HashMap.newHashMap(2);
        bodyMap.put("chat_id", dto.getChatId());
        bodyMap.put("conversation_id", dto.getConversationId());
        try {
            HttpRequest request = HttpRequest
                    .post(CustomConstant.COZE_PREFIX_URL + "v3/chat/cancel")
                    .header(CustomConstant.HEADER_AUTHORIZATION, "Bearer " + accessToken)
                    .body(objectMapper.writeValueAsString(bodyMap));
            try (HttpResponse response = request.execute()) {
                String body = response.body();
                log.info("调用COZE官方API：{}，响应内容：{}", request.getUrl(), body);
                return objectMapper.readValue(body, CommonCancelChatResponseDto.class);
            }
        } catch (Exception e) {
            throw new BusinessException(ErrorCode.COZE_ERROR);
        }
    }

    /**
     * 获取智能体信息-COZE官方API
     * @param tenantAuthInfoEntity 租户信息实体类
     * @param accessToken 访问令牌
     * @return 响应内容
     */
    @Override
    @LogInteraction(description = "获取智能体信息-COZE官方API")
    public CozeAgentInfoResponseDto getAgentInfo(TenantAuthInfoEntity tenantAuthInfoEntity, String accessToken) {
        try {
            HttpRequest request = HttpRequest
                    .get(CustomConstant.COZE_PREFIX_URL + "v1/bot/get_online_info")
                    .header(CustomConstant.HEADER_AUTHORIZATION, "Bearer " + accessToken)
                    .form("bot_id", tenantAuthInfoEntity.getAgentId());
            try (HttpResponse response = request.execute()) {
                String body = response.body();
                log.info("调用COZE官方API：{}，请求参数：{}，响应内容：{}", request.getUrl(), objectMapper.writeValueAsString(tenantAuthInfoEntity), body);

                JsonNode jsonNode = objectMapper.readTree(body);
                String code = jsonNode.at("/code").asText();
                String msg = jsonNode.at("/msg").asText();
                if (String.valueOf(ErrorCode.COZE_RESOURCE_NOT_FOUND.getStatus()).equalsIgnoreCase(code)) {
                    throw new BusinessException(String.valueOf(ErrorCode.COZE_RESOURCE_NOT_FOUND.getStatus()), ErrorCode.COZE_RESOURCE_NOT_FOUND.getDefaultMessage());
                } else if (String.valueOf(ErrorCode.COZE_ACCESS_DENIED.getStatus()).equalsIgnoreCase(code)) {
                    throw new BusinessException(String.valueOf(ErrorCode.COZE_ACCESS_DENIED.getStatus()), ErrorCode.COZE_ACCESS_DENIED.getDefaultMessage());
                } else if (String.valueOf(ErrorCode.COZE_INVALID_PARAM.getStatus()).equalsIgnoreCase(code)) {
                    throw new BusinessException(String.valueOf(ErrorCode.COZE_INVALID_PARAM.getStatus()), ErrorCode.COZE_INVALID_PARAM.getDefaultMessage());
                } else if (String.valueOf(ErrorCode.COZE_SUCCESS.getStatus()).equalsIgnoreCase(code)) {
                    return objectMapper.readValue(body, CozeAgentInfoResponseDto.class);
                }
                throw new BusinessException(String.valueOf(ErrorCode.COZE_FAILED.getStatus()), msg);
            }
        } catch (Exception e) {
            throw new BusinessException(ErrorCode.COZE_ERROR, e.getMessage());
        }
    }

    /**
     * 构建 SSE 请求
     */
    private Request buildRequest(CozeChatRequestDto dto, String accessToken) {
        try {
            String requestBody = objectMapper.writeValueAsString(dto);
            RequestBody body = RequestBody.create(requestBody, MediaType.parse("application/json"));

            if (StringUtils.isNotBlank(dto.getConversationId())) {
                return new Request.Builder()
                        .url(CustomConstant.COZE_PREFIX_URL + "v3/chat?conversation_id=" + dto.getConversationId())
                        .post(body)
                        .header(CustomConstant.HEADER_AUTHORIZATION, "Bearer " + accessToken)
                        .header("Content-Type", "application/json")
                        .header("Accept", "text/event-stream")
                        .header("Cache-Control", "no-cache")
                        .header("Connection", "keep-alive")
                        .build();
            }
            return new Request.Builder()
                    .url(CustomConstant.COZE_PREFIX_URL + "v3/chat")
                    .post(body)
                    .header(CustomConstant.HEADER_AUTHORIZATION, "Bearer " + accessToken)
                    .header("Content-Type", "application/json")
                    .header("Accept", "text/event-stream")
                    .header("Cache-Control", "no-cache")
                    .header("Connection", "keep-alive")
                    .build();

        } catch (Exception e) {
            throw new BusinessException("构建 COZE SSE 请求失败", e.getMessage());
        }
    }

    /**
     * 执行异步请求并处理响应
     */
    private void executeAsyncRequest(Request request, FluxSink<String> sink) {
        okHttpClient.newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(@NonNull Call call, @NonNull IOException e) {
                log.error("调用 COZE SSE API 失败: {}", e.getMessage());
                sink.error(new BusinessException("调用 COZE SSE API 失败", e.getMessage()));
            }

            @Override
            public void onResponse(@NonNull Call call, @NonNull Response response) {
                processResponse(response, sink);
            }
        });
    }

    /**
     * 处理 HTTP 响应
     */
    private void processResponse(Response response, FluxSink<String> sink) {
        if (!response.isSuccessful()) {
            log.error("调用 COZE SSE API 失败，状态码: {}", response.code());
            sink.error(new BusinessException("响应无效，状态码: " + response.code(), ""));
            return;
        }

        try (ResponseBody responseBody = response.body()) {
            if (responseBody == null) {
                log.error("响应体为空");
                sink.error(new BusinessException("响应体为空", ""));
                return;
            }
            processSseStream(responseBody, sink);
        } catch (Exception e) {
            log.error("处理 SSE 流失败: {}", e.getMessage());
            sink.error(new BusinessException("处理 SSE 流失败", e.getMessage()));
        } finally {
            sink.complete();
        }
    }

    /**
     * 处理 SSE 流
     */
    private void processSseStream(ResponseBody responseBody, FluxSink<String> sink) throws IOException {
        BufferedReader reader = new BufferedReader(
                new InputStreamReader(responseBody.byteStream(), StandardCharsets.UTF_8));
        String line;
        String eventType = null;
        StringBuilder dataBuilder = new StringBuilder();
        while ((line = reader.readLine()) != null) {
            if (line.isEmpty()) {
                if (eventType != null && !dataBuilder.isEmpty()) {
                    processEvent(eventType, dataBuilder.toString(), sink);
                    eventType = null;
                    dataBuilder.setLength(0);
                }
                continue;
            }
            if (line.startsWith("event:")) {
                eventType = line.substring("event:".length()).trim();
            } else if (line.startsWith("data:")) {
                dataBuilder.append(line.substring("data:".length()).trim());
            } else {
                JsonNode jsonNode = objectMapper.readTree(line);
                String code = jsonNode.at("/code").asText();
                String cozeMessage = jsonNode.at("/message").asText();
                sink.next("event:" + "conversation.chat.failed");
                String message = "";
                if (String.valueOf(ErrorCode.COZE_INVALID_PARAM.getStatus()).equalsIgnoreCase(code)) {
                    sink.next(objectMapper.writeValueAsString(Result.error(ErrorCode.COZE_INVALID_PARAM.getCode(), cozeMessage)));
                } else if (String.valueOf(ErrorCode.COZE_QUOTA_EXCEEDED.getStatus()).equalsIgnoreCase(code)) {
                    message = ErrorCode.COZE_QUOTA_EXCEEDED.getDefaultMessage();
                    sink.next(objectMapper.writeValueAsString(Result.error(ErrorCode.COZE_QUOTA_EXCEEDED.getCode(), message)));
                } else if (String.valueOf(ErrorCode.COZE_EXCEED_MAX_RPM.getStatus()).equalsIgnoreCase(code)) {
                    message = ErrorCode.COZE_EXCEED_MAX_RPM.getDefaultMessage();
                    sink.next(objectMapper.writeValueAsString(Result.error(ErrorCode.COZE_EXCEED_MAX_RPM.getCode(), message)));
                } else if (String.valueOf(ErrorCode.COZE_NO_ENOUGH_CREDIT.getStatus()).equalsIgnoreCase(code)) {
                    message = ErrorCode.COZE_NO_ENOUGH_CREDIT.getDefaultMessage();
                    sink.next(objectMapper.writeValueAsString(Result.error(ErrorCode.COZE_NO_ENOUGH_CREDIT.getCode(), message)));
                } else if (String.valueOf(ErrorCode.COZE_TOKEN_INVALID.getStatus()).equalsIgnoreCase(code)) {
                    message = ErrorCode.COZE_TOKEN_INVALID.getDefaultMessage();
                    sink.next(objectMapper.writeValueAsString(Result.error(ErrorCode.COZE_TOKEN_INVALID.getCode(), message)));
                }
                if (StringUtils.isNotBlank(message)) {
                    log.error("COZE SSE 流调用返回错误事件：{}", message);
                } else {
                    log.error("COZE SSE 流调用返回错误事件：{}", cozeMessage);
                }
                sink.complete();
                return;
            }
        }
    }

    /**
     * 处理 SSE 事件
     */
    private void processEvent(String eventType, String data, FluxSink<String> sink) {
        try {
            JsonNode eventData = objectMapper.readTree(data);
            String evenDataJsonString = objectMapper.writeValueAsString(eventData);
            
            switch (eventType) {
                case "conversation.message.delta":
                    if (eventData.has(CONTENT)) {
                        String content = eventData.get(CONTENT).asText();
                        if (chatFlowConfigEntity.containsAnswer(content)) {
                            sink.next("event:" + CustomConstant.SSE_EVENT_TYPE_NOT_MATCH);
                            sink.next(evenDataJsonString);
                            log.info("当前问题在知识库未匹配上：{}", "event:" + CustomConstant.SSE_EVENT_TYPE_NOT_MATCH);
                            log.debug("当前问题在知识库未匹配上，消息ID：{}", eventData.at("/chat_id").asText());
                            sink.complete();
                            break;
                        }
                        sink.next("event:" + eventType);
                        sink.next(evenDataJsonString);
                        log.info("收到消息增量: {}", content);
                    }
                    break;

                case "conversation.message.completed":
                    sink.next("event:" + eventType);
                    log.info("已收到全部信息： {}", eventType);
                    break;

                case "conversation.chat.completed":
                    sink.next("event:" + eventType);
                    log.info("对话已完成： {}", eventType);
                    break;

                case "conversation.chat.failed":
                    sink.next("event:" + eventType);
                    String code = eventData.at("/last_error/code").asText();
                    String defaultMessage = "";
                    if (String.valueOf(ErrorCode.COZE_QUOTA_EXCEEDED.getStatus()).equals(code)) {
                        sink.next(objectMapper.writeValueAsString(Result.error(ErrorCode.COZE_QUOTA_EXCEEDED.getCode(), ErrorCode.COZE_QUOTA_EXCEEDED.getDefaultMessage())));
                        defaultMessage = ErrorCode.COZE_QUOTA_EXCEEDED.getDefaultMessage();
                    } else if (String.valueOf(ErrorCode.COZE_RESOURCE_NOT_FOUND.getStatus()).equals(code)) {
                        sink.next(objectMapper.writeValueAsString(Result.error(ErrorCode.COZE_RESOURCE_NOT_FOUND.getCode(), ErrorCode.COZE_RESOURCE_NOT_FOUND.getDefaultMessage())));
                        defaultMessage = ErrorCode.COZE_RESOURCE_NOT_FOUND.getDefaultMessage();
                    } else if (String.valueOf(ErrorCode.COZE_NO_ENOUGH_CREDIT.getStatus()).equals(code)) {
                        sink.next(objectMapper.writeValueAsString(Result.error(ErrorCode.COZE_NO_ENOUGH_CREDIT.getCode(), ErrorCode.COZE_NO_ENOUGH_CREDIT.getDefaultMessage())));
                        defaultMessage = ErrorCode.COZE_NO_ENOUGH_CREDIT.getDefaultMessage();
                    } else if (String.valueOf(ErrorCode.COZE_ACCOUNT_HAS_UNPAID_BILLS.getStatus()).equals(code)) {
                        sink.next(objectMapper.writeValueAsString(Result.error(ErrorCode.COZE_ACCOUNT_HAS_UNPAID_BILLS.getCode(), ErrorCode.COZE_ACCOUNT_HAS_UNPAID_BILLS.getDefaultMessage())));
                        defaultMessage = ErrorCode.COZE_ACCOUNT_HAS_UNPAID_BILLS.getDefaultMessage();
                    }
                    log.error("COZE SSE 流调用返回错误事件：{}", defaultMessage);
                    sink.complete();
                    break;

                case "done":
                    sink.next("event:" + eventType);
                    log.info("SSE 事件完成：{}", eventType);
                    break;

                default:
                    log.debug("未处理事件类型: {}", eventType);
            }
        } catch (Exception e) {
            log.error("处理 SSE 事件失败，事件类型: {}, 数据: {}", eventType, data, e);
        }
    }
}

