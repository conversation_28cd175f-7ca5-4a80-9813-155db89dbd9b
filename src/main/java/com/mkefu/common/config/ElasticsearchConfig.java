package com.mkefu.common.config;

import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.json.jackson.JacksonJsonpMapper;
import co.elastic.clients.transport.ElasticsearchTransport;
import co.elastic.clients.transport.rest_client.RestClientTransport;
import com.mkefu.common.entity.ElasticsearchConfigEntity;
import org.apache.http.HttpHost;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.client.CredentialsProvider;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestClientBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Elasticsearch 配置类
 * <AUTHOR>
 * @since 2025-04-10
 */
@Configuration
public class ElasticsearchConfig {

    private final ElasticsearchConfigEntity elasticsearchConfigEntity;

    public ElasticsearchConfig(ElasticsearchConfigEntity elasticsearchConfigEntity) {
        this.elasticsearchConfigEntity = elasticsearchConfigEntity;
    }

    /**
     * 创建 ElasticsearchClient
     * @return ElasticsearchClient
     */
    @Bean
    public ElasticsearchClient elasticsearchClient() {
        // 解析 uris（支持多个地址）
        String[] uriArray = elasticsearchConfigEntity.getUris().split(",");
        HttpHost[] httpHosts = new HttpHost[uriArray.length];
        for (int i = 0; i < uriArray.length; i++) {
            String uri = uriArray[i].trim();
            // 假设 uri 格式为 http://host:port
            String[] parts = uri.split("://");
            String scheme = parts[0];
            String hostPort = parts[1];
            String[] hostPortParts = hostPort.split(":");
            String host = hostPortParts[0];
            int port = Integer.parseInt(hostPortParts[1]);
            httpHosts[i] = new HttpHost(host, port, scheme);
        }

        // 配置认证信息
        CredentialsProvider credentialsProvider = new BasicCredentialsProvider();
        credentialsProvider.setCredentials(AuthScope.ANY,
                new UsernamePasswordCredentials(elasticsearchConfigEntity.getUsername(), elasticsearchConfigEntity.getPassword()));

        // 创建 RestClient
        RestClientBuilder builder = RestClient.builder(httpHosts)
                .setHttpClientConfigCallback(httpClientBuilder -> httpClientBuilder
                        .setDefaultCredentialsProvider(credentialsProvider));

        RestClient restClient = builder.build();

        // 创建 Transport 和 Client
        ElasticsearchTransport transport = new RestClientTransport(restClient, new JacksonJsonpMapper());
        return new ElasticsearchClient(transport);
    }
}
