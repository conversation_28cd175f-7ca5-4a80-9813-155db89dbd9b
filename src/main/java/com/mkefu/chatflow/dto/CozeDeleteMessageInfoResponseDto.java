package com.mkefu.chatflow.dto;

import com.mkefu.chatflow.entity.CozeBaseResponseEntity;
import com.mkefu.chatflow.entity.OpenMessageApiEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR> Yang 杨国锋
 * @since 2025-05-09
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "删除消息响应实体类", description = "CozeDeleteMessageInfoResponseDto")
@EqualsAndHashCode(callSuper = true)
public class CozeDeleteMessageInfoResponseDto extends CozeBaseResponseEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "已删除的 Message 详情")
    private OpenMessageApiEntity data;
}
