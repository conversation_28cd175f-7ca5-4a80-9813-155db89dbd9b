package com.mkefu.chatflow.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 租户与节点的分配关系实体，存储租户分配的节点信息
 * <AUTHOR> Yang 杨国锋
 * @since 2025-05-14
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("tenant_node_assignment")
@Schema(name = "租户与节点的分配关系实体类", description = "DifyTenantNodeAssignmentEntity")
public class DifyTenantNodeAssignmentEntity implements Serializable {

    @Schema(description = "租户唯一标识")
    @TableId("tenant_id")
    private String tenantId;

    @Schema(description = "节点实例 ID")
    @TableField("instance_id")
    private String instanceId;

    @Schema(description = "分配时间")
    @TableField("assigned_at")
    private LocalDateTime assignedAt;
}
