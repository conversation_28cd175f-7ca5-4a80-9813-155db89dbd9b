package com.mkefu.wechat.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 类描述
 *
 * <AUTHOR> 杨国锋
 * @since 2025-07-07
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "微信菜单项配置", description = "WeChatMsgMenuEntity")
public class WeChatMsgMenuEntity {

    @Schema(description = "菜单类型。" +
            "click-回复菜单 view-超链接菜单 miniprogram-小程序菜单")
    private String msgMenuType;

    @Schema(description = "菜单ID")
    private String msgMenuClickId;

    @Schema(description = "菜单显示内容")
    private String msgMenuClickContent;

    @Schema(description = "点击后跳转的链接")
    private String msgMenuViewUrl;

    @Schema(description = "菜单显示内容")
    private String msgMenuViewContent;

    @Schema(description = "小程序appid")
    private String msgMenuMiniProgramAppId;

    @Schema(description = "点击后进入的小程序页面")
    private String msgMenuMiniProgramPagePath;

    @Schema(description = "菜单显示内容")
    private String msgMenuMiniProgramContent;

    @Schema(description = "结束文本")
    private String msgMenuTailContent;
}
