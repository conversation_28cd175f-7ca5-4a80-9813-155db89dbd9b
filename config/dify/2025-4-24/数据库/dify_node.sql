CREATE TABLE "public"."dify_nodes"
(
    "instance_id" varchar(50) COLLATE "pg_catalog"."default"  NOT NULL,
    "url"         varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
    "active"      bool         DEFAULT true,
    "created_at"  timestamp(6) DEFAULT CURRENT_TIMESTAMP,
    "updated_at"  timestamp(6) DEFAULT CURRENT_TIMESTAMP,
    "api_key"     varchar(255) COLLATE "pg_catalog"."default",
    "latency"     float8,
    "load"        float8,
    CONSTRAINT "dify_nodes_pkey" PRIMARY KEY ("instance_id")
)
;

ALTER TABLE "public"."dify_nodes"
    OWNER TO "postgres";

COMMENT
ON COLUMN "public"."dify_nodes"."instance_id" IS 'Dify 节点的唯一标识';

COMMENT
ON COLUMN "public"."dify_nodes"."url" IS 'Dify 节点的访问地址';

COMMENT
ON COLUMN "public"."dify_nodes"."active" IS '节点是否可用，TRUE 表示可用';

COMMENT
ON COLUMN "public"."dify_nodes"."created_at" IS '记录创建时间';

COMMENT
ON COLUMN "public"."dify_nodes"."updated_at" IS '记录最后更新时间';

COMMENT
ON COLUMN "public"."dify_nodes"."api_key" IS 'Api Key';

COMMENT
ON COLUMN "public"."dify_nodes"."latency" IS '延迟字段，单位：毫秒';

COMMENT
ON COLUMN "public"."dify_nodes"."load" IS '负载字段，范围：0-1';

COMMENT
ON TABLE "public"."dify_nodes" IS '存储 Dify 节点信息的表，用于多服务器分布式部署';
