singleServerConfig:
  address: redis://*************:6379
  password: 123!@#asdASD
  database: 1
  idleConnectionTimeout: 10000
  connectTimeout: 10000
  timeout: 3000
  retryAttempts: 3
  retryInterval: 1500
  subscriptionsPerConnection: 5
  subscriptionConnectionMinimumIdleSize: 1
  subscriptionConnectionPoolSize: 1
  connectionMinimumIdleSize: 1
  connectionPoolSize: 1
threads: 0
nettyThreads: 0
codec: !org.redisson.codec.JsonJacksonCodec {}
transportMode: NIO
