package com.mkefu.chatflow.service.impl;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mkefu.chatflow.dto.*;
import com.mkefu.common.constant.CustomConstant;
import com.mkefu.common.constant.ErrorCode;
import com.mkefu.common.exception.BusinessException;
import com.mkefu.chatflow.service.CozeConversationService;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <AUTHOR> 杨国锋
 * @since 2025-05-08
 */
@Slf4j
@Schema(description = "创建会话服务实现类")
@Service
public class CozeConversationServiceImpl implements CozeConversationService {

    private final ObjectMapper objectMapper;

    public CozeConversationServiceImpl(ObjectMapper objectMapper) {
        this.objectMapper = objectMapper;
    }

    /**
     * 创建会话
     * @param dto 请求体
     * @param accessToken 访问令牌
     * @return 响应体
     */
    @Override
    public Map<String, Object> createConversation(CozeConversationRequestDto dto, String accessToken) {
        try {
            HttpRequest request = HttpRequest
                    .post(CustomConstant.COZE_PREFIX_URL + "v1/conversation/create")
                    .header(CustomConstant.HEADER_AUTHORIZATION, "Bearer " + accessToken)
                    .body(objectMapper.writeValueAsString(dto));
            log.info("调用COZE官方API：{}，请求参数：{}", request.getUrl(), objectMapper.writeValueAsString(dto));
            try (HttpResponse response = request.execute()) {
                String body = response.body();
                log.info("调用COZE官方API：{}，响应结果：{}", request.getUrl(), body);
                return objectMapper.readValue(body, new TypeReference<>() {});
            }
        } catch (Exception e) {
            throw new BusinessException(ErrorCode.COZE_ERROR);
        }
    }

    /**
     * 获取会话列表
     * @param botId 智能体ID
     * @param accessToken 访问令牌
     * @return 响应体
     */
    @Override
    public CozeConversationListResponseDto getConversationList(String botId, String accessToken) {
        try {
            HttpRequest request = HttpRequest
                    .get(CustomConstant.COZE_PREFIX_URL + "v1/conversations")
                    .header(CustomConstant.HEADER_AUTHORIZATION, "Bearer " + accessToken)
                    .form("bot_id", botId)
                    .form("sort_order", "desc");
            try (HttpResponse response = request.execute()) {
                log.info("调用COZE官方API：{}，请求参数：{}", request.getUrl(), objectMapper.writeValueAsString(request.form()));
                String body = response.body();
                log.info("调用COZE官方API：{}，响应结果：{}", request.getUrl(), body);
                return objectMapper.readValue(body, CozeConversationListResponseDto.class);
            }
        } catch (Exception e) {
            throw new BusinessException(ErrorCode.COZE_ERROR, e.getMessage());
        }
    }

    /**
     * 获取会话信息
     * @param conversationId 会话ID
     * @param accessToken 访问令牌
     * @return 响应体
     */
    @Override
    public Map<String, Object> getConversationInfo(String conversationId, String accessToken) {
        try {
            HttpRequest request = HttpRequest
                    .get(CustomConstant.COZE_PREFIX_URL + "v1/conversation/retrieve")
                    .header(CustomConstant.HEADER_AUTHORIZATION, "Bearer " + accessToken)
                    .form("conversation_id", conversationId);
            try (HttpResponse response = request.execute()) {
                String body = response.body();
                log.info("调用COZE官方API：{}，响应结果：{}", request.getUrl(), body);
                return objectMapper.readValue(body, new TypeReference<>() {});
            }
        } catch (Exception e) {
            throw new BusinessException(ErrorCode.COZE_ERROR, e.getMessage());
        }
    }

    /**
     * 清除会话上下文
     * @param conversationId 会话ID
     * @param accessToken 访问令牌
     * @return 响应体
     */
    @Override
    public CozeCleanContextResponseDto cleanContext(String conversationId, String accessToken) {
        try {
            HttpRequest request = HttpRequest
                    .post(CustomConstant.COZE_PREFIX_URL + "v1/conversations/" + conversationId + "/clear")
                    .header(CustomConstant.HEADER_AUTHORIZATION, "Bearer " + accessToken);
            try (HttpResponse response = request.execute()) {
                String body = response.body();
                log.info("调用COZE官方API：{}，响应结果：{}", request.getUrl(), body);
                return objectMapper.readValue(body, CozeCleanContextResponseDto.class);
            }
        } catch (Exception e) {
            throw new BusinessException(ErrorCode.COZE_ERROR, e.getMessage());
        }
    }

    /**
     * 创建消息
     * @param map 请求体
     * @param accessToken 访问令牌
     * @return 响应体
     */
    @Override
    public CozeCreateMessageResponseDto createMessage(Map<String, Object> map, String accessToken) {
        try {
            String conversationId = map.get("conversation_id").toString();
            map.remove("conversation_id");
            HttpRequest request = HttpRequest
                    .post(CustomConstant.COZE_PREFIX_URL + "v1/conversation/message/create?conversation_id=" + conversationId)
                    .header(CustomConstant.HEADER_AUTHORIZATION, "Bearer " + accessToken)
                    .body(objectMapper.writeValueAsString(map));
            try (HttpResponse response = request.execute()) {
                String body = response.body();
                log.info("调用COZE官方API：{}，请求参数：{}，响应结果：{}", request.getUrl(), objectMapper.writeValueAsString(map), body);
                return objectMapper.readValue(body, CozeCreateMessageResponseDto.class);
            }
        } catch (Exception e) {
            throw new BusinessException(ErrorCode.COZE_ERROR, e.getMessage());
        }
    }

    /**
     * 查看消息列表
     * @param map 请求体
     * @param accessToken 访问令牌
     * @return 响应体
     */
    @Override
    public CozeGetMessageListResponseDto getMessageList(Map<String, Object> map, String accessToken) {
        try {
            String conversationId = map.get("conversationId").toString();
            map.remove("conversationId");
            HttpRequest request = HttpRequest
                    .post(CustomConstant.COZE_PREFIX_URL + "v1/conversation/message/list?conversation_id=" + conversationId)
                    .header(CustomConstant.HEADER_AUTHORIZATION, "Bearer " + accessToken)
                    .body(objectMapper.writeValueAsString(map));
            try (HttpResponse response = request.execute()) {
                String body = response.body();
                log.info("调用COZE官方API：{}，请求参数：{}，响应结果：{}", request.getUrl(), objectMapper.writeValueAsString(map), body);
                return objectMapper.readValue(body, CozeGetMessageListResponseDto.class);
            }
        } catch (Exception e) {
            throw new BusinessException(ErrorCode.COZE_ERROR, e.getMessage());
        }
    }

    /**
     * 查看消息详情
     * @param conversationId 会话ID
     * @param messageId 消息ID
     * @param accessToken 访问令牌
     * @return 响应体
     */
    @Override
    public CozeGetMessageInfoResponseDto getMessageInfo(String conversationId, String messageId, String accessToken) {
        Map<String, Object> params = Map.of(
                "conversation_id", conversationId,
                "message_id", messageId
        );
        try {
            HttpRequest request = HttpRequest
                    .get(CustomConstant.COZE_PREFIX_URL + "v1/conversation/message/retrieve")
                    .header(CustomConstant.HEADER_AUTHORIZATION, "Bearer " + accessToken)
                    .form(params);
            try (HttpResponse response = request.execute()) {
                String body = response.body();
                log.info("调用COZE官方API：{}，请求参数：{}，响应结果：{}", request.getUrl(), objectMapper.writeValueAsString(params), body);
                return objectMapper.readValue(body, CozeGetMessageInfoResponseDto.class);
            }
        } catch (Exception e) {
            throw new BusinessException(ErrorCode.COZE_ERROR, e.getMessage());
        }
    }

    /**
     * 修改消息
     * @param map 请求体
     * @param accessToken 访问令牌
     * @return 响应体
     */
    @Override
    public CozeUpdateMessageInfoResponseDto updateMessageInfo(Map<String, Object> map, String accessToken) {
        String conversationId = map.get("conversationId").toString();
        String messageId = map.get("messageId").toString();
        map.remove("conversationId");
        map.remove("messageId");
        try {
            HttpRequest request = HttpRequest
                    .post(CustomConstant.COZE_PREFIX_URL + "v1/conversation/message/modify?conversation_id=" + conversationId + "&message_id=" + messageId)
                    .header(CustomConstant.HEADER_AUTHORIZATION, "Bearer " + accessToken)
                    .body(objectMapper.writeValueAsString(map));
            try (HttpResponse response = request.execute()) {
                String body = response.body();
                log.info("调用COZE官方API：{}，body请求参数：{}，响应结果：{}", request.getUrl(), objectMapper.writeValueAsString(map), body);
                return objectMapper.readValue(body, CozeUpdateMessageInfoResponseDto.class);
            }
        } catch (Exception e) {
            throw new BusinessException(ErrorCode.COZE_ERROR, e.getMessage());
        }
    }

    /**
     * 删除消息
     * @param map 删除消息请求体
     * @param accessToken 访问令牌
     * @return 响应体
     */
    @Override
    public CozeDeleteMessageInfoResponseDto deleteMessageInfo(Map<String, Object> map, String accessToken) {
        String conversationId = map.get("conversationId").toString();
        String messageId = map.get("messageId").toString();
        try {
            HttpRequest request = HttpRequest
                    .post(CustomConstant.COZE_PREFIX_URL + "v1/conversation/message/delete?conversation_id=" + conversationId + "&message_id=" + messageId)
                    .header(CustomConstant.HEADER_AUTHORIZATION, "Bearer " + accessToken);
            try (HttpResponse response = request.execute()) {
                String body = response.body();
                log.info("调用COZE官方API：{}，body请求参数：{}，响应结果：{}", request.getUrl(), objectMapper.writeValueAsString(map), body);
                return objectMapper.readValue(body, CozeDeleteMessageInfoResponseDto.class);
            }
        } catch (Exception e) {
            throw new BusinessException(ErrorCode.COZE_ERROR, e.getMessage());
        }
    }
}
