package com.mkefu.common.util;

import cn.hutool.core.net.URLEncodeUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.crypto.digest.DigestUtil;
import com.mkefu.common.annotation.LogInteraction;
import com.mkefu.common.constant.BaseCode;
import com.mkefu.common.entity.KuaidiniaoConfigEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.Map;

/**
 * 快递鸟签名工具
 * <AUTHOR>
 * @since 2025-04-10
 */
@Slf4j
@Component
public class KdniaoSignUtil {

    private final KuaidiniaoConfigEntity kuaidiniaoConfigEntity;

    public KdniaoSignUtil(KuaidiniaoConfigEntity kuaidiniaoConfigEntity) {
        this.kuaidiniaoConfigEntity = kuaidiniaoConfigEntity;
    }

    /**
     * 生成签名
     * @param map 请求参数
     * @return 签名
     */
    @LogInteraction(description = "生成快递鸟签名")
    public String generateDataSign(Map<String, Object> map) {
        // 1. 校验参数
        if (map == null || !map.containsKey(BaseCode.REQUEST_DATA.getCode())) {
            throw new IllegalArgumentException("RequestData 参数不能为空");
        }
        String requestData = String.valueOf(map.get("RequestData")).trim();
        if (CharSequenceUtil.isBlank(requestData)) {
            throw new IllegalArgumentException("RequestData 内容不能为空");
        }

        // 2. 获取 ApiKey
        String apiKey = kuaidiniaoConfigEntity.getApiKey().trim();
        if (CharSequenceUtil.isBlank(apiKey)) {
            throw new IllegalArgumentException("ApiKey 不能为空");
        }

        // 3. 拼接 RequestData 和 ApiKey
        String data = requestData + apiKey;

        // 4. MD5 加密（32 位小写）
        String md5 = DigestUtil.md5Hex(data);
        log.debug("MD5 结果: {}", md5);

        // 5. Base64 编码并移除任何可能的空格
        String base64 = Base64.getEncoder().encodeToString(md5.getBytes(StandardCharsets.UTF_8))
                .replaceAll("\\s+", "");
        log.debug("Base64 编码结果: {}", base64);

        // 6. URL 编码（UTF-8）
        String urlEncoded = URLEncodeUtil.encode(base64, StandardCharsets.UTF_8);
        log.debug("URL 编码结果: {}", urlEncoded);
        return urlEncoded;
    }

}
