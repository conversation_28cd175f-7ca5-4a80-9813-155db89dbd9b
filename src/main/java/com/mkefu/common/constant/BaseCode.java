package com.mkefu.common.constant;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

/**
 * 通用枚举类
 * <AUTHOR>
 * @since 2025-04-10
 */
@Getter
public enum BaseCode {

    /**
     * 字符串0
     */
    ZERO("0"),

    /**
     * 字符串1
     */
    ONE("1"),

    /**
     * 字符串2
     */
    TWO("2"),

    /**
     * 字符串3
     */
    THREE("3"),

    /**
     * 字符串4
     */
    FOUR("4"),

    /**
     * 字符串32
     */
    THIRTY_TWO("32"),

    /**
     * 字符串RequestData
     */
    REQUEST_DATA("RequestData"),

    /**
     * 字符串:
     */
    COLON(":"),

    /**
     * 字符串json
     */
    JSON("json"),

    /**
     * 字符串unknown
     */
    UNKNOWN("unknown"),

    /**
     * 字符串tenantSecretKeys
     */
    TENANT_SECRET_KEYS("tenantSecretKeys"),

    /**
     * 流式模式（推荐）。基于 SSE（Server-Sent Events）实现类似打字机输出方式的流式返回。
     */
    STREAMING("streaming"),

    /**
     * 阻塞模式，等待执行完毕后返回结果。（请求若流程较长可能会被中断）。 由于 Cloudflare 限制，请求会在 100 秒超时无返回后中断。
     */
    BLOCKING("blocking"),

    /**
     * dify
     */
    PLAT_FORM_TYPE_DIFY("dify"),

    /**
     * coze
     */
    PLAT_FORM_TYPE_COZE("coze"),

    /**
     * 官方API
     */
    API_TYPE_OFFICIAL("official"),

    /**
     * 自部署API
     */
    API_TYPE_SELF_HOSTED("self-hosted"),

    /**
     * 租户授权码Redis Key前缀
     */
    TENANT_AUTH_INFO_PREFIX("mkefu:tenant:authCode:"),

    /**
     * 租户私钥Redis Key前缀
     */
    SECRET_KEY_PREFIX("mkefu:tenantSecretKeys:"),

    /**
     * 微信回调 Redis Key前缀
     */
    WECHAT_PERMANENT_CODE("weChat:permanentCode:"),

    /**
     * 微信预授权码Redis Key前缀
     */
    WECHAT_THIRD_PARTY_PRE_AUTH_CODE("weChat:thirdPartyPreAuthCode:"),

    /**
     * 微信第三方平台 AccessToken Redis Key前缀
     */
    WECHAT_SUITE_ACCESS_TOKEN("weChat:suiteAccessToken:"),
    
    /**
     * 微信企业 授权方AccessToken Redis Key前缀
     */
    WECHAT_CORP_ACCESS_TOKEN("weChat:corpAccessToken:"),

    /**
     * 微信企业 服务商Id Redis Key前缀
     */
    WECHAT_RECEIVE_ID("weChat:receiveId:"),

    /**
     * 授权链接UUID Redis Key前缀
     */
    WECHAT_AUTH_URL_UUID("weChat:authUrlUuid:"),

    /**
     * 微信客服信息 Redis Key前缀
     */
    WECHAT_KF_ACCOUNT_INFO("weChat:kfAccountInfo:"),

    /**
     * 微信客服信息 Redis Key前缀
     */
    WECHAT_NEXT_CURSOR_INFO("weChat:nextCursorInfo:"),
    
    /**
     * 事件的类型
     */
    WECHAT_EVENT("event"),
    
    /**
     * 租户私钥锁Redis Key前缀
     */
    LOCK_KEY_PREFIX("mkefu:lock:"),

    /**
     * 获取智能体信息
     */
    GET_AGENT_INFO_URL("/getAgentInfo"),

    /**
     * token
     */
    AUTHORIZATION("authorization"),

    /**
     * 字符串400
     */
    FOUR_HUNDRED("400"),

    /**
     * 答案
     */
    ANSWER("answer"),

    /**
     * 字符串404
     */
    FOUR_ZERO_FOUR("404");

    @Schema(description = "编码")
    private final String code;

    /**
     * 构造函数
     * @param code 编码
     */
    BaseCode(String code) {
        this.code = code;
    }
}
