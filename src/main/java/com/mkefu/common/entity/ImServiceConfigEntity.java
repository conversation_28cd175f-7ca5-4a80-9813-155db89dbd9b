package com.mkefu.common.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.io.Serial;
import java.io.Serializable;

/**
 * ImService服务配置Entity
 *
 * <AUTHOR> 杨国锋
 * @since 2025-07-18
 */
@Data
@Configuration
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "ImService服务配置Entity", description = "ImServiceConfigEntity")
@ConfigurationProperties(prefix = "im-service")
public class ImServiceConfigEntity implements Serializable {
    
    @Serial
    private static final long serialVersionUID = 1L;
    
    @Schema(description = "IM服务密钥")
    private String aesKey;
    
    @Schema(description = "IM服务IV")
    private String aesIv;
}
