package com.mkefu.chatflow.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR> 杨国锋
 * @since 2025-05-29
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "智能体配置的音色实体类。")
public class CozeVoiceInfoEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "音色的 ID。获取方法请参见查看音色列表。")
    @JsonProperty("voice_id")
    private String voiceId;

    @Schema(description = "此音色的语种代号。获取方法请参见查看音色列表。")
    @JsonProperty("language_code")
    private String languageCode;
}
