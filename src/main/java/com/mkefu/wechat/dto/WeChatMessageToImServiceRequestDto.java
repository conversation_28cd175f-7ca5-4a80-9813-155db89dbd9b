package com.mkefu.wechat.dto;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.mkefu.common.config.RawJsonStringDeserializerConfig;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * 类描述
 *
 * <AUTHOR> 杨国锋
 * @since 2025-07-07
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "聊天消息转发给Im-Service请求Dto" , description = "WeChatMessageToImServiceRequestDto")
public class WeChatMessageToImServiceRequestDto implements Serializable {
    
    @Serial
    private static final long serialVersionUID = 1L;
    
    @Schema(description = "租户Id")
    @JsonProperty("tenant_id")
    private String tenantId;

    @Schema(description = "渠道类型")
    @JsonProperty("plat_form_type")
    private String platFormType;

    @Schema(description = "微信用户Id")
    @JsonProperty("external_userid")
    private String externalUserId;

    @Schema(description = "授权方企Token")
    @JsonProperty("corp_access_token")
    private String corpAccessToken;
    
    @Schema(description = "客服Id")
    @JsonProperty("open_kfid")
    private String openKfId;
    
    @Schema(description = "微信客服聊天链接")
    @JsonProperty("kf_account_url")
    private String kfAccountUrl;

    @Schema(description = "消息类型")
    @JsonProperty("msgtype")
    private String msgType;

    @Schema(description = "授权方企业Id")
    @JsonProperty("corp_id")
    private String corpId;

    @Schema(description = "消息Id")
    @JsonProperty("msgid")
    private String msgId;

    @Schema(description = "不同类型的消息内容结构体")
    @JsonAlias({"text", "image", "voice", "video", "file", "textcard", "news", "mpnews", "miniprogrampage", "markdown", "wxcard", "location", "event"})
    @JsonDeserialize(using = RawJsonStringDeserializerConfig.class)
    private String msgContent;
}
