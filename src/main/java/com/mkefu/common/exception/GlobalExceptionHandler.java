package com.mkefu.common.exception;

import com.mkefu.common.response.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.resource.NoResourceFoundException;

import java.sql.SQLException;

/**
 * 全局异常处理器
 * <AUTHOR>
 * @since 2025-04-10
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

    /**
     * 处理业务异常
     * @param ex 业务异常
     * @return 响应结果
     */
    @ExceptionHandler(BusinessException.class)
    public ResponseEntity<Result> handleBusinessException(BusinessException ex) {
        log.warn("业务异常: code={}, message={}", ex.getErrorCode(), ex.getMessage());
        return ResponseEntity.status(ex.getStatusCode())
                .body(Result.failure(ex.getStatusCode(), ex.getErrorCode(), ex.getMessage()));
    }

    /**
     * 处理参数校验异常
     * @param ex 参数校验异常
     * @return 响应结果
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<Result> handleValidationException(MethodArgumentNotValidException ex) {
        String errorMessage = ex.getBindingResult().getAllErrors().getFirst().getDefaultMessage();
        log.warn("参数校验失败: {}", errorMessage);
        return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(Result.badRequest("VALIDATION_FAILED", errorMessage));
    }

    /**
     * 处理非法参数异常
     * @param ex 非法参数异常
     * @return 响应结果
     */
    @ExceptionHandler(IllegalArgumentException.class)
    public ResponseEntity<Result> handleIllegalArgumentException(IllegalArgumentException ex) {
        log.warn("非法参数: {}", ex.getMessage());
        return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(Result.badRequest("INVALID_ARGUMENT", ex.getMessage()));
    }

    /**
     * 处理未捕获的异常
     * @param ex 未捕获的异常
     * @return 响应结果
     */
    @ExceptionHandler(Exception.class)
    public ResponseEntity<Result> handleGeneralException(Exception ex) {
        log.error("发生未捕获的异常", ex);
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(Result.error("SERVER_ERROR", "服务器内部错误: " + ex.getMessage()));
    }

    /**
     * 处理SQL异常
     * @param ex SQL异常
     * @return 响应结果
     */
    @ExceptionHandler(SQLException.class)
    public ResponseEntity<Result> handleSqlException(SQLException ex) {
        log.error("数据库异常: {}", ex.getMessage(), ex);
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(Result.error("DATABASE_ERROR", "数据库操作失败: " + ex.getMessage()));
    }

    /**
     * 处理NoResourceFoundException
     * @param ex 404异常
     * @return 响应结果
     */
    @ExceptionHandler(NoResourceFoundException.class)
    public ResponseEntity<Result> handleNoResourceException(NoResourceFoundException ex) {
        log.warn("资源未找到: path={}, method={}, message={}",
                ex.getResourcePath(), ex.getHttpMethod(), ex.getMessage());
        return ResponseEntity.status(HttpStatus.NOT_FOUND)
                .body(Result.error("RESOURCE_NOT_FOUND", String.format("请求的资源不存在: %s", ex.getResourcePath()), HttpStatus.NOT_FOUND.value()));
    }
}
