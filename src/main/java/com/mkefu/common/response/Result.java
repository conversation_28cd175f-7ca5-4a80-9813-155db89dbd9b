package com.mkefu.common.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * 通用API响应类
 * <AUTHOR>
 * @since 2025-04-10
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "通用API响应类", description = "Result")
public class Result implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 是否成功
     */
    @Schema(description = "是否成功", example = "true")
    private Boolean success;

    /**
     * 状态码
     * 200: 成功
     * 400: 参数错误
     * 401: 未授权
     * 403: 禁止访问
     * 500: 服务器错误
     */
    @Schema(description = "状态码", example = "200")
    private Integer code;

    /**
     * 错误码（仅在失败时有值）
     */
    @Schema(description = "错误码", example = "404")
    private String error;

    /**
     * 响应消息
     */
    @Schema(description = "响应消息", example = "操作成功")
    private String message;

    /**
     * 响应数据（仅在成功时有值）
     */
    @Schema(description = "响应数据")
    private Object data;

    /**
     * 成功响应（无数据）
     */
    public static Result success() {
        return new Result(true, 200, null, "操作成功", null);
    }

    /**
     * 成功响应（带数据）
     */
    public static Result success(Object data) {
        return new Result(true, 200, null, "操作成功", data);
    }

    /**
     * 成功响应（自定义消息和数据）
     */
    public static Result success(String message, Object data) {
        return new Result(true, 200, null, message, data);
    }

    /**
     * 失败响应（自定义状态码、错误码和消息）
     */
    public static Result failure(int code, String error, String message) {
        return new Result(false, code, error, message, null);
    }

    /**
     * 参数错误响应
     */
    public static Result badRequest(String error, String message) {
        return new Result(false, 400, error, message, null);
    }

    /**
     * 未授权响应
     */
    public static Result unauthorized(String error, String message) {
        return new Result(false, 401, error, message, null);
    }

    /**
     * 服务器错误响应
     */
    public static Result error(String error, String message) {
        return new Result(false, 500, error, message, null);
    }

    /**
     * 服务器错误响应
     */
    public static Result error(String error, String message, int code) {
        return new Result(false, code, error, message, null);
    }

    /**
     * 判断响应是否成功
     */
    public boolean isSuccess() {
        return this.success != null && this.success;
    }
}
