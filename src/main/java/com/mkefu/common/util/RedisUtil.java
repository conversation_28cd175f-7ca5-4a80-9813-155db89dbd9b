package com.mkefu.common.util;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.mkefu.common.constant.BaseCode;
import com.mkefu.common.constant.ErrorCode;
import com.mkefu.common.exception.BusinessException;
import com.mkefu.system.entity.TenantAuthInfoEntity;
import com.mkefu.system.entity.query.TenantAuthInfoQuery;
import com.mkefu.system.mapper.TenantAuthInfoMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.redisson.client.RedisConnectionException;
import org.redisson.client.RedisTimeoutException;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

/**
 * Redis 通用工具类，封装 RedissonClient 的基础操作和分布式锁
 * <AUTHOR>
 * @since ：2025-04-11
 */
@Slf4j
@Component
public class RedisUtil {

    private final RedissonClient redissonClient;

    private final TenantAuthInfoMapper tenantAuthInfoMapper;

    public RedisUtil(RedissonClient redissonClient, TenantAuthInfoMapper tenantAuthInfoMapper) {
        this.redissonClient = redissonClient;
        this.tenantAuthInfoMapper = tenantAuthInfoMapper;
    }

    /**
     * 存储键值对到 Redis
     * @param key 键
     * @param value 值
     * @param <T> 值类型
     */
    public <T> void put(String key, T value) {
        if (key == null) {
            log.error("Redis key 为空，无法存储");
            throw new BusinessException(ErrorCode.INVALID_PARAM, "Redis key 不能为空");
        }

        String fullKey = BaseCode.SECRET_KEY_PREFIX.getCode() + key;
        RBucket<T> bucket = redissonClient.getBucket(fullKey);
        try {
            // 使用 setAsync，同步等待结果
            bucket.setAsync(value).get();
            log.debug("已存储到 Redis: key={}", fullKey);
        } catch (InterruptedException e) {
            log.error("存储到 Redis 时线程被中断: key={}, error={}", fullKey, e.getMessage());
            // 重新中断线程
            Thread.currentThread().interrupt();
            throw new BusinessException(ErrorCode.REDIS_PUT_ERROR, "Redis 存储操作被中断");
        } catch (ExecutionException e) {
            // unwrap 实际的异常原因
            Throwable cause = e.getCause();
            if (cause instanceof RedisConnectionException) {
                log.error("存储到 Redis 失败，连接问题: key={}, error={}", fullKey, cause.getMessage());
                throw new BusinessException(ErrorCode.REDIS_CONNECTION_ERROR, "Redis 存储失败：连接问题");
            } else if (cause instanceof RedisTimeoutException) {
                log.error("存储到 Redis 失败，操作超时: key={}, error={}", fullKey, cause.getMessage());
                throw new BusinessException(ErrorCode.REDIS_TIMEOUT_EXCEPTION, "Redis 存储失败：操作超时");
            } else {
                log.error("存储到 Redis 失败: key={}, error={}", fullKey, cause != null ? cause.getMessage() : e.getMessage());
                throw new BusinessException(ErrorCode.REDIS_PUT_ERROR, "Redis 存储失败");
            }
        } catch (Exception e) {
            // 捕获其他未预料的异常
            log.error("存储到 Redis 失败: key={}, error={}", fullKey, e.getMessage());
            throw new BusinessException(ErrorCode.REDIS_PUT_ERROR, "Redis 存储失败");
        }
    }

    /**
     * 存储键值对到 Redis，并设置过期时间
     * @param key 键
     * @param value 值
     * @param expireTime 过期时间
     * @param timeUnit 时间单位
     * @param <T> 值类型
     */
    public <T> void putByWeChatCorpToken(String key, T value, long expireTime, TimeUnit timeUnit) {
        if (key == null) {
            log.error("Redis key 为空，无法存储");
            throw new BusinessException(ErrorCode.INVALID_PARAM, "Redis key 不能为空");
        }
        if (expireTime <= 0) {
            log.error("Redis 过期时间必须大于 0: expireTime={}", expireTime);
            throw new BusinessException(ErrorCode.INVALID_PARAM, "Redis 过期时间必须大于 0");
        }

        String fullKey = BaseCode.WECHAT_PERMANENT_CODE.getCode() + key;
        RBucket<T> bucket = redissonClient.getBucket(fullKey);
        try {
            bucket.setAsync(value, expireTime, timeUnit).get();
            log.debug("已存储企业访问令牌到Redis: key={}, expireTime={} {}", fullKey, expireTime, timeUnit);
        } catch (InterruptedException e) {
            log.error("存储到 Redis 并设置过期时间时线程被中断: key={}, error={}", fullKey, e.getMessage());
            Thread.currentThread().interrupt();
            throw new BusinessException(ErrorCode.REDIS_PUT_ERROR, "Redis 存储操作被中断");
        } catch (ExecutionException e) {
            Throwable cause = e.getCause();
            if (cause instanceof RedisConnectionException) {
                log.error("存储到 Redis 并设置过期时间失败，连接问题: key={}, error={}", fullKey, cause.getMessage());
                throw new BusinessException(ErrorCode.REDIS_CONNECTION_ERROR, "Redis 存储失败：连接问题");
            } else if (cause instanceof RedisTimeoutException) {
                log.error("存储到 Redis 并设置过期时间失败，操作超时: key={}, error={}", fullKey, cause.getMessage());
                throw new BusinessException(ErrorCode.REDIS_TIMEOUT_EXCEPTION, "Redis 存储失败：操作超时");
            } else {
                log.error("存储到 Redis 并设置过期时间失败: key={}, error={}", fullKey, cause != null ? cause.getMessage() : e.getMessage());
                throw new BusinessException(ErrorCode.REDIS_PUT_ERROR, "Redis 存储失败");
            }
        } catch (Exception e) {
            log.error("存储到 Redis 并设置过期时间失败: key={}, error={}", fullKey, e.getMessage());
            throw new BusinessException(ErrorCode.REDIS_PUT_ERROR, "Redis 存储失败");
        }
    }

    /**
     * 存储键值对到 Redis，并设置过期时间
     * @param key 键
     * @param value 值
     * @param expireTime 过期时间
     * @param timeUnit 时间单位
     * @param <T> 值类型
     */
    public <T> void putByThirdPartyPreAuthCode(String key, T value, Integer expireTime, TimeUnit timeUnit) {
        if (key == null) {
            log.error("Redis key 为空，无法存储");
            throw new BusinessException(ErrorCode.INVALID_PARAM, "Redis key 不能为空");
        }
        if (expireTime <= 0) {
            log.error("Redis 过期时间必须大于 0: expireTime={}", expireTime);
            throw new BusinessException(ErrorCode.INVALID_PARAM, "Redis 过期时间必须大于 0");
        }

        String fullKey = BaseCode.WECHAT_THIRD_PARTY_PRE_AUTH_CODE.getCode() + key;
        RBucket<T> bucket = redissonClient.getBucket(fullKey);
        try {
            bucket.setAsync(value, expireTime, timeUnit).get();
            log.debug("已存储企业访问令牌到Redis: key={}, expireTime={} {}", fullKey, expireTime, timeUnit);
        } catch (InterruptedException e) {
            log.error("存储到 Redis 并设置过期时间时线程被中断: key={}, error={}", fullKey, e.getMessage());
            Thread.currentThread().interrupt();
            throw new BusinessException(ErrorCode.REDIS_PUT_ERROR, "Redis 存储操作被中断");
        } catch (ExecutionException e) {
            Throwable cause = e.getCause();
            if (cause instanceof RedisConnectionException) {
                log.error("存储到 Redis 并设置过期时间失败，连接问题: key={}, error={}", fullKey, cause.getMessage());
                throw new BusinessException(ErrorCode.REDIS_CONNECTION_ERROR, "Redis 存储失败：连接问题");
            } else if (cause instanceof RedisTimeoutException) {
                log.error("存储到 Redis 并设置过期时间失败，操作超时: key={}, error={}", fullKey, cause.getMessage());
                throw new BusinessException(ErrorCode.REDIS_TIMEOUT_EXCEPTION, "Redis 存储失败：操作超时");
            } else {
                log.error("存储到 Redis 并设置过期时间失败: key={}, error={}", fullKey, cause != null ? cause.getMessage() : e.getMessage());
                throw new BusinessException(ErrorCode.REDIS_PUT_ERROR, "Redis 存储失败");
            }
        } catch (Exception e) {
            log.error("存储到 Redis 并设置过期时间失败: key={}, error={}", fullKey, e.getMessage());
            throw new BusinessException(ErrorCode.REDIS_PUT_ERROR, "Redis 存储失败");
        }
    }

    /**
     * 存储键值对到 Redis，并设置过期时间
     * @param key 键
     * @param value 值
     * @param expireTime 过期时间
     * @param timeUnit 时间单位
     * @param <T> 值类型
     */
    public <T> void putBySuiteAccessToken(String key, T value, Integer expireTime, TimeUnit timeUnit) {
        if (key == null) {
            log.error("Redis key 为空，无法存储");
            throw new BusinessException(ErrorCode.INVALID_PARAM, "Redis key 不能为空");
        }
        if (expireTime <= 0) {
            log.error("Redis 过期时间必须大于 0: expireTime={}", expireTime);
            throw new BusinessException(ErrorCode.INVALID_PARAM, "Redis 过期时间必须大于 0");
        }

        String fullKey = BaseCode.WECHAT_SUITE_ACCESS_TOKEN.getCode() + key;
        RBucket<T> bucket = redissonClient.getBucket(fullKey);
        try {
            bucket.setAsync(value, expireTime, timeUnit).get();
            log.debug("已存储企业访问令牌到Redis: key={}, expireTime={} {}", fullKey, expireTime, timeUnit);
        } catch (InterruptedException e) {
            log.error("存储到 Redis 并设置过期时间时线程被中断: key={}, error={}", fullKey, e.getMessage());
            Thread.currentThread().interrupt();
            throw new BusinessException(ErrorCode.REDIS_PUT_ERROR, "Redis 存储操作被中断");
        } catch (ExecutionException e) {
            Throwable cause = e.getCause();
            if (cause instanceof RedisConnectionException) {
                log.error("存储到 Redis 并设置过期时间失败，连接问题: key={}, error={}", fullKey, cause.getMessage());
                throw new BusinessException(ErrorCode.REDIS_CONNECTION_ERROR, "Redis 存储失败：连接问题");
            } else if (cause instanceof RedisTimeoutException) {
                log.error("存储到 Redis 并设置过期时间失败，操作超时: key={}, error={}", fullKey, cause.getMessage());
                throw new BusinessException(ErrorCode.REDIS_TIMEOUT_EXCEPTION, "Redis 存储失败：操作超时");
            } else {
                log.error("存储到 Redis 并设置过期时间失败: key={}, error={}", fullKey, cause != null ? cause.getMessage() : e.getMessage());
                throw new BusinessException(ErrorCode.REDIS_PUT_ERROR, "Redis 存储失败");
            }
        } catch (Exception e) {
            log.error("存储到 Redis 并设置过期时间失败: key={}, error={}", fullKey, e.getMessage());
            throw new BusinessException(ErrorCode.REDIS_PUT_ERROR, "Redis 存储失败");
        }
    }

    /**
     * 存储键值对到 Redis，并设置过期时间
     * @param key 键
     * @param value 值
     * @param expireTime 过期时间
     * @param timeUnit 时间单位
     * @param <T> 值类型
     */
    public <T> void put(String key, T value, long expireTime, TimeUnit timeUnit) {
        if (key == null) {
            log.error("Redis key 为空，无法存储");
            throw new BusinessException(ErrorCode.INVALID_PARAM, "Redis key 不能为空");
        }
        if (expireTime <= 0) {
            log.error("Redis 过期时间必须大于 0: expireTime={}", expireTime);
            throw new BusinessException(ErrorCode.INVALID_PARAM, "Redis 过期时间必须大于 0");
        }

        String fullKey = BaseCode.SECRET_KEY_PREFIX.getCode() + key;
        RBucket<T> bucket = redissonClient.getBucket(fullKey);
        try {
            bucket.setAsync(value, expireTime, timeUnit).get();
            log.debug("已存储企业访问令牌到Redis: key={}, expireTime={} {}", fullKey, expireTime, timeUnit);
        } catch (InterruptedException e) {
            log.error("存储到 Redis 并设置过期时间时线程被中断: key={}, error={}", fullKey, e.getMessage());
            Thread.currentThread().interrupt();
            throw new BusinessException(ErrorCode.REDIS_PUT_ERROR, "Redis 存储操作被中断");
        } catch (ExecutionException e) {
            Throwable cause = e.getCause();
            if (cause instanceof RedisConnectionException) {
                log.error("存储到 Redis 并设置过期时间失败，连接问题: key={}, error={}", fullKey, cause.getMessage());
                throw new BusinessException(ErrorCode.REDIS_CONNECTION_ERROR, "Redis 存储失败：连接问题");
            } else if (cause instanceof RedisTimeoutException) {
                log.error("存储到 Redis 并设置过期时间失败，操作超时: key={}, error={}", fullKey, cause.getMessage());
                throw new BusinessException(ErrorCode.REDIS_TIMEOUT_EXCEPTION, "Redis 存储失败：操作超时");
            } else {
                log.error("存储到 Redis 并设置过期时间失败: key={}, error={}", fullKey, cause != null ? cause.getMessage() : e.getMessage());
                throw new BusinessException(ErrorCode.REDIS_PUT_ERROR, "Redis 存储失败");
            }
        } catch (Exception e) {
            log.error("存储到 Redis 并设置过期时间失败: key={}, error={}", fullKey, e.getMessage());
            throw new BusinessException(ErrorCode.REDIS_PUT_ERROR, "Redis 存储失败");
        }
    }

    /**
     * 租户授权码存储键值对到 Redis，并设置过期时间
     * @param key 键
     * @param value 值
     * @param expireTime 过期时间
     * @param timeUnit 时间单位
     * @param <T> 值类型
     */
    public <T> void putAuthCode(String key, T value, long expireTime, TimeUnit timeUnit) {
        if (key == null) {
            log.error("Redis key 为空，无法存储");
            throw new BusinessException(ErrorCode.INVALID_PARAM, "Redis key 不能为空");
        }
        if (expireTime <= 0) {
            log.error("Redis 过期时间必须大于 0: expireTime={}", expireTime);
            throw new BusinessException(ErrorCode.INVALID_PARAM, "Redis 过期时间必须大于 0");
        }

        String fullKey = BaseCode.TENANT_AUTH_INFO_PREFIX.getCode() + key;
        RBucket<T> bucket = redissonClient.getBucket(fullKey);
        try {
            bucket.setAsync(value, expireTime, timeUnit).get();
            log.debug("已存储企业访问令牌到Redis: key={}, expireTime={} {}", fullKey, expireTime, timeUnit);
        } catch (InterruptedException e) {
            log.error("租户授权码存储到 Redis 并设置过期时间时线程被中断: key={}, error={}", fullKey, e.getMessage());
            Thread.currentThread().interrupt();
            throw new BusinessException(ErrorCode.REDIS_PUT_ERROR, "Redis 存储操作被中断");
        } catch (ExecutionException e) {
            Throwable cause = e.getCause();
            if (cause instanceof RedisConnectionException) {
                log.error("租户授权码存储到 Redis 并设置过期时间失败，连接问题: key={}, error={}", fullKey, cause.getMessage());
                throw new BusinessException(ErrorCode.REDIS_CONNECTION_ERROR, "Redis 存储失败：连接问题");
            } else if (cause instanceof RedisTimeoutException) {
                log.error("租户授权码存储到 Redis 并设置过期时间失败，操作超时: key={}, error={}", fullKey, cause.getMessage());
                throw new BusinessException(ErrorCode.REDIS_TIMEOUT_EXCEPTION, "Redis 存储失败：操作超时");
            } else {
                log.error("租户授权码存储到 Redis 并设置过期时间失败: key={}, error={}", fullKey, cause != null ? cause.getMessage() : e.getMessage());
                throw new BusinessException(ErrorCode.REDIS_PUT_ERROR, "Redis 存储失败");
            }
        } catch (Exception e) {
            log.error("租户授权码存储到 Redis 失败: key={}, error={}", fullKey, e.getMessage());
            throw new BusinessException(ErrorCode.REDIS_PUT_ERROR, "Redis 存储失败");
        }
    }

    /**
     * 从 Redis 获取租户授权码
     *
     * @param key 键
     * @param <T> 值类型
     * @return 值
     */
    public <T> Object getAuthCode(String key) {
        if (key == null) {
            log.error("Redis key 为空，无法获取值");
            throw new BusinessException(ErrorCode.INVALID_PARAM, "Redis key 不能为空");
        }
        String fullKey = BaseCode.TENANT_AUTH_INFO_PREFIX.getCode() + key;
        RBucket<T> bucket = redissonClient.getBucket(fullKey);
        T value = bucket.get();
        if (value == null) {
            log.debug("Redis 中未找到对应值: key={}", fullKey);
            return null;
        }
        if (value instanceof TenantAuthInfoEntity entity) {
            long ttlMillis = bucket.remainTimeToLive();
            // 剩余时间小于0等于已经过期了
            if (ttlMillis < 0) {
                // 从数据重新查出来存入Redis 默认存2年
                QueryWrapper<TenantAuthInfoEntity> queryWrapper = new TenantAuthInfoQuery().init(entity).toQueryWrapper();
                TenantAuthInfoEntity countEntity = tenantAuthInfoMapper.selectOne(queryWrapper);
                if (countEntity != null) {
                    String newKey = countEntity.getTenantId() + countEntity.getAgentId() + countEntity.getAgentName() + countEntity.getTenantName();
                    this.putAuthCode(newKey, countEntity, 730, TimeUnit.DAYS);
                    log.info("租户授权码已过期，正在重新生成租户授权码并存入Redis: key={}", newKey);
                    return countEntity;
                } else {
                    log.warn("租户授权码已过期，但数据库中未找到对应实体: key={}", fullKey);
                    throw new  BusinessException(ErrorCode.REDIS_GET_ERROR, "租户授权码已过期，但数据库中未找到对应实体：key=" + fullKey);
                }
            }
        }
        return value;
    }

    public <T> Object getThirdPartyPreAuthCode(String key) {
        if (key == null) {
            log.error("Redis key 为空，无法获取值");
            throw new BusinessException(ErrorCode.INVALID_PARAM, "Redis key 不能为空");
        }
        String fullKey = BaseCode.WECHAT_THIRD_PARTY_PRE_AUTH_CODE.getCode() + key;
        RBucket<T> bucket = redissonClient.getBucket(fullKey);
        T value = bucket.get();
        if (value == null) {
            log.debug("Redis 中未找到对应值: key={}", fullKey);
            return null;
        }
        return value;
    }

    public <T> Object getSuiteAccessToken(String key) {
        if (key == null) {
            log.error("Redis key 为空，无法获取值");
            throw new BusinessException(ErrorCode.INVALID_PARAM, "Redis key 不能为空");
        }
        String fullKey = BaseCode.WECHAT_SUITE_ACCESS_TOKEN.getCode() + key;
        RBucket<T> bucket = redissonClient.getBucket(fullKey);
        T value = bucket.get();
        if (value == null) {
            log.debug("Redis 中未找到对应值: key={}", fullKey);
            return null;
        }
        return value;
    }

    /**
     * 从 Redis 获取值
     * @param key 键
     * @param <T> 值类型
     * @return 值
     */
    public <T> T get(String key) {
        if (key == null) {
            log.error("Redis key 为空，无法获取值");
            throw new BusinessException(ErrorCode.INVALID_PARAM, "Redis key 不能为空");
        }
        String fullKey = BaseCode.SECRET_KEY_PREFIX.getCode() + key;
        RBucket<T> bucket = redissonClient.getBucket(fullKey);
        T value;
        try {
            value = bucket.get();
        } catch (Exception e) {
            log.error("从 Redis 获取值失败: key={}, error={}", fullKey, e.getMessage());
            throw new BusinessException(ErrorCode.REDIS_GET_ERROR, "Redis 获取失败");
        }

        if (value == null) {
            log.debug("Redis 中未找到对应值: key={}", fullKey);
            return null;
        }
        if (value instanceof TenantAuthInfoEntity entity) {
            long ttlMillis = bucket.remainTimeToLive();
            long ttlSeconds = ttlMillis > 0 ? ttlMillis / 1000 : ttlMillis;
            entity.setValidityPeriodTime(ttlSeconds);
            log.debug("更新 entity TTL: key={}, ttl={} seconds", fullKey, ttlSeconds);
        }
        return value;
    }

    /**
     * 从 Redis 获取值
     * @param key 键
     * @param <T> 值类型
     * @return 值
     */
    public <T> T getByWeChatCorpToken(String key) {
        if (key == null) {
            log.error("Redis key 为空，无法获取值");
            throw new BusinessException(ErrorCode.INVALID_PARAM, "Redis key 不能为空");
        }
        String fullKey = BaseCode.WECHAT_PERMANENT_CODE.getCode() + key;
        RBucket<T> bucket = redissonClient.getBucket(fullKey);
        T value;
        try {
            value = bucket.get();
        } catch (Exception e) {
            log.error("从 Redis 获取值失败: key={}, error={}", fullKey, e.getMessage());
            throw new BusinessException(ErrorCode.REDIS_GET_ERROR, "Redis 获取失败");
        }

        if (value == null) {
            log.debug("Redis 中未找到对应值: key={}", fullKey);
            return null;
        }
        return value;
    }

    /**
     * 从 Redis 删除键值对
     * @param key 键
     */
    public void remove(String key) {
        if (key == null) {
            log.error("Redis key 为空，无法删除");
            throw new BusinessException(ErrorCode.INVALID_PARAM, "Redis key 不能为空");
        }

        String fullKey = BaseCode.SECRET_KEY_PREFIX.getCode() + key;
        RBucket<Object> bucket = redissonClient.getBucket(fullKey);
        bucket.delete();
        log.debug("已从 Redis 删除: key={}", fullKey);
    }

    /**
     * 判断键是否存在
     * @param key 键
     * @return 是否存在
     */
    public boolean exists(String key) {
        if (key == null) {
            log.error("Redis key 为空，无法检查是否存在");
            throw new BusinessException(ErrorCode.INVALID_PARAM, "Redis key 不能为空");
        }

        String fullKey = BaseCode.SECRET_KEY_PREFIX.getCode() + key;
        RBucket<Object> bucket = redissonClient.getBucket(fullKey);
        boolean exists = bucket.isExists();
        log.debug("Redis 键是否存在: key={}, exists={}", fullKey, exists);
        return exists;
    }

    /**
     * 批量存储键值对到 Redis
     * @param keyValues 键值对
     */
    public void putAll(Map<String, Object> keyValues) {
        if (keyValues == null || keyValues.isEmpty()) {
            log.warn("批量存储到 Redis 的键值对为空，跳过操作");
            return;
        }

        keyValues.forEach((key, value) -> {
            if (key == null) {
                log.warn("批量存储中发现空的 Redis key，跳过该项");
                return;
            }
            String fullKey = BaseCode.SECRET_KEY_PREFIX.getCode() + key;
            RBucket<Object> bucket = redissonClient.getBucket(fullKey);
            try {
                bucket.setAsync(value).get();
                log.debug("已批量存储到 Redis: key={}", fullKey);
            } catch (InterruptedException e) {
                log.error("批量存储到 Redis 时线程被中断: key={}, error={}", fullKey, e.getMessage());
                Thread.currentThread().interrupt();
                throw new BusinessException(ErrorCode.REDIS_PUT_ERROR, "Redis 批量存储操作被中断");
            } catch (ExecutionException e) {
                Throwable cause = e.getCause();
                if (cause instanceof RedisConnectionException) {
                    log.error("批量存储到 Redis 失败，连接问题: key={}, error={}", fullKey, cause.getMessage());
                    throw new BusinessException(ErrorCode.REDIS_CONNECTION_ERROR, "Redis 批量存储失败：连接问题");
                } else if (cause instanceof RedisTimeoutException) {
                    log.error("批量存储到 Redis 失败，操作超时: key={}, error={}", fullKey, cause.getMessage());
                    throw new BusinessException(ErrorCode.REDIS_TIMEOUT_EXCEPTION, "Redis 批量存储失败：操作超时");
                } else {
                    log.error("批量存储到 Redis 失败: key={}, error={}", fullKey, cause != null ? cause.getMessage() : e.getMessage());
                    throw new BusinessException(ErrorCode.REDIS_PUT_ERROR, "Redis 批量存储失败");
                }
            } catch (Exception e) {
                log.error("批量存储到 Redis 失败: key={}, error={}", fullKey, e.getMessage());
                throw new BusinessException(ErrorCode.REDIS_PUT_ERROR, "Redis 批量存储失败");
            }
        });
    }

    /**
     * 批量获取 Redis 中的值
     * @param keys 键列表
     * @return 键值对
     */
    public Map<String, Object> getAll(List<String> keys) {
        Map<String, Object> result = HashMap.newHashMap(16);
        for (String key : keys) {
            String fullKey = BaseCode.SECRET_KEY_PREFIX.getCode() + key;
            RBucket<Object> bucket = redissonClient.getBucket(fullKey);
            Object value = bucket.get();
            if (value != null) {
                result.put(key, value);
            }
        }
        return result;
    }

    /**
     * 获取分布式锁
     * @param lockKey 锁的键
     * @param leaseTime 锁租期
     * @param unit 时间单位
     */
    public void lock(String lockKey, long leaseTime, TimeUnit unit) {
        if (lockKey == null) {
            log.error("锁键为空，无法获取锁");
            throw new BusinessException(ErrorCode.INVALID_PARAM, "锁键不能为空");
        }
        String fullLockKey = BaseCode.LOCK_KEY_PREFIX.getCode() + lockKey;
        RLock lock = redissonClient.getLock(fullLockKey);
        try {
            lock.lock(leaseTime, unit);
            log.debug("已获取分布式锁: key={}", fullLockKey);
        } catch (Exception e) {
            log.error("获取分布式锁失败: key={}, error={}", fullLockKey, e.getMessage());
            throw new BusinessException(ErrorCode.REDIS_LOCK_ERROR, "获取分布式锁失败");
        }
    }

    /**
     * 尝试获取分布式锁
     * @param lockKey 锁的键
     * @param waitTime 最大等待时间
     * @param leaseTime 锁租期
     * @param unit 时间单位
     * @return 是否获取成功
     */
    public boolean tryLock(String lockKey, long waitTime, long leaseTime, TimeUnit unit) {
        if (lockKey == null) {
            log.error("锁键为空，无法获取锁");
            throw new BusinessException(ErrorCode.INVALID_PARAM, "锁键不能为空");
        }
        String fullLockKey = BaseCode.LOCK_KEY_PREFIX.getCode() + lockKey;
        RLock lock = redissonClient.getLock(fullLockKey);
        try {
            boolean acquired = lock.tryLock(waitTime, leaseTime, unit);
            if (acquired) {
                log.debug("已获取分布式锁: key={}", fullLockKey);
            } else {
                log.warn("获取分布式锁失败: key={}", fullLockKey);
            }
            return acquired;
        } catch (InterruptedException e) {
            log.error("尝试获取分布式锁时被中断: key={}", fullLockKey, e);
            // 重新中断线程
            Thread.currentThread().interrupt();
            throw new BusinessException(ErrorCode.REDIS_LOCK_ERROR, "获取分布式锁失败");
        } catch (Exception e) {
            log.error("尝试获取分布式锁失败: key={}, error={}", fullLockKey, e.getMessage());
            throw new BusinessException(ErrorCode.REDIS_LOCK_ERROR, "获取分布式锁失败");
        }
    }

    /**
     * 释放分布式锁
     * @param lockKey 锁的键
     */
    public void unlock(String lockKey) {
        if (lockKey == null) {
            log.error("锁键为空，无法释放锁");
            throw new BusinessException(ErrorCode.INVALID_PARAM, "锁键不能为空");
        }
        String fullLockKey = BaseCode.LOCK_KEY_PREFIX.getCode() + lockKey;
        RLock lock = redissonClient.getLock(fullLockKey);
        try {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
                log.debug("已释放分布式锁: key={}", fullLockKey);
            } else {
                log.debug("当前线程未持有锁，无需释放: key={}", fullLockKey);
            }
        } catch (Exception e) {
            log.error("释放分布式锁失败: key={}, error={}", fullLockKey, e.getMessage());
        }
    }

    /**
     * 修改 Redis 中已有的键的值
     * @param key 键
     * @param newValue 新值
     * @param <T> 值类型
     */
    public <T> void update(String key, T newValue) {
        if (key == null) {
            log.error("Redis key 为空，无法更新");
            throw new BusinessException(ErrorCode.INVALID_PARAM, "Redis key 不能为空");
        }
        String fullKey = BaseCode.SECRET_KEY_PREFIX.getCode() + key;
        RBucket<T> bucket = redissonClient.getBucket(fullKey);
        // 检查键是否存在，尝试更新一个不存在的键通常不是期望的行为
        try {
            if (!bucket.isExists()) {
                log.warn("尝试更新不存在的 Redis 键: key={}", fullKey);
                throw new BusinessException(ErrorCode.REDIS_KEY_NOT_FOUND, "Redis 中未找到要更新的键: " + key);
            }
            // 使用 setAsync 更新值，这会保留原有的 TTL（如果存在）
            bucket.setAsync(newValue).get();
            log.debug("已更新 Redis 值: key={}", fullKey);
        } catch (InterruptedException e) {
            log.error("更新 Redis 失败，线程被中断: key={}, error={}", fullKey, e.getMessage());
            Thread.currentThread().interrupt();
            throw new BusinessException(ErrorCode.REDIS_UPDATE_ERROR, "Redis 更新操作被中断");
        } catch (ExecutionException e) {
            Throwable cause = e.getCause();
            if (cause instanceof RedisConnectionException) {
                log.error("更新 Redis 失败，连接问题: key={}, error={}", fullKey, cause.getMessage());
                throw new BusinessException(ErrorCode.REDIS_CONNECTION_ERROR, "Redis 更新失败：连接问题");
            } else if (cause instanceof RedisTimeoutException) {
                log.error("更新 Redis 失败，操作超时: key={}, error={}", fullKey, cause.getMessage());
                throw new BusinessException(ErrorCode.REDIS_TIMEOUT_EXCEPTION, "Redis 更新失败：操作超时");
            } else {
                log.error("更新 Redis 失败: key={}, error={}", fullKey, cause != null ? cause.getMessage() : e.getMessage());
                throw new BusinessException(ErrorCode.REDIS_UPDATE_ERROR, "Redis 更新失败");
            }
        } catch (BusinessException e) {
            // 捕获前面抛出的REDIS_KEY_NOT_FOUND
            throw e;
        } catch (Exception e) {
            log.error("更新 Redis 失败: key={}, error={}", fullKey, e.getMessage());
            throw new BusinessException(ErrorCode.REDIS_UPDATE_ERROR, "Redis 更新失败");
        }
    }

    /**
     * 将企业访问令牌存储到Redis
     * @param key 企业访问令牌key
     * @param value 要存储的值
     * @param expireTime 过期时间
     * @param timeUnit 时间单位
     * @param <T> 值的类型
     */
    public <T> void putCorpAccessToken(String key, T value, Integer expireTime, TimeUnit timeUnit) {
        // 参数校验
        if (StringUtils.isBlank(key)) {
            log.error("Redis key 为空，无法存储");
            throw new BusinessException(ErrorCode.INVALID_PARAM, "Redis key 不能为空");
        }
        if (value == null) {
            log.error("Redis value 为空，无法存储");
            throw new BusinessException(ErrorCode.INVALID_PARAM, "Redis value 不能为空");
        }
        if (expireTime == null || expireTime <= 0) { 
            log.error("Redis 过期时间必须大于 0: expireTime={}", expireTime);
            throw new BusinessException(ErrorCode.INVALID_PARAM, "Redis 过期时间必须大于 0");
        }
        if (timeUnit == null) { 
            log.error("Redis 时间单位不能为空");
            throw new BusinessException(ErrorCode.INVALID_PARAM, "Redis 时间单位不能为空");
        }

        String fullKey = BaseCode.WECHAT_CORP_ACCESS_TOKEN.getCode() + key;
        RBucket<T> bucket = redissonClient.getBucket(fullKey);
        try {
            bucket.setAsync(value, expireTime, timeUnit).get();
            log.debug("已存储企业访问令牌到Redis: key={}, expireTime={} {}", fullKey, expireTime, timeUnit);
        } catch (InterruptedException e) {
            log.error("存储企业访问令牌时线程被中断: key={}, error={}", fullKey, e.getMessage());
            Thread.currentThread().interrupt(); 
            throw new BusinessException(ErrorCode.REDIS_PUT_ERROR, "Redis 存储操作被中断");
        } catch (ExecutionException e) {
            Throwable cause = e.getCause(); 
            handleRedisException(fullKey, cause, e);
        } catch (Exception e) {
            log.error("存储企业访问令牌失败: key={}, error={}", fullKey, e.getMessage());
            throw new BusinessException(ErrorCode.REDIS_PUT_ERROR, "Redis 存储失败");
        }
    }

    /**
     * 处理Redis异常
     * @param key Redis键
     * @param cause 异常原因
     * @param e 执行异常
     */
    private void handleRedisException(String key, Throwable cause, Exception e) {
        if (cause instanceof RedisConnectionException) {
            log.error("Redis操作失败，连接问题: key={}, error={}", key, cause.getMessage());
            throw new BusinessException(ErrorCode.REDIS_CONNECTION_ERROR, "Redis操作失败：连接问题");
        } else if (cause instanceof RedisTimeoutException) {
            log.error("Redis操作失败，操作超时: key={}, error={}", key, cause.getMessage());
            throw new BusinessException(ErrorCode.REDIS_TIMEOUT_EXCEPTION, "Redis操作失败：操作超时");
        } else {
            log.error("Redis操作失败: key={}, error={}", key, cause != null ? cause.getMessage() : e.getMessage());
            throw new BusinessException(ErrorCode.REDIS_PUT_ERROR, "Redis操作失败");
        }
    }

    /**
     * 从Redis获取企业访问令牌
     * @param key 企业访问令牌key
     * @param <T> 值的类型
     * @return 企业访问令牌对象，不存在时返回null
     */
    public <T> Object getCorpAccessToken(String key) {
        if (StringUtils.isBlank(key)) {
            log.error("Redis key 为空，无法获取企业访问令牌");
            throw new BusinessException(ErrorCode.INVALID_PARAM, "Redis key 不能为空");
        }

        String fullKey = BaseCode.WECHAT_CORP_ACCESS_TOKEN.getCode() + key;
        RBucket<T> bucket = redissonClient.getBucket(fullKey);

        try {
            T value = bucket.get();
            if (value == null) {
                log.debug("Redis中未找到企业访问令牌: key={}", fullKey);
                return null;
            }
            return value;
        } catch (Exception e) {
            log.error("获取企业访问令牌失败: key={}, error={}", fullKey, e.getMessage());
            throw new BusinessException(ErrorCode.REDIS_GET_ERROR, "获取企业访问令牌失败");
        }
    }

    /**
     * 从 Redis 删除授权方企业token
     * @param key 键
     */
    public void removeCorpAccessToken(String key) {
        if (key == null) {
            log.error("Redis key 为空，无法删除");
            throw new BusinessException(ErrorCode.INVALID_PARAM, "Redis key 不能为空");
        }

        String fullKey = BaseCode.WECHAT_CORP_ACCESS_TOKEN.getCode() + key;
        RBucket<Object> bucket = redissonClient.getBucket(fullKey);
        bucket.delete();
        log.debug("已从 Redis 删除授权方企业Token: key={}", fullKey);
    }

    /**
     * 将服务商存储到Redis
     * @param key 平台Id和服务Id
     * @param value 要存储的值
     * @param expireTime 过期时间 (设置为 -1 表示永久)
     * @param timeUnit 时间单位 (当 expireTime 为 -1 时，此参数可为 null 或任意值，但建议传入 null 或不使用)
     * @param <T> 值的类型
     */
    public <T> void putByReceiveId(String key, T value, Integer expireTime, TimeUnit timeUnit) {
        // 参数校验
        if (StringUtils.isBlank(key)) {
            log.error("Redis key 为空，无法存储");
            throw new BusinessException(ErrorCode.INVALID_PARAM, "Redis key 不能为空");
        }
        if (value == null) {
            log.error("Redis value 为空，无法存储");
            throw new BusinessException(ErrorCode.INVALID_PARAM, "Redis value 不能为空");
        }

        // 修改校验逻辑，允许 expireTime 为 -1
        // 允许 -1，但不允许小于 -1 的值
        if (expireTime != null && expireTime < -1) { 
            log.error("Redis 过期时间不能小于 -1: expireTime={}", expireTime);
            throw new BusinessException(ErrorCode.INVALID_PARAM, "Redis 过期时间不能小于 -1");
        }

        // 如果 expireTime 不为 -1，则 timeUnit 不能为空
        if (expireTime != null && expireTime != -1 && timeUnit == null) {
            log.error("Redis 时间单位不能为空，当 expireTime 不为 -1 时");
            throw new BusinessException(ErrorCode.INVALID_PARAM, "Redis 时间单位不能为空");
        }

        String fullKey = BaseCode.WECHAT_RECEIVE_ID.getCode() + key;
        RBucket<T> bucket = redissonClient.getBucket(fullKey);

        try {
            if (expireTime != null && expireTime == -1) {
                // 如果 expireTime 为 -1，则使用 Redisson 的 set() 方法，表示永久存储
                bucket.setAsync(value).get();
                log.debug("已存储企业访问令牌到Redis (永久): key={}", fullKey);
            } else {
                // 否则，使用带过期时间的方法
                // 这里的 expireTime <= 0 的情况应该在上面的校验中被捕获，
                // 所以走到这里 expireTime 肯定是大于 0 的有效值
                bucket.setAsync(value, expireTime, timeUnit).get();
                log.debug("已存储企业访问令牌到Redis: key={}, expireTime={} {}", fullKey, expireTime, timeUnit);
            }
        } catch (InterruptedException e) {
            log.error("存储企业访问令牌时线程被中断: key={}, error={}", fullKey, e.getMessage());
            Thread.currentThread().interrupt();
            throw new BusinessException(ErrorCode.REDIS_PUT_ERROR, "Redis 存储操作被中断");
        } catch (ExecutionException e) {
            Throwable cause = e.getCause();
            handleRedisException(fullKey, cause, e);
        } catch (Exception e) {
            log.error("存储企业访问令牌失败: key={}, error={}", fullKey, e.getMessage());
            throw new BusinessException(ErrorCode.REDIS_PUT_ERROR, "Redis 存储失败: " + e.getMessage());
        }
    }

    /**
     * 从Redis获取服务商
     * @param key 平台Id和服务Id
     * @param <T> 值的类型
     * @return 企业访问令牌对象，不存在时返回null
     */
    public <T> Object getByReceiveId(String key) {
        if (StringUtils.isBlank(key)) {
            log.error("Redis key 为空，无法获取服务商");
            throw new BusinessException(ErrorCode.INVALID_PARAM, "Redis key 不能为空");
        }

        String fullKey = BaseCode.WECHAT_RECEIVE_ID.getCode() + key;
        RBucket<T> bucket = redissonClient.getBucket(fullKey);

        try {
            T value = bucket.get();
            if (value == null) {
                log.debug("Redis中未找到服务商: key={}", fullKey);
                return null;
            }
            return value;
        } catch (Exception e) {
            log.error("获取服务商失败: key={}, error={}", fullKey, e.getMessage());
            throw new BusinessException(ErrorCode.REDIS_GET_ERROR, "获取服务商失败");
        }
    }

    /**
     * 将服务商存储到Redis
     * @param key 平台Id和服务Id
     * @param value 要存储的值
     * @param expireTime 过期时间 (设置为 -1 表示永久)
     * @param timeUnit 时间单位 (当 expireTime 为 -1 时，此参数可为 null 或任意值，但建议传入 null 或不使用)
     * @param <T> 值的类型
     */
    public <T> void putByUuid(String key, T value, Integer expireTime, TimeUnit timeUnit) {
        // 参数校验
        if (StringUtils.isBlank(key)) {
            log.error("Redis key 为空，无法存储");
            throw new BusinessException(ErrorCode.INVALID_PARAM, "Redis key 不能为空");
        }
        if (value == null) {
            log.error("Redis value 为空，无法存储");
            throw new BusinessException(ErrorCode.INVALID_PARAM, "Redis value 不能为空");
        }

        // 修改校验逻辑，允许 expireTime 为 -1
        // 允许 -1，但不允许小于 -1 的值
        if (expireTime != null && expireTime < -1) {
            log.error("Redis 过期时间不能小于 -1: expireTime={}", expireTime);
            throw new BusinessException(ErrorCode.INVALID_PARAM, "Redis 过期时间不能小于 -1");
        }

        // 如果 expireTime 不为 -1，则 timeUnit 不能为空
        if (expireTime != null && expireTime != -1 && timeUnit == null) {
            log.error("Redis 时间单位不能为空，当 expireTime 不为 -1 时");
            throw new BusinessException(ErrorCode.INVALID_PARAM, "Redis 时间单位不能为空");
        }

        String fullKey = BaseCode.WECHAT_AUTH_URL_UUID.getCode() + key;
        RBucket<T> bucket = redissonClient.getBucket(fullKey);

        try {
            if (expireTime != null && expireTime == -1) {
                // 如果 expireTime 为 -1，则使用 Redisson 的 set() 方法，表示永久存储
                bucket.setAsync(value).get();
                log.debug("已存储企业访问令牌到Redis (永久): key={}", fullKey);
            } else {
                // 否则，使用带过期时间的方法
                // 这里的 expireTime <= 0 的情况应该在上面的校验中被捕获，
                // 所以走到这里 expireTime 肯定是大于 0 的有效值
                bucket.setAsync(value, expireTime, timeUnit).get();
                log.debug("已存储企业访问令牌到Redis: key={}, expireTime={} {}", fullKey, expireTime, timeUnit);
            }
        } catch (InterruptedException e) {
            log.error("存储企业访问令牌时线程被中断: key={}, error={}", fullKey, e.getMessage());
            Thread.currentThread().interrupt();
            throw new BusinessException(ErrorCode.REDIS_PUT_ERROR, "Redis 存储操作被中断");
        } catch (ExecutionException e) {
            Throwable cause = e.getCause();
            handleRedisException(fullKey, cause, e);
        } catch (Exception e) {
            log.error("存储企业访问令牌失败: key={}, error={}", fullKey, e.getMessage());
            throw new BusinessException(ErrorCode.REDIS_PUT_ERROR, "Redis 存储失败: " + e.getMessage());
        }
    }

    /**
     * 从Redis获取服务商
     * @param key 平台Id和服务Id
     * @param <T> 值的类型
     * @return 企业访问令牌对象，不存在时返回null
     */
    public <T> Object getByUuid(String key) {
        if (StringUtils.isBlank(key)) {
            log.error("Redis key 为空，无法获取服务商");
            throw new BusinessException(ErrorCode.INVALID_PARAM, "Redis key 不能为空");
        }

        String fullKey = BaseCode.WECHAT_AUTH_URL_UUID.getCode() + key;
        RBucket<T> bucket = redissonClient.getBucket(fullKey);

        try {
            T value = bucket.get();
            if (value == null) {
                log.debug("Redis中未找到服务商: key={}", fullKey);
                return null;
            }
            return value;
        } catch (Exception e) {
            log.error("获取服务商失败: key={}, error={}", fullKey, e.getMessage());
            throw new BusinessException(ErrorCode.REDIS_GET_ERROR, "获取服务商失败");
        }
    }

    /**
     * 将微信客服信息存储到Redis
     * @param key 微信客服openKfId
     * @param value 要存储的值
     * @param expireTime 过期时间 (设置为 -1 表示永久)
     * @param timeUnit 时间单位 (当 expireTime 为 -1 时，此参数可为 null 或任意值，但建议传入 null 或不使用)
     * @param <T> 值的类型
     */
    public <T> void putByOpenKfId(String key, T value, Integer expireTime, TimeUnit timeUnit) {
        // 参数校验
        if (StringUtils.isBlank(key)) {
            log.error("Redis key 为空，无法存储");
            throw new BusinessException(ErrorCode.INVALID_PARAM, "Redis key 不能为空");
        }
        if (value == null) {
            log.error("Redis value 为空，无法存储");
            throw new BusinessException(ErrorCode.INVALID_PARAM, "Redis value 不能为空");
        }

        // 修改校验逻辑，允许 expireTime 为 -1
        // 允许 -1，但不允许小于 -1 的值
        if (expireTime != null && expireTime < -1) {
            log.error("Redis 过期时间不能小于 -1: expireTime={}", expireTime);
            throw new BusinessException(ErrorCode.INVALID_PARAM, "Redis 过期时间不能小于 -1");
        }

        // 如果 expireTime 不为 -1，则 timeUnit 不能为空
        if (expireTime != null && expireTime != -1 && timeUnit == null) {
            log.error("Redis 时间单位不能为空，当 expireTime 不为 -1 时");
            throw new BusinessException(ErrorCode.INVALID_PARAM, "Redis 时间单位不能为空");
        }

        String fullKey = BaseCode.WECHAT_KF_ACCOUNT_INFO.getCode() + key;
        RBucket<T> bucket = redissonClient.getBucket(fullKey);

        try {
            if (expireTime != null && expireTime == -1) {
                // 如果 expireTime 为 -1，则使用 Redisson 的 set() 方法，表示永久存储
                bucket.setAsync(value).get();
                log.debug("已存储企业访问令牌到Redis (永久): key={}", fullKey);
            } else {
                // 否则，使用带过期时间的方法
                // 这里的 expireTime <= 0 的情况应该在上面的校验中被捕获，
                // 所以走到这里 expireTime 肯定是大于 0 的有效值
                bucket.setAsync(value, expireTime, timeUnit).get();
                log.debug("已存储企业访问令牌到Redis: key={}, expireTime={} {}", fullKey, expireTime, timeUnit);
            }
        } catch (InterruptedException e) {
            log.error("存储企业访问令牌时线程被中断: key={}, error={}", fullKey, e.getMessage());
            Thread.currentThread().interrupt();
            throw new BusinessException(ErrorCode.REDIS_PUT_ERROR, "Redis 存储操作被中断");
        } catch (ExecutionException e) {
            Throwable cause = e.getCause();
            handleRedisException(fullKey, cause, e);
        } catch (Exception e) {
            log.error("存储企业访问令牌失败: key={}, error={}", fullKey, e.getMessage());
            throw new BusinessException(ErrorCode.REDIS_PUT_ERROR, "Redis 存储失败: " + e.getMessage());
        }
    }

    /**
     * 从Redis获取微信客服信息
     * @param key 微信客服openKfId
     * @param <T> 值的类型
     * @return 企业访问令牌对象，不存在时返回null
     */
    public <T> Object getByOpenKfId(String key) {
        if (StringUtils.isBlank(key)) {
            log.error("Redis key 为空，无法获取服务商");
            throw new BusinessException(ErrorCode.INVALID_PARAM, "Redis key 不能为空");
        }

        String fullKey = BaseCode.WECHAT_KF_ACCOUNT_INFO.getCode() + key;
        RBucket<T> bucket = redissonClient.getBucket(fullKey);

        try {
            T value = bucket.get();
            if (value == null) {
                log.debug("Redis中未找到服务商: key={}", fullKey);
                return null;
            }
            return value;
        } catch (Exception e) {
            log.error("获取服务商失败: key={}, error={}", fullKey, e.getMessage());
            throw new BusinessException(ErrorCode.REDIS_GET_ERROR, "获取服务商失败");
        }
    }

    /**
     * 将nextCursor信息存储到Redis
     * @param key externalUserId
     * @param value 要存储的值
     * @param expireTime 过期时间 (设置为 -1 表示永久)
     * @param timeUnit 时间单位 (当 expireTime 为 -1 时，此参数可为 null 或任意值，但建议传入 null 或不使用)
     * @param <T> 值的类型
     */
    public <T> void putNextCursorByOpenKfId(String key, T value, Integer expireTime, TimeUnit timeUnit) {
        // 参数校验
        if (StringUtils.isBlank(key)) {
            log.error("Redis key 为空，无法存储");
            throw new BusinessException(ErrorCode.INVALID_PARAM, "Redis key 不能为空");
        }
        if (value == null) {
            log.error("Redis value 为空，无法存储");
            throw new BusinessException(ErrorCode.INVALID_PARAM, "Redis value 不能为空");
        }

        // 修改校验逻辑，允许 expireTime 为 -1
        // 允许 -1，但不允许小于 -1 的值
        if (expireTime != null && expireTime < -1) {
            log.error("Redis 过期时间不能小于 -1: expireTime={}", expireTime);
            throw new BusinessException(ErrorCode.INVALID_PARAM, "Redis 过期时间不能小于 -1");
        }

        // 如果 expireTime 不为 -1，则 timeUnit 不能为空
        if (expireTime != null && expireTime != -1 && timeUnit == null) {
            log.error("Redis 时间单位不能为空，当 expireTime 不为 -1 时");
            throw new BusinessException(ErrorCode.INVALID_PARAM, "Redis 时间单位不能为空");
        }

        String fullKey = BaseCode.WECHAT_NEXT_CURSOR_INFO.getCode() + key;
        RBucket<T> bucket = redissonClient.getBucket(fullKey);

        try {
            if (expireTime != null && expireTime == -1) {
                // 如果 expireTime 为 -1，则使用 Redisson 的 set() 方法，表示永久存储
                bucket.setAsync(value).get();
                log.debug("已存储企业访问令牌到Redis (永久): key={}", fullKey);
            } else {
                // 否则，使用带过期时间的方法
                // 这里的 expireTime <= 0 的情况应该在上面的校验中被捕获，
                // 所以走到这里 expireTime 肯定是大于 0 的有效值
                bucket.setAsync(value, expireTime, timeUnit).get();
                log.debug("已存储企业访问令牌到Redis: key={}, expireTime={} {}", fullKey, expireTime, timeUnit);
            }
        } catch (InterruptedException e) {
            log.error("存储企业访问令牌时线程被中断: key={}, error={}", fullKey, e.getMessage());
            Thread.currentThread().interrupt();
            throw new BusinessException(ErrorCode.REDIS_PUT_ERROR, "Redis 存储操作被中断");
        } catch (ExecutionException e) {
            Throwable cause = e.getCause();
            handleRedisException(fullKey, cause, e);
        } catch (Exception e) {
            log.error("存储企业访问令牌失败: key={}, error={}", fullKey, e.getMessage());
            throw new BusinessException(ErrorCode.REDIS_PUT_ERROR, "Redis 存储失败: " + e.getMessage());
        }
    }

    /**
     * 从Redis获取nextCursor信息
     * @param key externalUserId
     * @param <T> 值的类型
     * @return 企业访问令牌对象，不存在时返回null
     */
    public <T> Object getNextCursorByOpenKfId(String key) {
        if (StringUtils.isBlank(key)) {
            log.error("Redis key 为空，无法获取服务商");
            throw new BusinessException(ErrorCode.INVALID_PARAM, "Redis key 不能为空");
        }

        String fullKey = BaseCode.WECHAT_NEXT_CURSOR_INFO.getCode() + key;
        RBucket<T> bucket = redissonClient.getBucket(fullKey);

        try {
            T value = bucket.get();
            if (value == null) {
                log.debug("Redis中未找到服务商: key={}", fullKey);
                return null;
            }
            return value;
        } catch (Exception e) {
            log.error("获取服务商失败: key={}, error={}", fullKey, e.getMessage());
            throw new BusinessException(ErrorCode.REDIS_GET_ERROR, "获取服务商失败");
        }
    }
}
