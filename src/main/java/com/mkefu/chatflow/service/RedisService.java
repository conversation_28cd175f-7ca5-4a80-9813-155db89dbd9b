package com.mkefu.chatflow.service;

import com.mkefu.system.entity.TenantAuthInfoEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;

/**
 * <AUTHOR> 杨国锋
 * @since 2025-05-16
 */
@Schema(description = "Redis操作接口")
public interface RedisService {

    /**
     * 根据Key从Redis获取数据
     * @param key 主键
     * @return 响应
     */
    TenantAuthInfoEntity getKey(@NotBlank(message = "请传入key") String key);

    /**
     * 更新Redis数据
     * @param entity 要更新的数据
     * @return 响应
     */
    TenantAuthInfoEntity updateKey(@Valid TenantAuthInfoEntity entity);
}
