package com.mkefu.chatflow.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR> Yang 杨国锋
 * @since 2025-05-29
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "背景图渐变效果实体类", description = "CozeGradientPositionEntity")
public class CozeGradientPositionEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "渐变效果的左侧边界位置，单位为像素（px）。此值表示渐变从画布左侧开始的位置，值越小，渐变起始点越靠近画布左侧。")
    private Double left;

    @Schema(description = "渐变效果的右侧边界位置，单位为像素（px）。此值表示渐变在画布右侧结束的位置，值越大，渐变结束点越靠近画布右侧。")
    private Double right;
}
