package com.mkefu.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mkefu.system.entity.TenantAuthInfoEntity;
import com.mkefu.system.entity.query.TenantAuthInfoQuery;
import com.mkefu.system.entity.vo.TenantAuthInfoVo;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * 租户管理Service
 * <AUTHOR>
 * @since 2025-04-10
 */
public interface TenantAuthInfoService extends IService<TenantAuthInfoEntity> {

    /**
     * 生成接口私钥
     * @param entity 实体类
     * @return 私钥
     */
    TenantAuthInfoEntity generateMethodSecretKey(TenantAuthInfoEntity entity);

    /**
     * 生成租户授权码
     * @param entity 实体类
     * @return 授权码
     */
    TenantAuthInfoEntity generateTenantAuthCode(@Valid TenantAuthInfoEntity entity);

    /**
     * 查询租户对应授权码
     * @param query     租户Id
     * @param timestamp 请求时间
     * @return 前端渲染VO
     */
    TenantAuthInfoVo getAuthCodeByTenantId(TenantAuthInfoQuery query, String timestamp);

    /**
     * 生成接口签名
     * @param object 任意对象
     * @param timestamp 时间戳
     * @param tenantId 租户ID
     * @return 签名
     */
    String generateSignature(@NotNull(message = "请传入对象") Object object,
                             @NotBlank(message = "请传入X-Timestamp") String timestamp,
                             @NotBlank(message = "请传入tenantId") String tenantId);
}
