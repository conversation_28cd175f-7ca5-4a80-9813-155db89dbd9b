package com.mkefu.chatflow.service;

import com.mkefu.chatflow.dto.*;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;

import java.util.Map;

/**
 * <AUTHOR> Yang 杨国锋
 * @since 2025-05-08
 */
@Schema(description = "创建会话接口")
public interface CozeConversationService {

    /**
     * 创建会话
     * @param dto 请求体
     * @param accessToken 访问令牌
     * @return 会话信息
     */
    Map<String, Object> createConversation(@Valid CozeConversationRequestDto dto,
                                           @NotBlank(message = "请传入Authorization") String accessToken);

    /**
     * 获取会话列表
     * @param botId 智能体ID
     * @param accessToken 访问令牌
     * @return 会话列表
     */
    CozeConversationListResponseDto getConversationList(@NotBlank(message = "请传入botId") String botId,
                                                        @NotBlank(message = "请传入Authorization") String accessToken);

    /**
     * 获取会话信息
     * @param conversationId 会话ID
     * @param accessToken 访问令牌
     * @return 会话信息
     */
    Map<String, Object> getConversationInfo(@NotBlank(message = "请传入conversationId") String conversationId,
                                            @NotBlank(message = "请传入Authorization") String accessToken);

    /**
     * 清除会话上下文
     * @param conversationId 会话ID
     * @param accessToken 访问令牌
     * @return 清除结果
     */
    CozeCleanContextResponseDto cleanContext(@NotBlank(message = "请传入conversationId") String conversationId,
                                             @NotBlank(message = "请传入Authorization") String accessToken);

    /**
     * 创建消息
     * @param map 请求体
     * @param accessToken 访问令牌
     * @return 消息信息
     */
    CozeCreateMessageResponseDto createMessage(Map<String, Object> map, @NotBlank(message = "请传入Authorization") String accessToken);

    /**
     * 获取消息列表
     * @param map 请求体
     * @param accessToken 访问令牌
     * @return 消息列表
     */
    CozeGetMessageListResponseDto getMessageList(Map<String, Object> map, @NotBlank(message = "请传入Authorization") String accessToken);

    /**
     * 获取消息详情
     * @param conversationId 会话ID
     * @param messageId 消息ID
     * @param accessToken 访问令牌
     * @return 消息详情
     */
    CozeGetMessageInfoResponseDto getMessageInfo(@NotBlank(message = "conversationId") String conversationId,
                                                 @NotBlank(message = "messageId") String messageId,
                                                 @NotBlank(message = "请传入Authorization") String accessToken);

    /**
     * 修改消息
     * @param map 请求体
     * @param accessToken 访问令牌
     * @return 修改结果
     */
    CozeUpdateMessageInfoResponseDto updateMessageInfo(Map<String, Object> map, @NotBlank(message = "请传入Authorization") String accessToken);

    /**
     * 删除消息
     * @param map 请求体
     * @param accessToken 访问令牌
     * @return 删除结果
     */
    CozeDeleteMessageInfoResponseDto deleteMessageInfo(Map<String, Object> map, @NotBlank(message = "请传入Authorization") String accessToken);
}
