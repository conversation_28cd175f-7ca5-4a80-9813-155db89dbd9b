package com.mkefu.common.constant;

import lombok.Getter;

/**
 * 错误码枚举
 * <AUTHOR>
 * @since 2025-04-10
 */
@Getter
public enum ErrorCode {
    /**
     * 无效参数
     */
    INVALID_PARAM("INVALID_PARAM", 400, "无效参数"),

    /**
     * 签名无效
     */
    SIGNATURE_INVALID("SIGNATURE_INVALID", 401, "签名无效"),

    /**
     * 服务端与客户端签名不匹配
     */
    SIGNATURE_NOT_MATCH("SIGNATURE_INVALID", 401, "服务端与客户端签名不匹配"),

    /**
     * 缺少签名
     */
    SIGNATURE_MISSING("SIGNATURE_MISSING", 401, "缺少签名"),

    /**
     * 服务器内部错误
     */
    SERVER_ERROR("SERVER_ERROR", 500, "服务器内部错误"),

    /**
     * REDIS PUT错误
     */
    REDIS_PUT_ERROR("REDIS_PUT_ERROR", 500, "REDIS PUT失败"),

    /**
     * REDIS 连接失败
     */
    REDIS_CONNECTION_ERROR("REDIS_CONNECTION_ERROR", 500, "REDIS 连接失败"),

    /**
     * REDIS 连接超时
     */
    REDIS_TIMEOUT_EXCEPTION("REDIS_TIMEOUT_EXCEPTION", 500, "REDIS 连接超时"),

    /**
     * REDIS GET错误
     */
    REDIS_GET_ERROR("REDIS_GET_ERROR", 500, "REDIS GET失败"),

    /**
     * REDIS EXISTS错误
     */
    REDIS_EXISTS_ERROR("REDIS_EXISTS_ERROR", 500, "REDIS EXISTS失败"),

    /**
     * REDIS REMOVE错误
     */
    REDIS_REMOVE_ERROR("REDIS_REMOVE_ERROR", 500, "REDIS REMOVE失败"),

    /**
     * REDIS LOCK失败
     */
    REDIS_LOCK_ERROR("REDIS_LOCK_ERROR", 500, "REDIS LOCK失败"),

    /**
     * REDIS 中未找到要操作的Key
     */
    REDIS_KEY_NOT_FOUND("REDIS_KEY_NOT_FOUND", 500, "REDIS 中未找到要操作的Key"),

    /**
     * REDIS UPDATE失败
     */
    REDIS_UPDATE_ERROR("REDIS_UPDATE_ERROR", 500, "REDIS UPDATE失败"),

    /**
     * DIFY服务器内部错误
     */
    DIFY_SERVER_ERROR("DIFY_SERVER_ERROR", 500, "DIFY服务器内部错误"),

    /**
     * 调用DIFY方法返回报错
     */
    DIFY_METHOD_ERROR("DIFY_METHOD_ERROR", 400, "调用DIFY方法返回报错"),

    /**
     * DIFY节点未找到
     */
    DIFY_NODE_NOT_FUND("DIFY_NODE_NOT_FUND", 400, "DIFY节点未找到"),

    /**
     * DIFY节点不可用
     */
    DIFY_NODE_DOWN("DIFY_NODE_DOWN", 500, "DIFY节点不可用"),

    /**
     * DIFY 模型调用额度不足
     */
    DIFY_PROVIDER_QUOTA_EXCEEDED("DIFY_PROVIDER_QUOTA_EXCEEDED", 400, "模型调用额度不足"),

    /**
     * DIFY 当前模型不可用
     */
    DIFY_MODEL_CURRENTLY_NOT_SUPPORT("DIFY_MODEL_CURRENTLY_NOT_SUPPORT", 400, "当前模型不可用"),

    /**
     * DIFY 对话不存在
     */
    DIFY_CONVERSATION_NOT_FOUND("DIFY_CONVERSATION_NOT_FOUND", 400, "Conversation Not Exists."),

    /**
     * DIFY授权令牌不正确
     */
    DIFY_UNAUTHORIZED_ERROR("DIFY_UNAUTHORIZED_ERROR", 401, "DIFY授权令牌不正确"),

    /**
     * 调用DIFY接口返回正常状态
     */
    DIFY_SUCCESS("DIFY_SUCCESS", 0, "调用DIFY接口返回正常状态"),

    /**
     * DIFY 未处理的错误事件，请联系管理员添加
     */
    DIFY_UNKNOWN_ERROR("DIFY_UNKNOWN_ERROR", 500, "未处理的错误事件，请联系管理员添加"),

    /**
     * 请求过于频繁，服务商已限流，请稍后重试
     */
    DIFY_TRAFFIC_LIMITING("",503, "请求过于频繁，服务商已限流，请稍后重试"),

    /**
     * COZE调用报错
     */
    COZE_ERROR("COZE_ERROR", 400, "COZE调用报错"),

    /**
     * COZE智能体正在处理中
     */
    COZE_CHAT_IN_PROGRESS("COZE_CHAT_IN_PROGRESS", 400, "智能体正在处理中"),

    /**
     * COZE调用超出RPM峰值
     */
    COZE_EXCEED_MAX_RPM("COZE_EXCEED_MAX_RPM", 4020, "COZE当前 RPM 已超出购买的额度"),

    /**
     * COZE账号已欠费
     */
    COZE_NO_ENOUGH_CREDIT("COZE_NO_ENOUGH_CREDIT", 4019, "COZE账号已欠费"),

    /**
     * 请检查botId是否正确
     */
    COZE_RESOURCE_NOT_FOUND("COZE_RESOURCE_NOT_FOUND", 4200, "请检查botId是否正确"),

    /**
     * 调用COZE接口返回正常状态
     */
    COZE_SUCCESS("COZE_SUCCESS", 0, "调用COZE接口返回正常状态"),

    /**
     * 调用COZE返回未处理的报错类型
     */
    COZE_FAILED("COZE_FAILED", 400, "调用COZE返回未处理的报错类型"),

    /**
     * COZE资源点已用尽，请充值
     */
    COZE_QUOTA_EXCEEDED("COZE_QUOTA_EXCEEDED", 4028, "COZE当前 资源点已用尽，请充值"),
    
    /**
     * COZE账号有未支付账单，请处理
     */
    COZE_ACCOUNT_HAS_UNPAID_BILLS("COZE_ACCOUNT_HAS_UNPAID_BILLS", 4027, "COZE账号有未支付账单，请处理"),

    /**
     * COZE 请求参数错误
     */
    COZE_INVALID_PARAM("COZE_INVALID_PARAM", 4000, "COZE 请求参数错误"),

    /**
     * COZE 没有权限访问该资源
     */
    COZE_ACCESS_DENIED("COZE_ACCESS_DENIED", 4030, "COZE 没有权限访问该资源"),
    
    /**
     * COZE Token 已过期
     */
    COZE_TOKEN_EXPIRED("COZE_TOKEN_EXPIRED", *********, "COZE Token 已过期"),

    /**
     * COZE Token 无效
     */
    COZE_TOKEN_INVALID("COZE_TOKEN_INVALID", 4101, "COZE Token 无效"),
    
    /**
     * 字段超长
     */
    FIELD_TOO_LONG("FIELD_TOO_LONG", 400, "字段超长！"),

    /**
     * 租户授权错误
     */
    TENANT_AUTH_FAILED("TENANT_AUTH_FAILED", 500, "租户授权错误"),

    /**
     * 获取第三方应用凭证失败
     */
    WECHAT_THIRD_PARTY_CREDENTIALS_FAILED("WECHAT_THIRD_PARTY_CREDENTIALS_FAILED", 500, "获取第三方应用凭证失败"),

    /**
     * 获取预授权码失败
     */
    WECHAT_PRE_AUTH_CODE_FAILED("WECHAT_PRE_AUTH_CODE_FAILED", 500, "获取预授权码失败"),

    /**
     * 获取企业有就授权码失败
     */
    WECHAT_GET_PERMANENT_CODE_FAILED("WECHAT_GET_PERMANENT_CODE_FAILED", 500, "获取企业有就授权码失败"),

    /**
     * 未找到服务商授权信息，请先在微信客服配置回调地址
     */
    WECHAT_THIRD_PARTY_AUTH_INFO_NOT_FOUND("WECHAT_THIRD_PARTY_AUTH_INFO_NOT_FOUND", 500, "未找到服务商授权信息，请先在微信客服配置回调地址"),

    /**
     * 微信客服消息发送限制5条
     */
    WECHAT_MESSAGE_SENDING_NUM_LIMIT("WECHAT_MESSAGE_SENDING_LIMIT", 95001, "当用户主动发送消息给微信客服时，企业最多可发送5条消息给用户；若用户继续发送消息，企业可再次下发消息"),

    /**
     * 微信客服消息发送限制48小时
     */
    WECHAT_MESSAGE_SENDING_TIME_LIMIT("WECHAT_MESSAGE_SENDING_TIME_LIMIT", 95002, "当用户在主动发送消息给微信客服时，可在48小时内调用该接口发送消息给用户"),

    /**
     * 只能撤回2分钟内通过api发送的消息
     */
    WECHAT_RECALL_MESSAGE_TIME_LIMIT("WECHAT_RECALL_MESSAGE_TIME_LIMIT", 95029, "撤回消息的时效已过期，只能撤回2分钟内通过api发送的消息"),

    /**
     * 请检查授权方企业token是否合法
     */
    WECHAT_INVALID_TOKEN("WECHAT_RECALL_MESSAGE_TIME_LIMIT", 40014, "请检查授权方企业token是否合法"),
    

    /**
     * 微信API调用失败
     */   
    WECHAT_API_ERROR("WECHAT_API_ERROR", 500, "微信API调用失败"),

    /**
     * 当前请求IP不在允许的IP列表中
     */
    WECHAT_IP_NOT_ALLOW("WECHAT_IP_NOT_ALLOW", 60020, "当前请求IP不在允许的IP列表中"),

    /**
     * suite_ticket不存在或者已失效
     */
    WECHAT_INVALID_SUITE_TICKET("WECHAT_INVALID_SUITE_TICKET", 40085, "suite_ticket不存在或者已失效"),

    /**
     * 生成微信客服账号失败，请先获取授权方企业Token
     */
    WECHAT_GENERATOR_KF_ACCOUNT_FAILED("WECHAT_GENERATOR_KF_ACCOUNT_FAILED", 500, "生成微信客服账号失败，请先获取授权方企业Token"),

    /**
     * 当前租户未绑定微信客服，请先扫码绑定微信客服
     */
    WECHAT_TENANT_ID_NOT_FOUND("WECHAT_TENANT_ID_NOT_FOUND", 500, "当前租户未绑定微信客服，请先扫码绑定微信客服"),

    /**
     * 上传临时素材失败
     */
    WECHAT_UPLOAD_TEMP_MEDIA_FAILED("WECHAT_UPLOAD_TEMP_MEDIA_FAILED", 500, "上传临时素材失败" ),

    /**
     * 下载图片失败
     */
    DOWNLOAD_IMAGE_FAILED("DOWNLOAD_IMAGE_FAILED", 50, "下载图片失败"),
    
    /**
     * JSON转换错误
     */
    JSON_PROCESSING_ERROR("JSON_PROCESSING_ERROR", 500, "JSON转换错误"),

    /**
     * 请求im-service的methodName不能为空
     */
    IM_SERVICE_METHOD_NAME_NULL("IM_SERVICE_METHOD_NAME_NULL", 400, "请求im-service的methodName不能为空");

    private final String code;
    private final int status;
    private final String defaultMessage;

    /**
     * 构造函数
     * @param code 错误码
     * @param status 状态码
     * @param defaultMessage 默认消息
     */
    ErrorCode(String code, int status, String defaultMessage) {
        this.code = code;
        this.status = status;
        this.defaultMessage = defaultMessage;
    }
}
