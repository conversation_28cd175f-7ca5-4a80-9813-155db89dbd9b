package com.mkefu.wechat.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.mkefu.wechat.entity.WeChatCommonEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.Map;

/**
 * 获取企业永久授权码响应DTO
 *
 * <AUTHOR> Yang 杨国锋
 * @since 2025-06-18
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Schema(name = "获取企业永久授权码响应DTO", description = "WeChatGetPermanentCodeResponseDto")
public class WeChatGetPermanentCodeResponseDto extends WeChatCommonEntity implements Serializable {
    
    @Serial
    private static final long serialVersionUID = 1L;
    
    @Schema(description = "企业微信永久授权码,最长为512字节")
    @JsonProperty("permanent_code")
    private String permanentCode;

    @Schema(description = "授权方企业信息Map")
    @JsonProperty("auth_corp_info")
    private Map<String, Object> authCorpInfoMap;
    
    @Schema(description = "授权管理员的信息，可能不返回")
    @JsonProperty("auth_user_info")
    private Map<String, Object> authUserInfoMap;
    
    @Schema(description = "推广二维码安装相关信息Map，扫推广二维码安装时返回。成员授权时暂不支持。（注：无论企业是否新注册，只要通过扫推广二维码安装，都会返回该字段）")
    @JsonProperty("register_code_info")
    private Map<String, Object> registerCodeInfoMap;
    
    @Schema(description = "安装应用时，扫码或者授权链接中带的state值。详见state说明")
    private String state;
}
