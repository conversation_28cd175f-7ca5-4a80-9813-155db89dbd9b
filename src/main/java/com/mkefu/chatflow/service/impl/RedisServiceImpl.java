package com.mkefu.chatflow.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.mkefu.chatflow.service.RedisService;
import com.mkefu.common.constant.ErrorCode;
import com.mkefu.common.exception.BusinessException;
import com.mkefu.common.util.RedisUtil;
import com.mkefu.common.util.SignatureUtil;
import com.mkefu.system.entity.TenantAuthInfoEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> Yang 杨国锋
 * @since 2025-05-16
 */
@Slf4j
@Service
@Schema(description = "Redis操作实现")
public class RedisServiceImpl implements RedisService {

    private final RedisUtil redisUtil;

    private final SignatureUtil signatureUtil;

    /**
     * 从配置文件读取默认有效期，单位：秒
     */
    @Value("${signature.validity-period-time:86400}")
    private Long defaultValidityPeriodTime;

    public RedisServiceImpl(RedisUtil redisUtil, SignatureUtil signatureUtil) {
        this.redisUtil = redisUtil;
        this.signatureUtil = signatureUtil;
    }

    /**
     * 根据Key从Redis获取数据
     * @param key 主键
     * @return 响应
     */
    @Override
    public TenantAuthInfoEntity getKey(String key) {
        return redisUtil.get(key);
    }

    /**
     * 更新Redis数据
     * @param entity 要更新的数据
     * @return 响应
     */
    @Override
    public TenantAuthInfoEntity updateKey(TenantAuthInfoEntity entity) {
        // 校验 tenantId 和 tenantName 是否为空
        if (StringUtils.isBlank(entity.getTenantId()) || StringUtils.isBlank(entity.getTenantName())) {
            throw new BusinessException(ErrorCode.INVALID_PARAM, "tenantId 或 tenantName 不能为空");
        }
        String lockKey = "tenantSecretKeys:" + entity.getTenantId();
        boolean lockAcquired = false;
        try {
            // 尝试获取分布式锁，等待 5 秒，锁租期 10 秒
            lockAcquired = redisUtil.tryLock(lockKey, 5, 10, TimeUnit.SECONDS);
            if (!lockAcquired) {
                log.error("无法获取分布式锁: tenantId={}", entity.getTenantId());
                throw new BusinessException(ErrorCode.REDIS_LOCK_ERROR, "无法生成私钥，系统繁忙");
            }
            // 检查 Redis 是否已有私钥
            TenantAuthInfoEntity existing = redisUtil.get(lockKey);
            if (existing != null && existing.getAuthCode() != null) {
                redisUtil.update(existing.getTenantId(), entity);
                return existing;
            }
            // 如果要更新的Key不存在 则新增一个Key
            String authCode = signatureUtil.generateRandom32Byte();
            entity.setAuthCode(authCode);
            entity.setValidityPeriodTime(ObjectUtil.isEmpty(entity.getValidityPeriodTime())
                    ? defaultValidityPeriodTime
                    : entity.getValidityPeriodTime());
            redisUtil.put(entity.getTenantId(), entity, entity.getValidityPeriodTime(), TimeUnit.SECONDS);
            return entity;
        } finally {
            // 释放锁
            if (lockAcquired) {
                redisUtil.unlock(lockKey);
            }
        }
    }
}
