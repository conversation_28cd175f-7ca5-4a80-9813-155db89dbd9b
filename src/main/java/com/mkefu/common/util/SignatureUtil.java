package com.mkefu.common.util;

import cn.hutool.crypto.digest.HMac;
import cn.hutool.crypto.digest.HmacAlgorithm;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mkefu.common.constant.BaseCode;
import com.mkefu.common.constant.ErrorCode;
import com.mkefu.common.exception.BusinessException;
import com.mkefu.system.entity.TenantAuthInfoEntity;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.SecureRandom;
import java.util.*;

/**
 * 签名工具类，用于生成和验证签名
 * <AUTHOR>
 * @since 2025-04-10
 */
@Slf4j
@Component
public class SignatureUtil {

    private final RedisUtil redisUtil;
    private final ObjectMapper objectMapper;

    public SignatureUtil(RedisUtil redisUtil) {
        this.redisUtil = redisUtil;
        this.objectMapper = new ObjectMapper();
    }

    /**
     * 生成签名（支持任意业务对象）
     *
     * @param params    请求参数，可以是 Map 或任意业务实体类对象
     * @param timestamp 时间戳
     * @param tenantId  租户 ID
     * @return 签名，格式为 tenantId:signature
     */
    public String generateSignature(Object params, String timestamp, String tenantId) {
        if (StringUtils.isBlank(tenantId)) {
            log.error("租户 ID 为空，无法生成签名");
            throw new BusinessException(ErrorCode.INVALID_PARAM, "租户 ID 不能为空");
        }
        Map<String, Object> paramsMap = convertToMap(params);
        String secretKey = getSecretKeyByTenantId(tenantId);
        String data = buildSignData(paramsMap, timestamp, tenantId);
        HMac hMac = new HMac(HmacAlgorithm.HmacSHA256, secretKey.getBytes(StandardCharsets.UTF_8));
        String signature = hMac.digestHex(data);
        // 格式为 tenantId:signature
        return tenantId + ":" + signature;
    }

    /**
     * 验证签名（从 signature 中提取 tenantId）
     *
     * @param params    请求参数，可以是 Map 或任意业务实体类对象
     * @param timestamp 时间戳
     * @param signature 客户端签名，格式为 tenantId:signature
     * @return 是否验证通过
     */
    public boolean verifySignature(Object params, String timestamp, String signature) {
        if (StringUtils.isBlank(signature) || !signature.contains(BaseCode.COLON.getCode())) {
            log.warn("签名格式错误，缺少 tenantId: signature={}", signature);
            throw new BusinessException(ErrorCode.SIGNATURE_INVALID, "签名格式错误，缺少 tenantId");
        }

        String[] parts = signature.split(":", Integer.parseInt(BaseCode.TWO.getCode()));
        if (parts.length != Integer.parseInt(BaseCode.TWO.getCode())) {
            log.warn("签名格式错误，格式应为 tenantId:signature: signature={}", signature);
            throw new BusinessException(ErrorCode.SIGNATURE_INVALID, "签名格式错误，格式应为 tenantId:signature");
        }

        String tenantId = parts[0];
        String clientSignature = parts[1];

        // 验证时间戳
        long currentTime = System.currentTimeMillis() / 1000;
        long requestTime;
        try {
            requestTime = Long.parseLong(timestamp);
        } catch (NumberFormatException e) {
            log.warn("时间戳格式错误: {}", timestamp);
            return false;
        }

        // 从 Redis 获取有效期时间
        long validityPeriodTime = getValidityPeriodTimeByTenantId(tenantId);
        if (Math.abs(currentTime - requestTime) > validityPeriodTime) {
            log.warn("签名已过期: 当前时间={}, 请求时间={}, 有效期={}秒", currentTime, requestTime, validityPeriodTime);
            // 从 Redis 中删除过期密钥
            String redisKey = BaseCode.TENANT_SECRET_KEYS.getCode() + tenantId;
            redisUtil.remove(redisKey);
            return false;
        }

        // 生成服务端签名
        String serverSignature = generateSignature(params, timestamp, tenantId);
        // 提取服务端签名的实际签名部分（去掉 tenantId 前缀）
        String serverSignatureValue = serverSignature.split(":", 2)[1];
        boolean isValid = serverSignatureValue.equalsIgnoreCase(clientSignature);
        if (!isValid) {
            log.warn("签名不匹配: 客户端签名={}, 服务端签名={}", clientSignature, serverSignatureValue);
        }
        return isValid;
    }

    /**
     * 将任意对象转换为 Map<String, Object>
     */
    private Map<String, Object> convertToMap(Object params) {
        try {
            return switch (params) {
                case null -> new TreeMap<>();
                case Map map -> new TreeMap<String, Object>(map);
                case String str -> strConverToTreeMap(str);
                default -> objectMapper.convertValue(params, TreeMap.class);
            };
        } catch (Exception e) {
            log.error("无法将参数转换为 Map: params={}", params, e);
            throw new BusinessException(ErrorCode.INVALID_PARAM, "参数转换失败: " + e.getMessage());
        }
    }

    /**
     * 字符串转换为 TreeMap<String, Object>
     * @param str 字符串 Authorization=234253463423&testKey=23423432
     * @return TreeMap<String, Object>
     */
    private TreeMap<String, Object> strConverToTreeMap(String str) {
        try {
            TreeMap<String, Object> map = new TreeMap<>();
            if (str == null || str.trim().isEmpty()) {
                return map;
            }
            String[] pairs = str.split("&");
            for (String pair : pairs) {
                int idx = pair.indexOf("=");
                if (idx > 0 && idx < pair.length() - 1) {
                    String key = pair.substring(0, idx);
                    String value = pair.substring(idx + 1);
                    map.put(key, value);
                }
            }
            if (map.isEmpty()) {
                return objectMapper.readValue(str, TreeMap.class);
            }
            return map;
        } catch (Exception e) {
            throw new BusinessException(ErrorCode.INVALID_PARAM, "参数转换失败: " + e.getMessage());
        }
    }

    /**
     * 构建签名数据（支持任意类型参数）
     */
    private String buildSignData(Map<String, Object> params, String timestamp, String tenantId) {
        TreeMap<String, Object> sortedParams = new TreeMap<>(params != null ? params : new TreeMap<>());
        StringBuilder sb = new StringBuilder();
        sb.append("tenantId=").append(URLEncoder.encode(tenantId, StandardCharsets.UTF_8)).append("&");

        for (Map.Entry<String, Object> entry : sortedParams.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();
            String valueStr;
            try {
                valueStr = value == null ? "" : objectMapper.writeValueAsString(value);
            } catch (Exception e) {
                log.warn("序列化参数失败，fallback to toString: key={}, value={}", key, value, e);
                valueStr = value.toString();
            }
            // 对值进行 URL 编码
            String encodedValue = URLEncoder.encode(valueStr, StandardCharsets.UTF_8);
            sb.append(key).append("=").append(encodedValue).append("&");
        }
        sb.append("timestamp=").append(URLEncoder.encode(timestamp, StandardCharsets.UTF_8));
        return sb.toString();
    }

    /**
     * 根据租户 ID 获取密钥
     * @param tenantId 租户 ID
     * @return 密钥
     */
    private String getSecretKeyByTenantId(String tenantId) {
        TenantAuthInfoEntity entity = redisUtil.get(tenantId);
        if (entity == null || StringUtils.isBlank(entity.getAuthCode())) {
            log.error("未找到租户 ID 对应的密钥: tenantId={}", tenantId);
            throw new BusinessException(ErrorCode.SIGNATURE_INVALID, "未找到租户 ID 对应的密钥: " + tenantId);
        }
        return entity.getAuthCode();
    }

    /**
     * 根据租户 ID 获取密钥有效期时间
     * @param tenantId 租户 ID
     * @return 有效期
     */
    private long getValidityPeriodTimeByTenantId(String tenantId) {
        TenantAuthInfoEntity entity = redisUtil.get(tenantId);
        if (entity == null || entity.getValidityPeriodTime() == null) {
            log.error("未找到租户 ID 对应的有效期时间: tenantId={}", tenantId);
            throw new BusinessException(ErrorCode.SIGNATURE_INVALID, "未找到租户 ID 对应的有效期时间: " + tenantId);
        }
        return entity.getValidityPeriodTime();
    }

    /**
     * 生成24位随机数+Base64编码再补足为32位的字符码作为私钥和授权码
     * @return 私钥or授权码
     */
    public String generateRandom32Byte() {
        // 使用 SecureRandom 生成 24 字节随机数据
        SecureRandom random = new SecureRandom();
        byte[] bytes = new byte[24];
        random.nextBytes(bytes);
        // Base64 编码并移除 +, /, = 和空白字符
        String encoded = Base64.getEncoder().encodeToString(bytes)
                .replaceAll("[+/=\\s]+", "");
        // 确保长度为 32 字符，如果不足则补充随机字符
        StringBuilder secretKeyBuilder = new StringBuilder(encoded);
        while (secretKeyBuilder.length() < Integer.parseInt(BaseCode.THIRTY_TWO.getCode())) {
            // 补充随机字符（a-z）
            secretKeyBuilder.append((char) ('a' + random.nextInt(26)));
        }
        return secretKeyBuilder.substring(0, 32);
    }
}
