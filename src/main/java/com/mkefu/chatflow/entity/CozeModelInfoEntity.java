package com.mkefu.chatflow.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR> 杨国锋
 * @since 2025-05-29
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "智能体配置的模型实体类。")
public class CozeModelInfoEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "Top K")
    @JsonProperty("top_k")
    private Integer topK;

    @Schema(description = "Top P")
    @JsonProperty("top_p")
    private Double topP;

    @Schema(description = "模型的唯一标识。")
    @JsonProperty("model_id")
    private String modelId;

    @Schema(description = "最大回复长度。")
    @JsonProperty("max_tokens")
    private Integer maxTokens;

    @Schema(description = "模型名称。")
    @JsonProperty("model_name")
    private String modelName;

    @Schema(description = "生成随机性。")
    private Double temperature;

    @Schema(description = "携带上下文轮数。")
    @JsonProperty("context_round")
    private Integer contextRound;

    @Schema(description = "输出格式。取值：" +
            "text：文本。" +
            "markdown：Markdown格式。" +
            "json：json格式。")
    @JsonProperty("response_format")
    private String responseFormat;

    @Schema(description = "重复主题惩罚。")
    @JsonProperty("presence_penalty")
    private Double presencePenalty;

    @Schema(description = "重复语句惩罚。")
    @JsonProperty("frequency_penalty")
    private Double frequencyPenalty;

}
