CREATE TABLE `interaction_log`
(
    `id`             bigint NOT NULL AUTO_INCREMENT COMMENT '自增ID',
    `method_name`    varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '执行的方法名称',
    `arguments`      text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin COMMENT '方法传入的参数',
    `result`         text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin COMMENT '方法返回的结果',
    `exception`      text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin COMMENT '方法执行失败时的异常信息',
    `execution_time` bigint                                                 DEFAULT NULL COMMENT '方法执行时间（毫秒）',
    `timestamp`      datetime                                               DEFAULT NULL COMMENT '交互时间戳',
    `description`    varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '来自@LogInteraction注解的方法描述',
    `ip_address`     varchar(255) COLLATE utf8mb4_bin                       DEFAULT NULL COMMENT '请求的客户端IP地址',
    `create_time`    timestamp NULL                                         DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_id`      varchar(50) COLLATE utf8mb4_bin                        DEFAULT NULL COMMENT '创建人ID',
    `create_name`    varchar(100) COLLATE utf8mb4_bin                       DEFAULT NULL COMMENT '创建人名字',
    `update_time`    timestamp NULL                                         DEFAULT NULL COMMENT '更新时间',
    `update_id`      varchar(50) COLLATE utf8mb4_bin                        DEFAULT NULL COMMENT '更新人ID',
    `update_name`    varchar(100) COLLATE utf8mb4_bin                       DEFAULT NULL COMMENT '更新人名字',
    `del_flag`       char(1) COLLATE utf8mb4_bin                            DEFAULT '1' COMMENT '删除标志 0-已删除 1-未删除',
    `remark`         text COLLATE utf8mb4_bin COMMENT '备注',
    PRIMARY KEY (`id`),
    KEY `idx_timestamp` (`timestamp`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 42
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin COMMENT ='系统交互日志表';



CREATE TABLE `tenant_auth_info`
(
    `id`                   bigint                                                 NOT NULL AUTO_INCREMENT COMMENT '自增ID',
    `auth_code`            varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '授权码',
    `tenant_id`            varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '租户ID',
    `tenant_name`          varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '租户名称',
    `validity_period_time` bigint                                                 NOT NULL COMMENT '私钥有效期',
    `agent_id`             varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '智能体ID',
    `agent_name`           varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '智能体名称',
    `create_time`          timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_id`            varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  DEFAULT NULL COMMENT '创建人ID',
    `create_name`          varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '创建人名字',
    `update_time`          timestamp NULL DEFAULT NULL COMMENT '更新时间',
    `update_id`            varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  DEFAULT NULL COMMENT '更新人ID',
    `update_name`          varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '更新人名字',
    `del_flag`             char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin      DEFAULT '1' COMMENT '删除标志 0-已删除 1-未删除',
    `remark`               text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin COMMENT '备注',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='租户授权信息表';
