package com.mkefu.chatflow.service.impl;

import cn.hutool.http.HttpUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mkefu.common.entity.KuaidiniaoConfigEntity;
import com.mkefu.common.build.KdniaoRequestBuilder;
import com.mkefu.chatflow.entity.query.LogisticsTraceQuery;
import com.mkefu.chatflow.mapper.LogisticsQueryLogMapper;
import com.mkefu.chatflow.service.LogisticsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * 物流服务实现类
 * <AUTHOR>
 * @since 2025-04-10
 */
@Slf4j
@Service
public class LogisticsServiceImpl extends ServiceImpl<LogisticsQueryLogMapper, LogisticsTraceQuery> implements LogisticsService {

    private final KuaidiniaoConfigEntity kuaidiniaoConfigEntity;

    private final KdniaoRequestBuilder kdniaoRequestBuilder;

    public LogisticsServiceImpl(KuaidiniaoConfigEntity kuaidiniaoConfigEntity, KdniaoRequestBuilder kdniaoRequestBuilder) {
        this.kuaidiniaoConfigEntity = kuaidiniaoConfigEntity;
        this.kdniaoRequestBuilder = kdniaoRequestBuilder;
    }

    /**
     * 根据物流单号查询物流公司编码
     * @param logisticsNo 物流单号
     * @return 物流公司编码
     */
    @Override
    public String queryCode(String logisticsNo) {
        Map<String, Object> dataMap = HashMap.newHashMap(1);
        dataMap.put("LogisticCode", logisticsNo);
        Map<String, Object> params = kdniaoRequestBuilder.buildRequest(dataMap, 2002);
        return HttpUtil.post(kuaidiniaoConfigEntity.getApiUrl(), params);
    }

    /**
     * 根据物流单号查询物流轨迹
     * @param logisticsNo 物流单号
     * @param customerName 收件人手机尾号后4位（查顺丰和跨越时必传）
     * @return 物流轨迹
     */
    @Override
    public String queryTrace(String logisticsNo, String customerName) {
        Map<String, Object> dataMap = HashMap.newHashMap(3);
        dataMap.put("LogisticCode", logisticsNo);
        dataMap.put("Sort", 1);
        dataMap.put("CustomerName", customerName);
        Map<String, Object> params = kdniaoRequestBuilder.buildRequest(dataMap, 8002);
        return HttpUtil.post(kuaidiniaoConfigEntity.getApiUrl(), params);
    }
}
