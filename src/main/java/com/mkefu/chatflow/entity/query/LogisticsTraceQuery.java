package com.mkefu.chatflow.entity.query;

import com.mkefu.common.entity.query.BaseQuery;
import com.mkefu.chatflow.entity.LogisticsTraceEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 物流轨迹查询实体类
 * <AUTHOR>
 * @since 2025-04-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(name = "物流轨迹查询对象", description = "LogisticsTraceQuery")
public class LogisticsTraceQuery extends BaseQuery<LogisticsTraceEntity> {

    @Schema(description = "物流轨迹ID")
    private Long id;

    @Schema(description = "物流单号")
    private String trackingNumber;

    @Schema(description = "物流公司编码")
    private String carrierCode;

    @Schema(description = "查询结果")
    private String queryResult;

    @Schema(description = "查询时间")
    private LocalDateTime queryTime;
}
