package com.mkefu.chatflow.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.mkefu.chatflow.dto.CozeChatRequestDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR> 杨国锋
 * @since 2025-05-29
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "")
public class CozeBotInfoEntity implements Serializable {


    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "智能体 ID")
    @JsonProperty("bot_id")
    private String botId;

    @Schema(description = "智能体名称")
    private String name;

    @Schema(description = "智能体描述")
    private String description;

    @Schema(description = "智能体的头像地址。")
    @JsonProperty("icon_url")
    private String iconUrl;

    @Schema(description = "创建时间，格式为 10 位的 unixTime 时间戳，单位为秒（s）。")
    @JsonProperty("create_time")
    private Long createTime;

    @Schema(description = "更新时间，格式为 10 位的 unixTime 时间戳，单位为秒（s）。")
    @JsonProperty("update_time")
    private Long updateTime;

    @Schema(description = "智能体最新版本的版本号。")
    private String version;

    @Schema(description = "智能体的提示词配置实体类。")
    @JsonProperty("prompt_info")
    private CozePromptInfoEntity promptInfo;

    @Schema(description = "智能体的开场白配置实体类。")
    @JsonProperty("onboarding_info")
    private CozeOnboardingInfoEntity onboardingInfo;

    @Schema(description = "智能体模式，取值：" +
            "0：单 Agent 模式" +
            "1：多 Agent 模式")
    @JsonProperty("bot_mode")
    private Integer botMode;

    @Schema(description = "智能体配置的插件实体类。")
    @JsonProperty("plugin_info_list")
    private List<CozePluginInfoEntity> pluginInfoList;

    @Schema(description = "智能体配置的模型实体类。")
    @JsonProperty("model_info")
    private CozeModelInfoEntity modelInfo;

    @Schema(description = "智能体绑定的知识库实体类。")
    private CozeKnowledgeEntity knowledge;

    @Schema(description = "智能体配置的变量列表实体类。")
    private List<CozeVariablesEntity> variables;

    @Schema(description = "智能体配置的音色实体类。")
    @JsonProperty("voice_info_list")
    private List<CozeVoiceInfoEntity> voiceInfoList;

    @Schema(description = "智能体配置的快捷指令实体类。")
    @JsonProperty("shortcut_commands")
    private List<CozeChatRequestDto.ShortcutCommand> shortcutCommands;

    @Schema(description = "用户问题建议实体类")
    @JsonProperty("suggest_reply_info")
    private CozeSuggestReplyInfoEntity suggestReplyInfo;

    @Schema(description = "智能体配置的工作流实体类。")
    @JsonProperty("workflow_info_list")
    private List<CozeWorkflowInfoEntity> workflowInfoList;

    @Schema(description = "智能体背景的图片。")
    @JsonProperty("background_image_info")
    private CozeBackgroundImageEntity backgroundImageInfo;

    @Schema(description = "默认用户输入类型")
    @JsonProperty("default_user_input_type")
    private String defaultUserInputType;
}
