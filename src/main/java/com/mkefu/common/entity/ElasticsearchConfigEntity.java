package com.mkefu.common.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * Elasticsearch配置实体
 * <AUTHOR>
 * @since 2025-04-10
 */
@Data
@Component
@ConfigurationProperties(prefix = "spring.elasticsearch")
public class ElasticsearchConfigEntity {

    @Schema(description = "Elasticsearch URIs")
    private String uris;

    @Schema(description = "Elasticsearch 用户名")
    private String username;

    @Schema(description = "Elasticsearch 密码")
    private String password;
}
