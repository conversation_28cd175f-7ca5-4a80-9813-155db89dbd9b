package com.mkefu.chatflow.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.mkefu.chatflow.entity.DifyConversationEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR> 杨国锋
 * @since 2025-05-13
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "Dify获取会话列表响应DTO", description = "DifyGetConversationListResponseDto")
public class DifyGetConversationListResponseDto implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "返回条数，若传入超过系统限制，返回系统限制数量")
    private int limit;

    @Schema(description = "是否还有数据没返回")
    @JsonProperty("has_more")
    private boolean hasMore;

    @Schema(description = "会话列表")
    private List<DifyConversationEntity> data;

}
