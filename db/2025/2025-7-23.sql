-- 删除不再使用的表
DROP TABLE wechat_third_party_auth_info;
DROP TABLE wechat_async_message_info;

-- 新增表
CREATE TABLE `fs_mkefu_server_info` (
                                        `id` bigint NOT NULL AUTO_INCREMENT,
                                        `show_name` varchar(64) NOT NULL,
                                        `app_id` varchar(64) NOT NULL,
                                        `app_id_query` varchar(32) NOT NULL,
                                        `access_token` varchar(255) NOT NULL,
                                        `encoding_aes_key` varchar(255) NOT NULL,
                                        `server_url` varchar(511) NOT NULL,
                                        `api_version` tinyint DEFAULT '1',
                                        `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
                                        `update_time` datetime DEFAULT CURRENT_TIMESTAMP,
                                        PRIMARY KEY (`id`) USING BTREE,
                                        UNIQUE KEY `idx_app_id` (`app_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='服务器负载均衡表';

CREATE TABLE `mkefu_env_info` (
                                  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键id',
                                  `plat_form_id` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '平台Id',
                                  `tenant_id` varchar(100) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '租户Id',
                                  `show_name` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '服务器环境',
                                  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                  `create_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '创建人ID',
                                  `create_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '创建人名字',
                                  `update_time` timestamp NULL DEFAULT NULL COMMENT '更新时间',
                                  `update_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '更新人ID',
                                  `update_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '更新人名字',
                                  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '1' COMMENT '删除标志 0-已删除 1-未删除',
                                  `remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin COMMENT '备注',
                                  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='租户负载均衡关联表';

-- 替换表主键
ALTER TABLE wechat_customer_company_info DROP PRIMARY KEY;

-- 新增表字段
ALTER TABLE wechat_customer_company_info
    ADD COLUMN `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键Id';

ALTER TABLE wechat_customer_company_info ADD PRIMARY KEY (id);

ALTER TABLE wechat_kf_account_info
    ADD COLUMN `tenant_id` varchar(100) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '租户Id';

ALTER TABLE wechat_kf_account_info
    ADD COLUMN `head_image_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '头像url';

ALTER TABLE wechat_kf_account_info
    ADD COLUMN `kf_name` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '客服名称';    

