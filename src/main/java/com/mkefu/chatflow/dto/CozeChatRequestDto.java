package com.mkefu.chatflow.dto;

import com.fasterxml.jackson.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> 杨国锋
 * @since 2025-05-06
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "coze发起对话请求实体类", description = "CozeChatRequestDto")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CozeChatRequestDto implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "会话ID")
    @JsonProperty("conversation_id")
    private String conversationId;

    @Schema(description = "要进行会话聊天的智能体 ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty("bot_id")
    @NotBlank(message = "请传入 botId")
    private String botId;

    @Schema(description = "标识当前与智能体的用户，由使用方自行定义、生成与维护", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty("user_id")
    @NotBlank(message = "请传入 userId")
    private String userId;

    @Schema(description = "true：采用流式响应。 “流式响应” 将模型的实时响应提供给客户端，类似打字机效果。你可以实时获取服务端返回的对话、消息事件，并在客户端中同步处理、实时展示，也可以直接在 completed 事件中获取智能体最终的回复。" +
            "false：（默认）采用非流式响应。 “非流式响应” 是指响应中仅包含本次对话的状态等元数据。此时应同时开启 auto_save_history，在本次对话处理结束后再查看模型回复等完整响应内容。")
    private boolean stream;

    @Valid
    @Schema(description = "对话的附加信息")
    @JsonProperty("additional_messages")
    private List<AdditionalMessage> additionalMessages = new ArrayList<>();


    @Schema(description = "智能体中定义的变量。在智能体 prompt 中设置变量 {{key}} 后，可以通过该参数传入变量值，同时支持 Jinja2 语法。" +
            "详细说明可参考变量示例。" +
            "变量名只支持英文字母和下划线。")
    @JsonProperty("custom_variables")
    private Map<String, Object> customVariablesMap;

    @Schema(description = "是否保存本次对话记录。" +
            "true：（默认）会话中保存本次对话记录，包括 additional_messages 中指定的所有消息、本次对话的模型回复结果、模型执行中间结果。" +
            "false：会话中不保存本次对话记录，后续也无法通过任何方式查看本次对话信息、消息详情。在同一个会话中再次发起对话时，本次会话也不会作为上下文传递给模型。" +
            "非流式响应下（stream=false），此参数必须设置为 true，即保存本次对话记录，否则无法查看对话状态和模型回复。")
    @JsonProperty("auto_save_history")
    private boolean autoSaveHistory;

    @Schema(description = "附加信息，通常用于封装一些业务相关的字段。查看对话消息详情时，系统会透传此附加信息。" +
            "自定义键值对，应指定为 Map 对象格式。长度为 16 对键值对，其中键（key）的长度范围为 1～64 个字符，值（value）的长度范围为 1～512 个字符。")
    @JsonProperty("meta_data")
    private Map<String, Object> metaDataMap;

    @Schema(description = "附加参数，通常用于特殊场景下指定一些必要参数供模型判断，例如指定经纬度，并询问智能体此位置的天气。" +
            "自定义键值对格式，其中键（key）仅支持设置为：" +
            "latitude：纬度，此时值（Value）为纬度值，例如 39.9800718。" +
            "longitude：经度，此时值（Value）为经度值，例如 116.309314。")
    @JsonProperty("extra_params")
    private Map<String, Object> extraParamsMap;

    @Schema(description = "快捷指令实体类")
    @JsonProperty("shortcut_command")
    private ShortcutCommand shortcutCommand;

    @Schema(description = "用户输入的快捷指令组件参数信息。" +
            "自定义键值对，其中键（key）为快捷指令组件的名称，值（value）为组件对应的用户输入，为 **object_string object ** 数组序列化之后的 JSON String，详细说明可参考 object_string object。")
    @JsonProperty("parameters")
    private Map<String, Object> parametersMap;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(name = "附加消息实体类", description = "AdditionalMessage")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class AdditionalMessage implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        @Schema(description = "消息的内容，支持纯文本、多模态（文本、图片、文件混合输入）、卡片等多种类型的内容")
        private String content;

        @Schema(description = "消息内容的类型，支持设置为：" +
                "text：文本。" +
                "object_string：多模态内容，即文本和文件的组合、文本和图片的组合。")
        @JsonProperty("content_type")
        private String contentType;

        @Schema(description = "创建消息时的附加消息，获取消息时也会返回此附加消息。" +
                "自定义键值对，应指定为 Map 对象格式。长度为 16 对键值对，其中键（key）的长度范围为 1～64 个字符，值（value）的长度范围为 1～512 个字符")
        @JsonProperty("meta_data")
        private Map<String, Object> metaDataMap;

        @Schema(description = "发送这条消息的实体" +
                "user：代表该条消息内容是用户发送的" +
                "assistant：代表该条消息内容是智能体发送的。")
        private String role;


        @Schema(description = "消息类型。默认为 question。" +
                "question：用户输入内容。" +
                "answer：智能体返回给用户的消息内容，支持增量返回。如果工作流绑定了消息节点，可能会存在多 answer 场景，此时可以用流式返回的结束标志来判断所有 answer 完成。" +
                "function_call：智能体对话过程中调用函数（function call）的中间结果。" +
                "tool_response：调用工具 （function call）后返回的结果。" +
                "如果 autoSaveHistory=true，type 支持设置为 question 或 answer。" +
                "如果 autoSaveHistory=false，type 支持设置为 question、answer、function_call、tool_output/tool_response。" +
                "其中，type=question 只能和 role=user 对应，即仅用户角色可以且只能发起 question 类型的消息。")
        private String type;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(name = "快捷指令实体类", description = "ShortcutCommand")
    public static class ShortcutCommand implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        @Schema(description = "对话要执行的快捷指令 ID，必须是智能体已绑定的快捷指令。", requiredMode = Schema.RequiredMode.REQUIRED)
        @JsonProperty("command_id")
        private String commandId;
    }
}
