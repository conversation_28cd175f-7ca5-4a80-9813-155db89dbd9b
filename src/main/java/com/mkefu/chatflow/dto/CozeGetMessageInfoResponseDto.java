package com.mkefu.chatflow.dto;

import com.mkefu.chatflow.entity.CozeBaseResponseEntity;
import com.mkefu.chatflow.entity.OpenMessageApiEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR> 杨国锋
 * @since 2025-05-09
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "查看消息详情响应实体类", description = "CozeGetMessageInfoResponseDto")
@EqualsAndHashCode(callSuper = true)
public class CozeGetMessageInfoResponseDto extends CozeBaseResponseEntity implements Serializable {

    @Schema(description = "消息的详情")
    private OpenMessageApiEntity data;
}
