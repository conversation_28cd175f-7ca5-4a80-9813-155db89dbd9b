package com.mkefu.common.interceptor;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mkefu.common.constant.BaseCode;
import com.mkefu.common.constant.CustomConstant;
import com.mkefu.common.constant.ErrorCode;
import com.mkefu.common.exception.BusinessException;
import com.mkefu.common.util.SignatureUtil;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import java.nio.charset.StandardCharsets;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;

/**
 * 签名拦截器
 * <AUTHOR>
 * @since 2025-04-10
 */
@Slf4j
@Component
public class SignatureInterceptor implements HandlerInterceptor {

    private final SignatureUtil signatureUtil;

    private final ObjectMapper objectMapper;

    public SignatureInterceptor(SignatureUtil signatureUtil, ObjectMapper objectMapper) {
        this.signatureUtil = signatureUtil;
        this.objectMapper = objectMapper;
    }

    private static final String HEADER_SIGNATURE = "X-Signature";

    private static final String HEADER_TIMESTAMP = "X-Timestamp";

    /**
     * 拦截请求，验证签名
     * @param request 请求
     * @param response 响应
     * @param handler 处理器
     * @return 是否继续处理请求
     */
    @Override
    public boolean preHandle(HttpServletRequest request,
                             @NonNull HttpServletResponse response,
                             @NonNull Object handler) {
        String uri = request.getRequestURI();
        String method = request.getMethod();
        String timestamp = request.getHeader(HEADER_TIMESTAMP);
        String signature = request.getHeader(HEADER_SIGNATURE);

        if (StringUtils.isBlank(timestamp) || StringUtils.isBlank(signature)) {
            log.warn("签名缺失: [{} {}] timestamp={}, signature={}", method, uri, timestamp, signature);
            throw new BusinessException(ErrorCode.SIGNATURE_MISSING);
        }

        Map<String, Object> params = getParams(request, uri);
        log.debug("请求参数 [{} {}]: {}", method, uri, params);

        boolean isValid = signatureUtil.verifySignature(params, timestamp, signature);
        if (!isValid) {
            log.warn("签名验证失败: [{} {}] timestamp={}, params={}", method, uri, timestamp, params);
            throw new BusinessException(ErrorCode.SIGNATURE_NOT_MATCH);
        }

        String tenantId = signature.split(":", 2)[0];
        log.info("签名验证通过: [{} {}] tenantId={}, timestamp={}", method, uri, tenantId, timestamp);
        return true;
    }

    /**
     * 从请求中提取参数（支持 JSON 和查询参数）
     * @param request HTTP 请求
     * @return 参数映射
     */
    private Map<String, Object> getParams(HttpServletRequest request, String uri) {
        Map<String, Object> params = HashMap.newHashMap(16);

        // 1. 获取查询参数（支持多值参数）
        request.getParameterMap().forEach((key, value) -> {
            if (value != null) {
                params.put(key, value.length == 1 ? value[0] : String.join(",", value));
            }
        });

        // 2. JSON 请求体
        String contentType = request.getContentType();
        if (contentType != null && contentType.toLowerCase().contains(BaseCode.JSON.getCode())) {
            String jsonBody = (String) request.getAttribute("cachedJsonBody");
            if (StringUtils.isNotBlank(jsonBody)) {
                try {
                    Map<String, Object> jsonMap = objectMapper.readValue(jsonBody, new TypeReference<>() {});
                    params.putAll(jsonMap);
                } catch (Exception e) {
                    log.warn("JSON 参数解析失败: [{}] {}", request.getRequestURI(), e.getMessage());
                    throw new BusinessException(ErrorCode.INVALID_PARAM, "无效的 JSON 参数格式");
                }
            } else {
                log.debug("JSON 请求体为空: [{}]", request.getRequestURI());
            }
        } else {
            // header参数
            try {
                String header = request.getHeader(CustomConstant.HEADER_X_PLATFORMTYPE);
                if (uri.contains(BaseCode.GET_AGENT_INFO_URL.getCode()) && BaseCode.PLAT_FORM_TYPE_DIFY.getCode().equals(header)) {
                    Enumeration<String> headerNames = request.getHeaderNames();
                    while (headerNames.hasMoreElements()) {
                        String key = headerNames.nextElement();
                        if (key.equals(BaseCode.AUTHORIZATION.getCode())) {
                            String value = request.getHeader(key);
                            params.put("Authorization", value);
                            break;
                        }
                    }
                } else {
                    String headerValue = request.getHeader(CustomConstant.HEADER_X_TENANTENTITY);
                    if (StringUtils.isNotBlank(headerValue)) {
                        byte[] isoBytes = headerValue.getBytes(StandardCharsets.ISO_8859_1);
                        String reDecodedFromIsoToUtf8 = new String(isoBytes, StandardCharsets.UTF_8);
                        Map<String, Object> headerParams = objectMapper.readValue(reDecodedFromIsoToUtf8, new TypeReference<>(){});
                        params.putAll(headerParams); 
                    }
                }

            } catch (Exception e) {
                log.warn("header 参数解析失败: [{}] {}", request.getRequestURI(), e.getMessage());
                throw new BusinessException(ErrorCode.INVALID_PARAM, "无效的 JSON 参数格式");
            }
        }
        return params;
    }
}
