package com.mkefu.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mkefu.common.entity.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 租户管理实体类
 * <AUTHOR>
 * @since 2025-04-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("tenant_auth_info")
@Schema(name = "租户管理实体类", description = "TenantAuthInfoEntity")
public class TenantAuthInfoEntity extends BaseEntity {

    @TableId(type = IdType.AUTO)
    @Schema(description = "自增主键ID", example = "1")
    private Long id;

    @TableField("auth_code")
    @Schema(description = "授权码", example = "your-secret-key")
    private String authCode;

    @TableField("tenant_id")
    @NotBlank(message = "租户ID不能为空")
    @Schema(description = "租户ID", example = "efeggffwerw53453", requiredMode = Schema.RequiredMode.REQUIRED)
    private String tenantId;

    @TableField("tenant_name")
    @NotBlank(message = "租户名字不能为空")
    @Schema(description = "租户名字", example = "XXX有限公司", requiredMode = Schema.RequiredMode.REQUIRED)
    private String tenantName;

    @TableField("validity_period_time")
    @Schema(description = "私钥有效期（秒）", example = "300")
    private Long validityPeriodTime;

    @TableField("agent_id")
    @NotBlank(message = "智能体ID不能为空")
    @Schema(description = "智能体ID", example = "1", requiredMode = Schema.RequiredMode.REQUIRED)
    private String agentId;

    @TableField("agent_name")
    @NotBlank(message = "智能体名称不能为空")
    @Schema(description = "智能体名称", example = "测试模型", requiredMode = Schema.RequiredMode.REQUIRED)
    private String agentName;
    
    @TableField(exist = false)
    @Schema(description = "客服默认头像Url")
    private String headImageUrl;
}
