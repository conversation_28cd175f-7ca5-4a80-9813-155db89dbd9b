package com.mkefu.wechat.service.handler.impl;

import com.mkefu.common.util.MkefuImLoadBalancerUtil;
import com.mkefu.wechat.dto.WeChatMessageToImServiceRequestDto;
import com.mkefu.wechat.service.handler.MessageHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 文本消息处理器
 *
 * <AUTHOR> 杨国锋
 * @since 2025-07-08
 */
@Slf4j
@Component
public class TextMessageHandler implements MessageHandler {

    private final MkefuImLoadBalancerUtil mkefuImLoadBalancerUtil;

    public TextMessageHandler(MkefuImLoadBalancerUtil mkefuImLoadBalancerUtil) {
        this.mkefuImLoadBalancerUtil = mkefuImLoadBalancerUtil;
    }

    @Override
    public void handle(WeChatMessageToImServiceRequestDto dto, String methodName) {
        mkefuImLoadBalancerUtil.balanceByTenantId(dto, methodName);
    }
}
