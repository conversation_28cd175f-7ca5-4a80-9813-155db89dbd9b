package com.mkefu.wechat.service;

import com.mkefu.wechat.entity.WeChatThirdPartyBaseInfoEntity;

/**
 * 微信异步处理服务接口
 * <AUTHOR> Yang 杨国锋
 * @since 2025-06-20
 */
public interface WeChatAsyncService {
    
    /**
     * 异步处理infoType事件类型
     *
     * @param decryptedXml   解密后的XML数据
     * @param suiteId        服务商服务Id
     * @param platFormId     平台Id
     * @param baseInfoEntity 服务商基础信息
     */
    void handleWeChatEventAsync(String decryptedXml, String suiteId, String platFormId, WeChatThirdPartyBaseInfoEntity baseInfoEntity);
}
