package com.mkefu.chatflow.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.Map;

/**
 * <AUTHOR> 杨国锋
 * @since 2025-05-09
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "Coze消息的详情实体类", description = "OpenMessageApiEntity")
public class OpenMessageApiEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "Message ID，即消息的唯一标识。")
    private String id;

    @Schema(description = "此消息所在的会话 ID。")
    @JsonProperty("conversation_id")
    private String conversationId;

    @Schema(description = "编写此消息的智能体 ID。此参数仅在对话产生的消息中返回。")
    @JsonProperty("bot_id")
    private String botId;

    @Schema(description = "Chat ID。此参数仅在对话产生的消息中返回。" +
            "不同的对话中，系统会生成新的chat_id。同一个用户在第一次对话和第二次对话时，chat_id不一样。")
    @JsonProperty("chat_id")
    private String chatId;

    @Schema(description = "创建消息时的附加消息，获取消息时也会返回此附加消息。")
    @JsonProperty("meta_data")
    private Map<String, Object> metaData;

    @Schema(description = "发送这条消息的实体。取值：" +
            "user：代表该条消息内容是用户发送的。" +
            "assistant：代表该条消息内容是 Bot 发送的。")
    private String role;

    @Schema(description = "消息的内容，支持纯文本、多模态（文本、图片、文件混合输入）、卡片等多种类型的内容。")
    private String content;

    @Schema(description = "消息内容的类型，取值包括：" +
            "text：文本。" +
            "object_string：多模态内容，即文本和文件的组合、文本和图片的组合。" +
            "card：卡片。此枚举值仅在接口响应中出现，不支持作为入参。")
    @JsonProperty("content_type")
    private String contentType;

    @Schema(description = "消息的创建时间，格式为 10 位的 Unixtime 时间戳，单位为秒（s）。")
    @JsonProperty("created_at")
    private String createdAt;

    @Schema(description = "消息的更新时间，格式为 10 位的 Unixtime 时间戳，单位为秒（s）。")
    @JsonProperty("updated_at")
    private String updatedAt;

    @Schema(description = "消息类型。" +
            "question：用户输入内容。" +
            "answer：智能体返回给用户的消息内容，支持增量返回。如果工作流绑定了 messge 节点，可能会存在多 answer 场景，此时可以用流式返回的结束标志来判断所有 answer 完成。" +
            "function_call：智能体对话过程中调用函数（function call）的中间结果。" +
            "tool_response：调用工具 （function call）后返回的结果。" +
            "follow_up：如果在 Bot 上配置打开了用户问题建议开关，则会返回推荐问题相关的回复内容。" +
            "verbose：多 answer 场景下，服务端会返回一个 verbose 包，对应的 content 为 JSON 格式，content.msg_type =generate_answer_finish 代表全部 answer 回复完成。")
    private String type;

    @Schema(description = "上下文片段 ID。每次清除上下文都会生成一个新的 section_id。")
    @JsonProperty("section_id")
    private String sectionId;

    @Schema(description = "DeepSeek-R1 模型的思维链（CoT）。模型会将复杂问题逐步分解为多个简单步骤，并按照这些步骤逐一推导出最终答案。" +
            "该参数仅在使用 DeepSeek-R1 模型时才会返回。")
    @JsonProperty("reasoning_content")
    private String reasoningContent;

}
