SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for wechat_async_message_info
-- ----------------------------
CREATE TABLE `wechat_async_message_info` (
                                             `open_kf_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '客服账号ID',
                                             `plat_form_id` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '平台Id',
                                             `plat_form_type` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '渠道类型',
                                             `corp_id` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '授权方企业Id',
                                             `next_cursor` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '下次调用带上该值，则从当前的位置继续往后拉，以实现增量拉取。',
                                             `external_user_id` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '微信用户ID',
                                             `msg_type` varchar(5) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '消息类型',
                                             `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                             `create_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '创建人ID',
                                             `create_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '创建人名字',
                                             `update_time` timestamp NULL DEFAULT NULL COMMENT '更新时间',
                                             `update_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '更新人ID',
                                             `update_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '更新人名字',
                                             `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '1' COMMENT '删除标志 0-已删除 1-未删除',
                                             `remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin COMMENT '备注',
                                             PRIMARY KEY (`open_kf_id`,`plat_form_type`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='微信客服消息同步信息表';

-- ----------------------------
-- Table structure for wechat_customer_company_info
-- ----------------------------
CREATE TABLE `wechat_customer_company_info` (
                                                `tenant_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '代开发企业平台Id',
                                                `plat_form_id` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '平台Id',
                                                `corp_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '被服务的企业微信Id',
                                                `corp_name` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '被服务的企业名称',
                                                `permanent_code` varchar(600) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '被服务的企业永久授权码',
                                                `user_id` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '被服务的企业管理员的userId',
                                                `open_userid` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '被服务的企业管理员的openUserId',
                                                `name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '被服务的企业授权管理员的name',
                                                `avatar` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '被服务的企业授权管理员的头像URL',
                                                `register_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '被服务的企业注册码',
                                                `template_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '被服务的企业推广包ID',
                                                `state` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '安装应用时，扫码或者授权链接中带的state值。详见state说明',
                                                `register_state` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '仅当获取注册码指定该字段时才返回',
                                                `expires_in` int DEFAULT NULL COMMENT '有效期（秒）',
                                                `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                                `create_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '创建人ID',
                                                `create_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '创建人名字',
                                                `update_time` timestamp NULL DEFAULT NULL COMMENT '更新时间',
                                                `update_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '更新人ID',
                                                `update_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '更新人名字',
                                                `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '1' COMMENT '删除标志 0-已删除 1-未删除',
                                                `remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin COMMENT '备注',
                                                PRIMARY KEY (`tenant_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='被服务的企业信息表';

-- ----------------------------
-- Table structure for wechat_kf_account_info
-- ----------------------------
CREATE TABLE `wechat_kf_account_info` (
                                          `open_kf_id` varchar(255) COLLATE utf8mb4_bin NOT NULL COMMENT '客服账号Id',
                                          `plat_form_id` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '平台Id',
                                          `plat_form_type` varchar(255) COLLATE utf8mb4_bin NOT NULL COMMENT '渠道类型',
                                          `kf_account_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '客服对应的聊天URL',
                                          `corp_id` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '授权方企业Id',
                                          `suite_id` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '服务商应用Id',
                                          `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                          `create_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '创建人ID',
                                          `create_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '创建人名字',
                                          `update_time` timestamp NULL DEFAULT NULL COMMENT '更新时间',
                                          `update_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '更新人ID',
                                          `update_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '更新人名字',
                                          `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '1' COMMENT '删除标志 0-已删除 1-未删除',
                                          `remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin COMMENT '备注',
                                          PRIMARY KEY (`open_kf_id`,`plat_form_type`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='微信客服账号信息表';

-- ----------------------------
-- Table structure for wechat_third_party_auth_info
-- ----------------------------
CREATE TABLE `wechat_third_party_auth_info` (
                                                `suite_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '代开发企业应用id',
                                                `plat_form_id` varchar(255) COLLATE utf8mb4_bin NOT NULL COMMENT '平台id',
                                                `receive_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '代开发企业Id',
                                                `suite_ticket` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '代开发企业微信后台推送的ticket',
                                                `suite_secret` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '代开发企业密钥',
                                                `suite_access_token` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '代开发企业应用access_token,最长为512字节',
                                                `suite_access_token_expires_in` int DEFAULT NULL COMMENT '代开发企业应用access_token的有效期时间',
                                                `suite_access_token_create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '代开发企业应用access_token获取到的时间',
                                                `suite_pre_auth_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '代开发企业预授权码',
                                                `suite_pre_auth_code_expires_in` int DEFAULT NULL COMMENT '代开发企业预授权码有效期时间',
                                                `suite_pre_auth_code_create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '代开发企业预授权码获取到的时间',
                                                `suite_auth_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '代开发企业授权链接URL',
                                                `permanent_code` varchar(600) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '被服务企业永久授权码',
                                                `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                                `create_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '创建人ID',
                                                `create_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '创建人名字',
                                                `update_time` timestamp NULL DEFAULT NULL COMMENT '更新时间',
                                                `update_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '更新人ID',
                                                `update_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '更新人名字',
                                                `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '1' COMMENT '删除标志 0-已删除 1-未删除',
                                                `remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin COMMENT '备注',
                                                PRIMARY KEY (`plat_form_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='代开发企业应用授权信息表';

-- ----------------------------
-- Table structure for wechat_third_party_base_info
-- ----------------------------
CREATE TABLE `wechat_third_party_base_info` (
                                                `plat_form_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '平台Id',
                                                `receive_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '企业Id',
                                                `suite_id` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '服务Id',
                                                `suite_secret` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '服务密钥',
                                                `suite_token` varchar(500) COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'token',
                                                `suite_encoding_aes_key` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '解密密钥',
                                                `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                                `create_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '创建人ID',
                                                `create_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '创建人名字',
                                                `update_time` timestamp NULL DEFAULT NULL COMMENT '更新时间',
                                                `update_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '更新人ID',
                                                `update_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '更新人名字',
                                                `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '1' COMMENT '删除标志 0-已删除 1-未删除',
                                                `remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin COMMENT '备注',
                                                PRIMARY KEY (`plat_form_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='代开发企业配置页信息';

SET FOREIGN_KEY_CHECKS = 1;
