package com.mkefu.chatflow.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.sql.Timestamp;
import java.util.List;

/**
 * <AUTHOR> Yang 杨国锋
 * @since 2025-05-13
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "Dify消息实体类", description = "DifyMessageEntity")
public class DifyMessageEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "消息 ID")
    private String id;

    @Schema(description = "会话 ID")
    @JsonProperty("conversation_id")
    private String conversationId;

    @Schema(description = "用户输入参数。")
    private Object inputs;

    @Schema(description = "用户输入的问题")
    private String query;

    @Schema(description = "回复")
    private String answer;

    @Schema(description = "文件")
    @JsonProperty("message_files")
    private List<Object> messageFiles;

    @Schema(description = "反馈信息")
    private String feedback;

    @Schema(description = "引用和归属分段列表")
    @JsonProperty("retriever_resources")
    private List<Object> retrieverResources;

    @Schema(description = "创建时间")
    @JsonProperty("created_at")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Timestamp createdAt;


}
