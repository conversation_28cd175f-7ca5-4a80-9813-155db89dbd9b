package com.mkefu.wechat.service.handler.impl;

import com.mkefu.common.util.MkefuImLoadBalancerUtil;
import com.mkefu.wechat.dto.WeChatMessageToImServiceRequestDto;
import com.mkefu.wechat.service.handler.MessageHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 默认消息处理器
 * 用于处理未知类型的消息
 *
 * <AUTHOR> Yang 杨国锋
 * @since 2025-07-08
 */
@Slf4j
@Component
public class DefaultMessageHandler implements MessageHandler {

    private final MkefuImLoadBalancerUtil mkefuImLoadBalancerUtil;

    public DefaultMessageHandler(MkefuImLoadBalancerUtil mkefuImLoadBalancerUtil) {
        this.mkefuImLoadBalancerUtil = mkefuImLoadBalancerUtil;
    }

    @Override
    public void handle(WeChatMessageToImServiceRequestDto dto, String methodName) {
        mkefuImLoadBalancerUtil.balanceByTenantId(dto, methodName);
    }
}
