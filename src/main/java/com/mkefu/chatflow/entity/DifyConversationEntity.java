package com.mkefu.chatflow.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.sql.Timestamp;

/**
 * <AUTHOR> 杨国锋
 * @since 2025-05-13
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "Dify会话列表实体类", description = "DifyConversationEntity")
public class DifyConversationEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "会话 ID")
    private String id;

    @Schema(description = "会话名称，默认由大语言模型生成。")
    private String name;

    @Schema(description = "用户输入参数。")
    private Object inputs;

    @Schema(description = "会话状态")
    private String status;

    @Schema(description = "开场白")
    private String introduction;

    @Schema(description = "创建时间")
    @JsonProperty("created_at")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Timestamp createdAt;

    @Schema(description = "更新时间")
    @JsonProperty("updated_at")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Timestamp updatedAt;

}
