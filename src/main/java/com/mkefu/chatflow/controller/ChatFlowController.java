package com.mkefu.chatflow.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.mkefu.chatflow.dto.*;
import com.mkefu.chatflow.dto.CommonCancelChatRequestDto;
import com.mkefu.common.annotation.LogInteraction;
import com.mkefu.common.constant.BaseCode;
import com.mkefu.common.constant.CustomConstant;
import com.mkefu.common.constant.ErrorCode;
import com.mkefu.common.exception.BusinessException;
import com.mkefu.common.response.Result;
import com.mkefu.chatflow.service.CozeChatService;
import com.mkefu.chatflow.service.CozeConversationService;
import com.mkefu.chatflow.service.DifyChatService;
import com.mkefu.system.entity.TenantAuthInfoEntity;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 *
 * <AUTHOR> Yang 杨国锋
 * @since 2025-05-06
 */
@Validated
@RestController
@Tag(name = "对话流控制层")
@RequestMapping("/api/chatflow")
public class ChatFlowController {

    private final CozeChatService cozeChatService;

    private final DifyChatService difyChatService;

    private final CozeConversationService cozeConversationService;

    private final ObjectMapper objectMapper;

    public ChatFlowController(CozeChatService cozeChatService, DifyChatService difyChatService, CozeConversationService cozeConversationService, ObjectMapper objectMapper) {
        this.cozeChatService = cozeChatService;
        this.difyChatService = difyChatService;
        this.cozeConversationService = cozeConversationService;
        this.objectMapper = objectMapper;
    }

    @Operation(summary = "发起对话-支持SSE流和阻塞式")
    @PostMapping(value = "/chatMessages", produces = {MediaType.TEXT_EVENT_STREAM_VALUE, MediaType.APPLICATION_JSON_VALUE})
    public Object cozeChatMessages(@RequestBody String body,
                                   @RequestHeader(name = CustomConstant.HEADER_X_PLATFORMTYPE) String platformType,
                                   @RequestHeader(name = CustomConstant.HEADER_X_APITYPE) String apiType,
                                   @RequestHeader(name = CustomConstant.HEADER_AUTHORIZATION) String accessToken) {
        try {
            if (BaseCode.PLAT_FORM_TYPE_COZE.getCode().equalsIgnoreCase(platformType)) {
                CozeChatRequestDto cozeDto = objectMapper.readValue(body, CozeChatRequestDto.class);
                boolean stream = cozeDto.isStream();
                if (stream) {
                    return cozeChatService.chatMessagesStream(cozeDto, accessToken);
                }
                return Result.success(cozeChatService.chatMessages(cozeDto, accessToken));
            } else {
                DifyChatRequestDto difyDto = objectMapper.readValue(body, DifyChatRequestDto.class);
                if (BaseCode.API_TYPE_OFFICIAL.getCode().equalsIgnoreCase(apiType)) {
                    String responseMode = difyDto.getResponseMode();
                    if (BaseCode.STREAMING.getCode().equals(responseMode)) {
                        return difyChatService.chatMessagesStream(difyDto, accessToken);
                    }
                    return Result.success(difyChatService.chatMessages(difyDto, accessToken));
                }
                // Dify 自部署 API
                return null;
            }
        } catch (Exception e) {
            throw new BusinessException(ErrorCode.INVALID_PARAM, "请求参数错误，请检查JSON格式是否正确");
        }
    }
    
    @Operation(summary = "查看对话消息详情-COZE官方API")
    @GetMapping(value = "/getMessages", headers = {"X-ApiType=official", "X-PlatFormType=coze"})
    @LogInteraction(description = "查看对话消息详情-COZE官方API")
    public Result cozeGetMessages(@RequestHeader(name = CustomConstant.HEADER_AUTHORIZATION) String accessToken,
                                  @RequestParam("conversationId") String conversationId,
                                  @RequestParam("chatId") String chatId) {
        return Result.success(cozeChatService.getMessages(accessToken, conversationId, chatId));
    }

    @Operation(summary = "获取会话历史消息-DIFY官方API")
    @PostMapping(value = "/getMessages", headers = {"X-ApiType=official","X-PlatFormType=dify"})
    @LogInteraction(description = "获取会话历史消息-DIFY官方API")
    public Result difyGetMessages(@NotBlank(message = "请传入Authorization") @RequestHeader(name = CustomConstant.HEADER_AUTHORIZATION) String accessToken,
                                  @NotNull(message = "请传入Map结构") @RequestBody Map<String, Object> params) {
        return Result.success(difyChatService.getMessages(accessToken, params));
    }

    @Operation(summary = "查看消息列表-COZE官方API")
    @PostMapping(value = "/getMessageList", headers = {"X-ApiType=official", "X-PlatFormType=coze"})
    @LogInteraction(description = "查看消息列表-COZE官方API")
    public Result getMessageList(@RequestBody Map<String, Object> map,
                                 @NotBlank(message = "请传入Authorization") @RequestHeader(name = CustomConstant.HEADER_AUTHORIZATION) String accessToken) {
        return Result.success(cozeConversationService.getMessageList(map, accessToken));
    }

    @Operation(summary = "获取下一轮建议问题列表-DIFY官方API")
    @PostMapping(value = "/getSuggested", headers = {"X-ApiType=official","X-PlatFormType=dify"})
    @LogInteraction(description = "获取下一轮建议问题列表-DIFY官方API")
    public Result getSuggested(@Valid @RequestBody DifySuggestedRequestDto difySuggestedRequestDto,
                               @NotBlank(message = "请传入请传入Authorization") @RequestHeader(name = CustomConstant.HEADER_AUTHORIZATION) String accessToken) {
        return Result.success(difyChatService.getSuggested(difySuggestedRequestDto, accessToken));
    }

    @Operation(summary = "查看对话详情-COZE官方API")
    @GetMapping(value = "/getConversation", headers = {"X-ApiType=official", "X-PlatFormType=coze"})
    @LogInteraction(description = "查看对话详情-COZE官方API")
    public Result getConversation(@NotBlank(message = "请传入Authorization") @RequestHeader(name = CustomConstant.HEADER_AUTHORIZATION) String accessToken,
                                  @NotBlank(message = "请传入conversationId") @RequestParam("conversationId") String conversationId,
                                  @NotBlank(message = "请传入chatId") @RequestParam("chatId") String chatId) {
        return Result.success(cozeChatService.getConversation(accessToken, conversationId, chatId));
    }

    @Operation(summary = "获取会话列表-DIFY官方API")
    @PostMapping(value = "/getConversationList", headers = {"X-ApiType=official","X-PlatFormType=dify"})
    @LogInteraction(description = "获取会话列表-DIFY官方API")
    public Result difyGetConversationList(@NotBlank(message = "请传入Authorization") @RequestHeader(name = CustomConstant.HEADER_AUTHORIZATION) String accessToken,
                                          @NotNull(message = "请传入Map结构") @RequestBody Map<String, Object> params) {
        return Result.success(difyChatService.getConversationList(accessToken, params));
    }

    @Operation(summary = "查看会话列表-COZE官方API")
    @GetMapping(value = "/getConversationList", headers = {"X-ApiType=official", "X-PlatFormType=coze"})
    @LogInteraction(description = "查看会话列表-COZE官方API")
    public Result cozeGetConversationList(@NotBlank(message = "请传入botId") @RequestParam("botId") String botId,
                                          @NotBlank(message = "请传入Authorization") @RequestHeader(name = CustomConstant.HEADER_AUTHORIZATION) String accessToken) {
        return Result.success(cozeConversationService.getConversationList(botId, accessToken));
    }

    @Operation(summary = "取消进行中的对话-仅流式-官方API")
    @PostMapping(value = "/cancelChat")
    public Result cozeCancelChat(@NotBlank(message = "请传入Authorization") @RequestHeader(name = CustomConstant.HEADER_AUTHORIZATION) String accessToken,
                                 @RequestHeader(name = CustomConstant.HEADER_X_PLATFORMTYPE) String platformType,
                                 @RequestHeader(name = CustomConstant.HEADER_X_APITYPE) String apiType,
                                 @Valid @RequestBody CommonCancelChatRequestDto dto) {
        if (BaseCode.API_TYPE_OFFICIAL.getCode().equalsIgnoreCase(apiType)) {
            if (BaseCode.PLAT_FORM_TYPE_COZE.getCode().equalsIgnoreCase(platformType)) {
                return Result.success(cozeChatService.cancelChat(dto, accessToken));
            }
            return Result.success(difyChatService.cancelChat(dto, accessToken));
        } else {
            // Dify 自部署 API
            return null;
        }
    }

    @Operation(summary = "创建会话-COZE官方API")
    @PostMapping(value = "/createConversation", headers = {"X-ApiType=official", "X-PlatFormType=coze"})
    @LogInteraction(description = "创建会话-COZE官方API")
    public Result cozeCreateConversation(@Valid @RequestBody CozeConversationRequestDto dto,
                                         @NotBlank(message = "请传入Authorization") @RequestHeader(name = CustomConstant.HEADER_AUTHORIZATION) String accessToken) {
        return Result.success(cozeConversationService.createConversation(dto, accessToken));
    }

    @Operation(summary = "查看会话信息-COZE官方API")
    @GetMapping(value = "/getConversationInfo", headers = {"X-ApiType=official", "X-PlatFormType=coze"})
    @LogInteraction(description = "查看会话信息-COZE官方API")
    public Result cozeGetConversationInfo(@NotBlank(message = "请传入conversationId") @RequestParam("conversationId") String conversationId,
                                          @NotBlank(message = "请传入Authorization") @RequestHeader(name = CustomConstant.HEADER_AUTHORIZATION) String accessToken) {
        return Result.success(cozeConversationService.getConversationInfo(conversationId, accessToken));
    }

    @Operation(summary = "清除会话上下文-COZE官方API")
    @GetMapping(value = "/cleanContext", headers = {"X-ApiType=official", "X-PlatFormType=coze"})
    @LogInteraction(description = "清除会话上下文-COZE官方API")
    public Result cozeCleanContext(@NotBlank(message = "请传入conversationId") @RequestParam String conversationId,
                                   @NotBlank(message = "请传入Authorization") @RequestHeader(name = CustomConstant.HEADER_AUTHORIZATION) String accessToken) {
        return Result.success(cozeConversationService.cleanContext(conversationId, accessToken));
    }

    @Operation(summary = "创建消息-COZE官方API")
    @PostMapping(value = "/createMessage", headers = {"X-ApiType=official", "X-PlatFormType=coze"})
    @LogInteraction(description = "创建消息-COZE官方API")
    public Result cozeCreateMessage(@RequestBody Map<String, Object> map,
                                @NotBlank(message = "请传入Authorization") @RequestHeader(name = CustomConstant.HEADER_AUTHORIZATION) String accessToken) {
        return Result.success(cozeConversationService.createMessage(map, accessToken));
    }

    @Operation(summary = "查看消息详情-COZE官方API")
    @GetMapping(value = "/getMessageInfo", headers = {"X-ApiType=official", "X-PlatFormType=coze"})
    @LogInteraction(description = "查看消息详情-COZE官方API")
    public Result cozeGetMessageInfo(@NotBlank(message = "请传入conversationId") @RequestParam("conversationId") String conversationId,
                                 @NotBlank(message = "请传入messageId") @RequestParam("messageId") String messageId,
                                 @NotBlank(message = "请传入Authorization") @RequestHeader(name = CustomConstant.HEADER_AUTHORIZATION) String accessToken) {
        return Result.success(cozeConversationService.getMessageInfo(conversationId, messageId, accessToken));
    }

    @Operation(summary = "修改消息-COZE官方API")
    @PostMapping(value = "/updateMessageInfo", headers = {"X-ApiType=official", "X-PlatFormType=coze"})
    @LogInteraction(description = "修改消息-COZE官方API")
    public Result cozeUpdateMessageInfo(@RequestBody Map<String, Object> map,
                                    @NotBlank(message = "请传入Authorization") @RequestHeader(name = CustomConstant.HEADER_AUTHORIZATION) String accessToken) {
        return Result.success(cozeConversationService.updateMessageInfo(map, accessToken));
    }

    @Operation(summary = "删除消息-COZE官方API")
    @PostMapping(value = "/deleteMessageInfo", headers = {"X-ApiType=official", "X-PlatFormType=coze"})
    @LogInteraction(description = "删除消息-COZE官方API")
    public Result cozeDeleteMessageInfo(@RequestBody Map<String, Object> map,
                                    @NotBlank(message = "请传入Authorization") @RequestHeader(name = CustomConstant.HEADER_AUTHORIZATION) String accessToken) {
        return Result.success(cozeConversationService.deleteMessageInfo(map, accessToken));
    }

    @Operation(summary = "删除会话-DIFY官方API")
    @PostMapping(value = "/deleteConversationInfo", headers = {"X-ApiType=official", "X-PlatFormType=dify"})
    @LogInteraction(description = "删除会话-DIFY官方API")
    public Result difyDeleteConversationInfo(@RequestBody Map<String, Object> map,
                                            @NotBlank(message = "请传入Authorization") @RequestHeader(name = CustomConstant.HEADER_AUTHORIZATION) String accessToken) {
        return Result.success(difyChatService.deleteConversationInfo(map, accessToken));
    }


    @GetMapping( "/getAgentInfo")
    @Operation(summary = "获取智能体信息")
    public Result cozeGetAgentInfo(@RequestHeader(name = CustomConstant.HEADER_AUTHORIZATION) String accessToken,
                                   @RequestHeader(name = CustomConstant.HEADER_X_TENANTENTITY, required = false) String jsonTenantAuthInfoEntity,
                                   @RequestHeader(name = CustomConstant.HEADER_X_APITYPE) String apiType,
                                   @RequestHeader(name = CustomConstant.HEADER_X_PLATFORMTYPE) String platformType) {
        if (BaseCode.API_TYPE_OFFICIAL.getCode().equalsIgnoreCase(apiType)) {
            if (BaseCode.PLAT_FORM_TYPE_COZE.getCode().equalsIgnoreCase(platformType)) {
                TenantAuthInfoEntity tenantAuthInfoEntity;
                try {
                    tenantAuthInfoEntity = objectMapper.readValue(jsonTenantAuthInfoEntity, TenantAuthInfoEntity.class);
                    return Result.success(cozeChatService.getAgentInfo(tenantAuthInfoEntity, accessToken));
                } catch (Exception e) {
                    throw new BusinessException(ErrorCode.COZE_FAILED, e.getMessage());
                }
            } else {
                return Result.success(difyChatService.getAgentInfo(accessToken));
            }
        } else {
            // Dify 自部署 API
            return null;
        }
    }
}
