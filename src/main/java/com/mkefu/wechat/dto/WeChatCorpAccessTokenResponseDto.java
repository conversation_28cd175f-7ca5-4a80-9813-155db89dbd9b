package com.mkefu.wechat.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.mkefu.wechat.entity.WeChatCommonEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * 类描述
 *
 * <AUTHOR> 杨国锋
 * @since 2025-06-25
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Schema(name = "", description = "")
public class WeChatCorpAccessTokenResponseDto extends WeChatCommonEntity implements Serializable {
    
    @Serial
    private static final long serialVersionUID = 1L;
    
    @Schema(description = "授权方企业令牌")
    @JsonProperty("access_token")
    private String corpAccessToken;
}
