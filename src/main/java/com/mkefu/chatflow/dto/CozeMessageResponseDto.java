package com.mkefu.chatflow.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.mkefu.chatflow.entity.CozeBaseResponseEntity;
import com.mkefu.chatflow.entity.MessageObjectEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR> Yang 杨国锋
 * @since 2025-05-06
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "coze查看对话消息详情响应DTO", description = "CozeMessageResponseDto")
@EqualsAndHashCode(callSuper = true)
public class CozeMessageResponseDto extends CozeBaseResponseEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;
    
    @Schema(description = "当前问题是否在知识库匹配到，true：匹配到了  false：未匹配到")
    @JsonProperty("is_resolved")
    private boolean isResolved = true;
    
    private List<MessageObjectEntity> data;
}
