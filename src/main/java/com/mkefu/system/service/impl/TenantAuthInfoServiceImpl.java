package com.mkefu.system.service.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mkefu.common.constant.BaseCode;
import com.mkefu.common.constant.ErrorCode;
import com.mkefu.common.exception.BusinessException;
import com.mkefu.common.util.RedisUtil;
import com.mkefu.common.util.SignatureUtil;
import com.mkefu.system.entity.TenantAuthInfoEntity;
import com.mkefu.system.entity.query.TenantAuthInfoQuery;
import com.mkefu.system.entity.vo.TenantAuthInfoVo;
import com.mkefu.system.mapper.TenantAuthInfoMapper;
import com.mkefu.system.service.TenantAuthInfoService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

/**
 * 租户管理Impl
 * <AUTHOR>
 * @since 2025-04-10
 */
@Slf4j
@Service
public class TenantAuthInfoServiceImpl extends ServiceImpl<TenantAuthInfoMapper, TenantAuthInfoEntity> implements TenantAuthInfoService {

    /**
     * 从配置文件读取默认有效期，单位：秒
     */
    @Value("${signature.validity-period-time:86400}")
    private Long defaultValidityPeriodTime;

    @Value("${authCode.validity-period-time:4102185600}")
    private Long authCodeValidityPeriodTime;

    private final RedisUtil redisUtil;

    private final TenantAuthInfoMapper tenantAuthInfoMapper;

    private final SignatureUtil signatureUtil;

    public TenantAuthInfoServiceImpl(RedisUtil redisUtil, TenantAuthInfoMapper tenantAuthInfoMapper, SignatureUtil signatureUtil) {
        this.redisUtil = redisUtil;
        this.tenantAuthInfoMapper = tenantAuthInfoMapper;
        this.signatureUtil = signatureUtil;
    }


    /**
     * 生成接口私钥
     * @param entity 实体类
     * @return 私钥
     */
    @Override
    public TenantAuthInfoEntity generateMethodSecretKey(TenantAuthInfoEntity entity) {
        // 校验 tenantId 和 tenantName 是否为空
        if (StringUtils.isBlank(entity.getTenantId()) || StringUtils.isBlank(entity.getTenantName())) {
            throw new BusinessException(ErrorCode.INVALID_PARAM, "tenantId 或 tenantName 不能为空");
        }

        String lockKey = entity.getTenantId();
        boolean lockAcquired = false;
        try {
            // 尝试获取分布式锁，等待 5 秒，锁租期 10 秒
            lockAcquired = redisUtil.tryLock(lockKey, 5, 10, TimeUnit.SECONDS);
            if (!lockAcquired) {
                log.error("无法获取分布式锁: tenantId={}", entity.getTenantId());
                throw new BusinessException(ErrorCode.REDIS_LOCK_ERROR, "无法生成私钥，系统繁忙");
            }

            // 检查 Redis 是否已有私钥
            TenantAuthInfoEntity existing = redisUtil.get(lockKey);
            if (existing != null && existing.getAuthCode() != null) {
                log.info("复用现有私钥: tenantId={}", entity.getTenantId());
                return existing;
            }

            // 生成新私钥
            String authCode = signatureUtil.generateRandom32Byte();
            entity.setAuthCode(authCode);
            entity.setValidityPeriodTime(ObjectUtil.isEmpty(entity.getValidityPeriodTime())
                    ? defaultValidityPeriodTime
                    : entity.getValidityPeriodTime());

            // 存储到 Redis
            redisUtil.put(lockKey, entity, entity.getValidityPeriodTime(), TimeUnit.SECONDS);
            log.info("已生成并保存私钥到 Redis: tenantId={}", entity.getTenantId());
            return entity;
        } finally {
            // 释放锁
            if (lockAcquired) {
                redisUtil.unlock(lockKey);
            }
        }
    }

    /**
     * 生成租户授权码
     * @param entity 实体类
     * @return 布尔
     */
    @Override
    public TenantAuthInfoEntity generateTenantAuthCode(TenantAuthInfoEntity entity) {
        String authCode = signatureUtil.generateRandom32Byte();
        // 设置实体属性
        entity.setAuthCode(authCode);
        // 2099年12月30过期
        entity.setValidityPeriodTime(authCodeValidityPeriodTime);
        tenantAuthInfoMapper.insert(entity);
        String key = entity.getTenantId() + entity.getAgentId() + entity.getAgentName() + entity.getTenantName();
        redisUtil.putAuthCode(key, entity, 730, TimeUnit.DAYS);
        return entity;
    }

    /**
     * 查询租户对应授权码
     * @param query     租户Id
     * @param timestamp 请求时间
     * @return 前端渲染VO
     */
    @Override
    public TenantAuthInfoVo getAuthCodeByTenantId(TenantAuthInfoQuery query, String timestamp) {
        TenantAuthInfoEntity tenantAuthInfoEntity = tenantAuthInfoMapper.selectOne(query.toQueryWrapper());
        long validityPeriodTime = tenantAuthInfoEntity.getValidityPeriodTime();
        long currentTime = System.currentTimeMillis() / 1000;
        long requestTime = Long.parseLong(timestamp);
        if (Math.abs(currentTime - requestTime) > validityPeriodTime) {
            log.warn("授权码已过期: 当前时间={}, 请求时间={}, 有效期={}秒", currentTime, requestTime, validityPeriodTime);
            // 修改DelFlag为0
            DateTime date = DateUtil.date(System.currentTimeMillis());
            tenantAuthInfoMapper.update(new UpdateWrapper<TenantAuthInfoEntity>()
                    .eq("id", tenantAuthInfoEntity.getId())
                    .set("updated_time", date)
                    .set("del_flag", BaseCode.ZERO.getCode()));
            tenantAuthInfoEntity.setUpdateTime(date);
            tenantAuthInfoEntity.setDelFlag(BaseCode.ZERO.getCode());
        }
        TenantAuthInfoVo tenantAuthInfoVo = new TenantAuthInfoVo();
        tenantAuthInfoVo.setId(tenantAuthInfoEntity.getId());
        tenantAuthInfoVo.setTenantId(tenantAuthInfoEntity.getTenantId());
        tenantAuthInfoVo.setTenantName(tenantAuthInfoEntity.getTenantName());
        tenantAuthInfoVo.setAuthCode(tenantAuthInfoEntity.getAuthCode());
        tenantAuthInfoVo.setDelFlag(tenantAuthInfoEntity.getDelFlag());
        return tenantAuthInfoVo;
    }

    /**
     * 生成接口签名
     * @param object 任意对象
     * @param timestamp 时间戳
     * @param tenantId 租户ID
     * @return 签名
     */
    @Override
    public String generateSignature(Object object, String timestamp, String tenantId) {
        return signatureUtil.generateSignature(object, timestamp, tenantId);
    }

}
