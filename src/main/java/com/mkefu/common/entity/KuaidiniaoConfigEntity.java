package com.mkefu.common.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 快递鸟配置实体类
 * <AUTHOR>
 * @since 2025-04-10
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "api.logistics.kuaidiniao")
public class KuaidiniaoConfigEntity {

    @JsonProperty("api-key")
    @Schema(description = "应用密钥")
    private String apiKey;

    @JsonProperty("api-url")
    @Schema(description = "API路径")
    private String apiUrl;

    @JsonProperty("business-id")
    @Schema(description = "API路径")
    private String businessId;


}
