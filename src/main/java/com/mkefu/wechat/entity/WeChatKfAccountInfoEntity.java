package com.mkefu.wechat.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mkefu.common.entity.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * 微信客服账号信息实体类
 *
 * <AUTHOR> Yang 杨国锋
 * @since 2025-06-26
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Schema(name = "微信客服账号信息实体类", description = "WeChatKfAccountInfoEntity")
@TableName("wechat_kf_account_info")
public class WeChatKfAccountInfoEntity extends BaseEntity implements Serializable {
    
    @Serial
    private static final long serialVersionUID = 1L;
    
    @Schema(description = "客服账号Id")
    @TableId(value = "open_kf_id", type = IdType.NONE)
    private String openKfId;

    @Schema(description = "客服账号名称")
    @TableField("kf_name")
    private String kfName;

    @Schema(description = "头像Url")
    @TableField("head_image_url")
    private String headImageUrl;
    
    @Schema(description = "租户Id")
    @TableField("tenant_id")
    private String tenantId;

    @Schema(description = "平台Id")
    @TableField("plat_form_id")
    private String platFormId;

    @Schema(description = "渠道类型")
    @TableField("plat_form_type")
    private String platFormType;
    
    @Schema(description = "客服对应的聊天URL")
    @TableField("kf_account_url")
    private String kfAccountUrl;
    
    @Schema(description = "授权方企业Id")
    @TableField("corp_id")
    private String corpId;

    @Schema(description = "服务商应用Id")
    @TableField("suite_id")
    private String suiteId;
    
}
