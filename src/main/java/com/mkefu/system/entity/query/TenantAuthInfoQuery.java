package com.mkefu.system.entity.query;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.mkefu.common.entity.query.BaseQuery;
import com.mkefu.system.entity.TenantAuthInfoEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;

/**
 * 租户管理Query类
 * <AUTHOR>
 * @since 2025-04-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(name = "租户管理Query类", description = "TenantAuthInfoQuery")
public class TenantAuthInfoQuery extends BaseQuery<TenantAuthInfoEntity> {

    @Schema(description = "租户ID")
    private String tenantId;

    @Schema(description = "租户名字")
    private String tenantName;

    @Schema(description = "智能体ID", example = "1", requiredMode = Schema.RequiredMode.REQUIRED)
    private String agentId;

    @Schema(description = "智能体名称", example = "测试模型", requiredMode = Schema.RequiredMode.REQUIRED)
    private String agentName;

    @Schema(description = "授权码", example = "your-secret-key")
    private String authCode;


    /**
     * 构建查询条件
     * @return QueryWrapper
     */
    @Override
    public QueryWrapper<TenantAuthInfoEntity> toQueryWrapper() {
        QueryWrapper<TenantAuthInfoEntity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(tenantId), "tenant_id", tenantId);
        wrapper.eq("del_flag", "1");
        wrapper.eq(StringUtils.isNotBlank(agentId), "agent_id", agentId);
        wrapper.eq(StringUtils.isNotBlank(agentName), "agent_name", agentName);
        wrapper.eq(StringUtils.isNotBlank(authCode), "auth_code", authCode);
        wrapper.like(StringUtils.isNotBlank(tenantName), "tenant_name", tenantName);
        wrapper.orderByDesc("create_time");
        return wrapper;
    }

    /**
     * 初始化
     * @param entity 租户管理实体类
     * @return query
     */
    public TenantAuthInfoQuery init(TenantAuthInfoEntity entity) {
        TenantAuthInfoQuery query = new TenantAuthInfoQuery();
        query.setTenantId(StringUtils.isNotBlank(entity.getTenantId()) ? entity.getTenantId() : "");
        query.setTenantName(StringUtils.isNotBlank(entity.getTenantName()) ? entity.getTenantName() : "");
        query.setAgentId(StringUtils.isNotBlank(entity.getAgentId()) ? entity.getAgentId() : "");
        query.setAgentName(StringUtils.isNotBlank(entity.getAgentName()) ? entity.getAgentName() : "");
        query.setAuthCode(StringUtils.isNotBlank(entity.getAuthCode()) ? entity.getAuthCode() : "");
        return query;
    }
}
