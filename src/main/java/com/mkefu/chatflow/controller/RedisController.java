package com.mkefu.chatflow.controller;

import com.mkefu.chatflow.service.RedisService;
import com.mkefu.common.annotation.LogInteraction;
import com.mkefu.common.response.Result;
import com.mkefu.system.entity.TenantAuthInfoEntity;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR> 杨国锋
 * @since 2025-05-16
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/api/redis")
@Tag(name = "Redis操作控制层")
public class RedisController {

    private final RedisService redisService;

    public RedisController(RedisService redisService) {
        this.redisService = redisService;
    }

    @Operation(summary = "根据Key从Redis获取数据")
    @GetMapping("/getByKey")
    @LogInteraction(description = "根据Key从Redis获取数据")
    public Result getByKey(@NotBlank(message = "请传入key") @RequestParam("key") String key) {
        return Result.success(redisService.getKey(key));
    }

    @Operation(summary = "修改在Redis已有的数据")
    @PostMapping("/updateKey")
    @LogInteraction(description = "修改在Redis已有的数据")
    public Result updateKey(@Valid @RequestBody TenantAuthInfoEntity entity) {
        return Result.success(redisService.updateKey(entity));
    }
}
