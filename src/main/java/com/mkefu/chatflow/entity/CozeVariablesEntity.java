package com.mkefu.chatflow.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR> 杨国锋
 * @since 2025-05-29
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "智能体配置的变量实体类。")
public class CozeVariablesEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "是否启用该变量。" +
            "true：启用该变量。" +
            "false：未启用该变量。")
    private boolean enabled;

    @Schema(description = "变量来源。当前只支持展示用户变量（custom）。")
    private String channel;

    @Schema(description = "变量名。")
    private String keyword;

    @Schema(description = "变量描述。")
    private String description;

    @Schema(description = "变量的默认值。")
    @JsonProperty("default_value")
    private String defaultValue;

    @Schema(description = "是否允许该变量被 Prompt 访问。" +
            "true：变量支持在 Prompt 中访问。" +
            "false：变量不支持在 Prompt 中访问，仅能在工作流中访问。")
    @JsonProperty("prompt_enable")
    private boolean promptEnable;

}
