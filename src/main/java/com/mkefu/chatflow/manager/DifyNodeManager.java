package com.mkefu.chatflow.manager;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.mkefu.chatflow.entity.DifyTenantNodeAssignmentEntity;
import com.mkefu.common.constant.ErrorCode;
import com.mkefu.common.exception.BusinessException;
import com.mkefu.chatflow.entity.DifyNodesEntity;
import com.mkefu.chatflow.mapper.DifyNodeMapper;
import com.mkefu.chatflow.mapper.DifyTenantNodeAssignmentMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Dify 节点管理器，负责管理 Dify 节点并为租户自动分配最优节点 URL
 * <AUTHOR> Yang 杨国锋
 * @since 2025-04-23
 */
@Slf4j
@Component
@DS("postgresql")
@ConditionalOnProperty(name = "dify.node-manager.enabled", havingValue = "true", matchIfMissing = true)
public class DifyNodeManager {

    private final DifyNodeMapper difyNodeMapper;

    private final DifyTenantNodeAssignmentMapper tenantNodeAssignmentMapper;

    /**
     * 存储所有活跃节点，键为 instanceId
     */
    private final Map<String, DifyNodesEntity> nodes = new ConcurrentHashMap<>();

    /**
     * 存储租户的节点分配，键为 tenantId
     */
    private final Map<String, NodeAssignment> nodeAssignments = new ConcurrentHashMap<>();

    private final UpdateWrapper<DifyNodesEntity> updateWrapper = new UpdateWrapper<>();


    public DifyNodeManager(DifyNodeMapper difyNodeMapper, DifyTenantNodeAssignmentMapper tenantNodeAssignmentMapper) {
        this.difyNodeMapper = difyNodeMapper;
        this.tenantNodeAssignmentMapper = tenantNodeAssignmentMapper;
    }

    /**
     * 初始化节点缓存，加载活跃节点
     */
    @PostConstruct
    public void init() {
        refreshNodes();
    }

    /**
     * 定期刷新活跃节点缓存
     */
    @Scheduled(fixedRateString = "${dify.node-refresh-interval}")
    public void refreshNodes() {
        try {
            // 查询所有活跃节点（active = true）
            List<DifyNodesEntity> activeNodes = difyNodeMapper.selectList(
                    new QueryWrapper<DifyNodesEntity>().eq("active", true)
            );
            // 更新节点缓存
            Map<String, DifyNodesEntity> newNodes = new ConcurrentHashMap<>(2);
            activeNodes.forEach(node -> newNodes.put(node.getInstanceId(), node));
            nodes.clear();
            nodes.putAll(newNodes);
            log.info("刷新 Dify 节点完成，节点数: {}", nodes.size());
        } catch (Exception e) {
            log.error("刷新 Dify 节点失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 为指定租户获取活跃节点 URL，优先复用已有分配，否则选择新节点
     * @param tenantId 租户唯一标识
     * @return 活跃的 Dify 节点实体
     * @throws BusinessException 如果没有可用节点
     */
    public DifyNodesEntity getNodeUrl(String tenantId) {
        NodeAssignment assignment = nodeAssignments.get(tenantId);
        if (assignment != null && assignment.isValid()) {
            // 复用有效分配
            return assignment.node;
        }
        // 选择新节点并缓存分配
        DifyNodesEntity node = assignNode(tenantId);
        nodeAssignments.put(tenantId, new NodeAssignment(node, LocalDateTime.now()));
        // 持久化分配到数据库
        saveTenantNodeAssignment(tenantId, node);
        return node;
    }

    /**
     * 根据节点延迟加权随机选择最优节点
     * @param tenantId 租户标识，用于日志
     * @return 选择的 Dify 节点实体
     * @throws BusinessException 如果没有可用节点
     */
    private DifyNodesEntity assignNode(String tenantId) {
        // 获取所有活跃节点
        List<DifyNodesEntity> availableNodes = nodes.values().stream()
                .filter(DifyNodesEntity::isActive)
                .toList();
        if (availableNodes.isEmpty()) {
            throw new BusinessException(ErrorCode.DIFY_NODE_NOT_FUND, "没有可用的 Dify 节点");
        }
        // 计算总权重（延迟倒数之和）
        double totalWeight = availableNodes.stream()
                .mapToDouble(node -> 1.0 / (node.getLatency() + 1e-6))
                .sum();
        // 加权随机选择节点
        double random = Math.random() * totalWeight;
        double currentWeight = 0;
        for (DifyNodesEntity node : availableNodes) {
            currentWeight += 1.0 / (node.getLatency() + 1e-6);
            if (currentWeight >= random) {
                // 记录分配日志
                log.info("为租户 {} 分配节点 {}, 延迟: {}ms", tenantId, node.getInstanceId(), node.getLatency());
                return node;
            }
        }
        // 回退到第一个活跃节点
        DifyNodesEntity fallbackNode = availableNodes.getFirst();
        log.info("为租户 {} 分配回退节点 {}, 延迟: {}ms", tenantId, fallbackNode.getInstanceId(), fallbackNode.getLatency());
        return fallbackNode;
    }

    /**
     * 定期检查节点健康状态并更新
     */
    @Scheduled(fixedRate = 60000)
    public void checkNodeHealth() {
        try {
            // 查询最近一小时内更新的节点
            List<DifyNodesEntity> recentNodes = difyNodeMapper.selectList(
                    new QueryWrapper<DifyNodesEntity>().gt("updated_at", LocalDateTime.now().minusHours(1))
            );
            for (DifyNodesEntity node : recentNodes) {
                // 检查健康状态并更新延迟
                boolean isHealthy = checkHealth(node);
                if (node.isActive() != isHealthy) {
                    // 更新节点状态和更新时间
                    updateWrapper.eq("instance_id", node.getInstanceId())
                            .set("active", isHealthy)
                            .set("updated_at", LocalDateTime.now());
                    difyNodeMapper.update(updateWrapper);
                    updateWrapper.clear();
                    log.info("更新节点 {} 健康状态: active={}", node.getInstanceId(), isHealthy);
                }
            }
        } catch (Exception e) {
            log.error("健康检查失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 检查节点健康状态并记录延迟
     * @param node 待检查的节点
     * @return 节点是否健康
     */
    private boolean checkHealth(DifyNodesEntity node) {
        try {
            // 构造健康检查 URL
            String healthUrl = node.getUrl().replace("/v1", "");
            long startTime = System.nanoTime();
            HttpRequest request = HttpRequest.get(healthUrl).timeout(5000);
            try (HttpResponse response = request.execute()) {
                // 计算请求延迟（毫秒）
                double latency = (System.nanoTime() - startTime) / 1_000_000.0;
                node.setLatency(latency);
                return response.getStatus() == 404 || response.getStatus() == 307;
            }
        } catch (Exception e) {
            log.error("健康检测失败 for URL {}: {}", node.getUrl(), e.getMessage());
            node.setLatency(Double.MAX_VALUE);
            return false;
        }
    }

    /**
     * 保存租户节点分配到数据库
     * @param tenantId 租户标识
     * @param node 分配的节点
     */
    private void saveTenantNodeAssignment(String tenantId, DifyNodesEntity node) {
        try {
            DifyTenantNodeAssignmentEntity assignment = new DifyTenantNodeAssignmentEntity(
                    tenantId, node.getInstanceId(), LocalDateTime.now()
            );
            tenantNodeAssignmentMapper.insert(assignment);
            log.info("为租户 {} 持久化分配节点 {}", tenantId, node.getInstanceId());
        } catch (Exception e) {
            log.error("持久化租户 {} 分配失败: {}", tenantId, e.getMessage(), e);
        }
    }

    /**
     * 节点分配记录，包含节点和分配时间
     */
    static class NodeAssignment {
        DifyNodesEntity node;
        LocalDateTime assignedAt;
        // 分配有效期 1 小时
        long ttlSeconds = 3600;

        NodeAssignment(DifyNodesEntity node, LocalDateTime assignedAt) {
            this.node = node;
            this.assignedAt = assignedAt;
        }

        /**
         * 检查分配是否有效
         * @return 如果分配未过期且节点活跃，返回 true
         */
        boolean isValid() {
            return assignedAt.plusSeconds(ttlSeconds).isAfter(LocalDateTime.now()) && node.isActive();
        }
    }
}
