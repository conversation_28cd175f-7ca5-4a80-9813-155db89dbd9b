package com.mkefu.chatflow.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR> Yang 杨国锋
 * @since 2025-04-29
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "Dify获取下一轮建议问题列表请求DTO", description = "DifySuggestedRequestDto")
public class DifySuggestedRequestDto implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "消息ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "请传入messageId！")
    @JsonProperty("message_id")
    private String messageId;

    @Schema(description = "用户标识", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "请传入user！")
    private String user;

}
