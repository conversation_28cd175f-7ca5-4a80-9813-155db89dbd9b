package com.mkefu.wechat.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 代开发企业应用授权信息请求DTO
 * <AUTHOR> 杨国锋
 * @since 2025-06-18
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "代开发企业应用授权信息请求DTO", description = "WeChatThirdPartyAuthInfoRequestDto")
public class WeChatThirdPartyAuthInfoRequestDto {
    
    @Schema(description = "第三方应用id或者代开发应用模板id。第三方应用以ww或wx开头应用id（对应于旧的以tj开头的套件id）；代开发应用以dk开头")
    @JsonProperty("suite_id")
    private String suiteId;

    @Schema(description = "第三方应用secret 或者代开发应用模板secret")
    @JsonProperty("suite_secret")
    private String suiteSecret;

    @Schema(description = "企业微信后台推送的ticket")
    @JsonProperty("suite_ticket")
    private String suiteTicket;
}
