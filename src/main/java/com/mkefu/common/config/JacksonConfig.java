package com.mkefu.common.config; // 建议放在 common.config 包下

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary; // 引入 @Primary

/**
 * 全局 Jackson ObjectMapper 配置类。
 * 用于配置 Spring Boot 应用中默认的 ObjectMapper，
 * 尤其禁用其默认类型信息，以避免在反序列化不带 @class 的 JSON 对象时抛出 InvalidTypeIdException。
 * 这适用于 Web 请求/响应的 JSON 处理。
 *
 * <AUTHOR>
 * @since 2025-06-26
 */
@Configuration
public class JacksonConfig {

    /**
     * 配置并提供应用程序全局使用的 ObjectMapper Bean。
     * 该 ObjectMapper 禁用了默认类型信息添加，以避免在反序列化纯 JSON 对象时出现 '@class' 属性问题。
     * 标记为 @Primary 以确保这是 Spring IoC容器中首选的 ObjectMapper。
     *
     * @return 配置好的 ObjectMapper 实例
     */
    @Bean
    @Primary 
    public ObjectMapper objectMapper() {
        ObjectMapper objectMapper = new ObjectMapper();
        // 忽略 JSON 中存在但 Java 对象中不存在的属性，防止反序列化失败
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        // 如果遇到空 Bean，不抛出异常，而是序列化为空对象
        objectMapper.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);
        // 关键配置：禁用 Jackson 的默认类型（@class）信息添加。
        // 这意味着在序列化时不会自动添加类型标识，反序列化时也不会期望存在类型标识。
        // 这是解决 InvalidTypeIdException 的核心。
        objectMapper.disableDefaultTyping();
        // 如果需要，可以在这里添加其他 Jackson 模块或配置，例如：
        // objectMapper.registerModule(new JavaTimeModule()); // 如果 POJO 中包含 Java 8 日期时间类型
        return objectMapper;
    }
}
