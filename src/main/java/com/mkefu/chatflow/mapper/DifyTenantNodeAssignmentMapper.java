package com.mkefu.chatflow.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mkefu.chatflow.entity.DifyTenantNodeAssignmentEntity;
import io.swagger.v3.oas.annotations.media.Schema;


/**
 * <AUTHOR> 杨国锋
 * @since 2025-05-14
 */
@DS("postgresql")
@Schema(description = "租户节点分配 Mapper 接口")
public interface DifyTenantNodeAssignmentMapper extends BaseMapper<DifyTenantNodeAssignmentEntity> {
}
