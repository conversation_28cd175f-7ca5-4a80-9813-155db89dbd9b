package com.mkefu.common.exception;

import com.mkefu.common.constant.ErrorCode;
import lombok.Getter;

/**
 * 业务异常类
 * <AUTHOR>
 * @since 2025-04-10
 */
@Getter
public class BusinessException extends RuntimeException {

    private final String errorCode;
    private final int statusCode;

    /**
     * 使用枚举构造异常
     * @param errorCode 枚举
     */
    public BusinessException(ErrorCode errorCode) {
        super(errorCode.getDefaultMessage());
        this.errorCode = errorCode.getCode();
        this.statusCode = errorCode.getStatus();
    }

    /**
     * 使用枚举和自定义消息构造异常
     * @param errorCode 枚举
     */
    public BusinessException(ErrorCode errorCode, String message) {
        super(message);
        this.errorCode = errorCode.getCode();
        this.statusCode = errorCode.getStatus();
    }

    /**
     * 全参数构造
     * @param errorCode 枚举
     * @param statusCode 状态码
     * @param message 消息
     */
    public BusinessException(String errorCode, String message, int statusCode) {
        super(message);
        this.errorCode = errorCode;
        this.statusCode = statusCode;
    }

    /**
     * 默认500状态码构造
     * @param errorCode 枚举
     * @param message 消息
     */
    public BusinessException(String errorCode, String message) {
        this(errorCode, message, 500);
    }

    /**
     * 带原因的构造
     * @param errorCode 枚举
     * @param cause 原因
     * @param statusCode 状态码
     * @param message 消息
     */
    public BusinessException(String errorCode, String message, int statusCode, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
        this.statusCode = statusCode;
    }

    /**
     * 使用枚举和原因构造异常
     * @param errorCode 枚举
     * @param cause 原因
     * @param message 消息
     */
    public BusinessException(ErrorCode errorCode, String message, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode.getCode();
        this.statusCode = errorCode.getStatus();
    }
}
