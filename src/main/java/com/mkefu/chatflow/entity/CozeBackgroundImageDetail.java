package com.mkefu.chatflow.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR> 杨国锋
 * @since 2025-05-29
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "智能体背景图实体类", description = "CozeBackgroundImageDetail")
public class CozeBackgroundImageDetail implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "背景图片的 URL 地址。")
    @JsonProperty("image_url")
    private String imageUrl;

    @Schema(description = "背景图片的主题颜色，通常用于与图片搭配的其他元素的颜色。格式为十六进制颜色代码。")
    @JsonProperty("theme_color")
    private String themeColor;

    @Schema(description = "定义裁剪区域在画布（Canvas）上的位置和尺寸。通过指定裁剪区域的顶部、左侧偏移量以及宽度和高度，来确定裁剪的具体范围。")
    @JsonProperty("canvas_position")
    private CozeCanvasPositionEntity canvasPosition;

    @Schema(description = "设置背景图渐变效果。通过指定渐变的左右边界位置，控制渐变的起始和结束点，从而实现背景图的渐变效果。")
    @JsonProperty("gradient_position")
    private CozeGradientPositionEntity gradientPosition;

}
