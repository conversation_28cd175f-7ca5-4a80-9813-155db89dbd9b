package com.mkefu.chatflow.service;

import com.mkefu.chatflow.dto.*;
import com.mkefu.chatflow.dto.CommonCancelChatRequestDto;
import com.mkefu.system.entity.TenantAuthInfoEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import reactor.core.publisher.Flux;

/**
 * <AUTHOR> 杨国锋
 * @since 2025-05-06
 */
@Schema(description = "coze聊天服务接口")
public interface CozeChatService {

    /**
     * 发起对话-SSE流
     * @param dto 数据传输对象
     * @param accessToken 访问令牌
     * @return 响应内容
     */
    Flux<String> chatMessagesStream(CozeChatRequestDto dto, String accessToken);

    /**
     * 发起对话
     * @param dto 数据传输对象
     * @param accessToken 访问令牌
     * @return 响应内容
     */
    CozeMessageResponseDto chatMessages(CozeChatRequestDto dto, String accessToken);

    /**
     * 查看对话消息详情
     * @param accessToken 访问令牌
     * @param conversationId 会话Id
     * @param chatId 消息Id
     * @return 响应内容
     */
    CozeMessageResponseDto getMessages(String accessToken, String conversationId, String chatId);

    /**
     * 查看对话详情
     * @param accessToken 访问令牌
     * @param conversationId 会话Id
     * @param chatId 消息Id
     * @return 响应内容
     */
    CozeConversationResponseDto getConversation(String accessToken, String conversationId, String chatId);

    /**
     * 取消进行中的对话-COZE官方API
     * @param dto 取消进行中的对话请求实体类
     * @param accessToken 访问令牌
     * @return 响应内容
     */
    CommonCancelChatResponseDto cancelChat(CommonCancelChatRequestDto dto, String accessToken);

    /**
     * 获取智能体信息-COZE官方API
     * @param tenantAuthInfoEntity 租户信息实体类
     * @param accessToken 访问令牌
     * @return 响应内容
     */
    CozeAgentInfoResponseDto getAgentInfo(TenantAuthInfoEntity tenantAuthInfoEntity, @NotBlank(message = "请传入Authorization") String accessToken);
}
