<?xml version="1.0" encoding="UTF-8"?>
<jmeterTestPlan version="1.2" properties="5.0" jmeter="5.6.3">
  <hashTree>
    <TestPlan guiclass="TestPlanGui" testclass="TestPlan" testname="Dify压测">
      <boolProp name="TestPlan.tearDown_on_shutdown">true</boolProp>
      <elementProp name="TestPlan.user_defined_variables" elementType="Arguments" guiclass="ArgumentsPanel" testclass="Arguments" testname="User Defined Variables">
        <collectionProp name="Arguments.arguments"/>
      </elementProp>
      <boolProp name="TestPlan.functional_mode">false</boolProp>
      <boolProp name="TestPlan.serialize_threadgroups">false</boolProp>
    </TestPlan>
    <hashTree>
      <ThreadGroup guiclass="ThreadGroupGui" testclass="ThreadGroup" testname="Dify压测">
        <intProp name="ThreadGroup.num_threads">200</intProp>
        <intProp name="ThreadGroup.ramp_time">1</intProp>
        <longProp name="ThreadGroup.duration">180</longProp>
        <longProp name="ThreadGroup.delay">5</longProp>
        <boolProp name="ThreadGroup.same_user_on_next_iteration">true</boolProp>
        <boolProp name="ThreadGroup.scheduler">true</boolProp>
        <stringProp name="ThreadGroup.on_sample_error">continue</stringProp>
        <elementProp name="ThreadGroup.main_controller" elementType="LoopController" guiclass="LoopControlPanel" testclass="LoopController" testname="Loop Controller">
          <stringProp name="LoopController.loops">1</stringProp>
          <boolProp name="LoopController.continue_forever">false</boolProp>
        </elementProp>
      </ThreadGroup>
      <hashTree>
        <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="前置-香港节点_chat-message(前置-香港节点_chat-message)">
          <stringProp name="HTTPSampler.domain">************</stringProp>
          <stringProp name="HTTPSampler.protocol">http</stringProp>
          <stringProp name="HTTPSampler.path">/v1/chat-messages</stringProp>
          <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
          <stringProp name="HTTPSampler.method">POST</stringProp>
          <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
          <boolProp name="HTTPSampler.postBodyRaw">true</boolProp>
          <elementProp name="HTTPsampler.Arguments" elementType="Arguments">
            <collectionProp name="Arguments.arguments">
              <elementProp name="" elementType="HTTPArgument">
                <boolProp name="HTTPArgument.always_encode">false</boolProp>
                <stringProp name="Argument.value">{   &#xd;
	&quot;files&quot;: [],   &#xd;
	&quot;query&quot;: &quot;你好~&quot;,   &#xd;
	&quot;inputs&quot;: {},   &#xd;
	&quot;workflow_id&quot;: &quot;3f732cba-8334-4527-9a12-98e183fb8d56&quot;,&#xd;
	&quot;user&quot;: &quot;${tid}&quot; &#xd;
}</stringProp>
                <stringProp name="Argument.metadata">=</stringProp>
              </elementProp>
            </collectionProp>
          </elementProp>
        </HTTPSamplerProxy>
        <hashTree>
          <HeaderManager guiclass="HeaderPanel" testclass="HeaderManager" testname="HTTP Header Manager">
            <collectionProp name="HeaderManager.headers">
              <elementProp name="" elementType="Header">
                <stringProp name="Header.name">Authorization</stringProp>
                <stringProp name="Header.value">Bearer app-bfevMY03zfTkI3IBDKPxLfao</stringProp>
              </elementProp>
              <elementProp name="" elementType="Header">
                <stringProp name="Header.name">Content-Type</stringProp>
                <stringProp name="Header.value">application/json</stringProp>
              </elementProp>
            </collectionProp>
          </HeaderManager>
          <hashTree/>
          <ResponseAssertion guiclass="AssertionGui" testclass="ResponseAssertion" testname="Response Assertion">
            <collectionProp name="Asserion.test_strings">
              <stringProp name="49586">200</stringProp>
            </collectionProp>
            <stringProp name="Assertion.custom_message"></stringProp>
            <stringProp name="Assertion.test_field">Assertion.response_code</stringProp>
            <boolProp name="Assertion.assume_success">false</boolProp>
            <intProp name="Assertion.test_type">8</intProp>
          </ResponseAssertion>
          <hashTree/>
          <BeanShellPostProcessor guiclass="TestBeanGUI" testclass="BeanShellPostProcessor" testname="BeanShell PostProcessor">
            <stringProp name="filename"></stringProp>
            <stringProp name="parameters"></stringProp>
            <boolProp name="resetInterpreter">false</boolProp>
            <stringProp name="script">//获取响应代码Unicode编码的
        String s2=new String(prev.getResponseData(),&quot;UTF-8&quot;);
//---------------以下步骤为转码过程---------------
        char aChar;
        int len= s2.length();
        StringBuffer outBuffer=new StringBuffer(len);
        for(int x =0; x &lt;len;){
            aChar= s2.charAt(x++);
            if(aChar==&apos;\\&apos;){
                aChar= s2.charAt(x++);
                if(aChar==&apos;u&apos;){
                    int value =0;
                    for(int i=0;i&lt;4;i++){
                        aChar= s2.charAt(x++);
                        switch(aChar){
                            case&apos;0&apos;:
                            case&apos;1&apos;:
                            case&apos;2&apos;:
                            case&apos;3&apos;:
                            case&apos;4&apos;:
                            case&apos;5&apos;:
                            case&apos;6&apos;:
                            case&apos;7&apos;:
                            case&apos;8&apos;:
                            case&apos;9&apos;:
                                value=(value &lt;&lt;4)+aChar-&apos;0&apos;;
                                break;
                            case&apos;a&apos;:
                            case&apos;b&apos;:
                            case&apos;c&apos;:
                            case&apos;d&apos;:
                            case&apos;e&apos;:
                            case&apos;f&apos;:
                                value=(value &lt;&lt;4)+10+aChar-&apos;a&apos;;
                                break;
                            case&apos;A&apos;:
                            case&apos;B&apos;:
                            case&apos;C&apos;:
                            case&apos;D&apos;:
                            case&apos;E&apos;:
                            case&apos;F&apos;:
                                value=(value &lt;&lt;4)+10+aChar-&apos;A&apos;;
                                break;
                            default:
                                throw new IllegalArgumentException(
                                        &quot;Malformed   \\uxxxx  encoding.&quot;);}}
                    outBuffer.append((char) value);}else{
                    if(aChar==&apos;t&apos;)
                        aChar=&apos;\t&apos;;
                    else if(aChar==&apos;r&apos;)
                    aChar=&apos;\r&apos;;
                    else if(aChar==&apos;n&apos;)
                    aChar=&apos;\n&apos;;
                    else if(aChar==&apos;f&apos;)
                    aChar=&apos;\f&apos;;
                    outBuffer.append(aChar);}}else
                outBuffer.append(aChar);}
//-----------------以上内容为转码过程---------------------------
//将转成中文的响应结果在查看结果树中显示
        prev.setResponseData(outBuffer.toString());</stringProp>
          </BeanShellPostProcessor>
          <hashTree/>
          <BeanShellPostProcessor guiclass="TestBeanGUI" testclass="BeanShellPostProcessor" testname="BeanShell PostProcessor">
            <boolProp name="resetInterpreter">false</boolProp>
            <stringProp name="parameters"></stringProp>
            <stringProp name="filename"></stringProp>
            <stringProp name="script">import com.alibaba.fastjson2.*;
 
//获取请求的返回值
String response_data = prev.getResponseDataAsString();
//日志打印获取请求的返回值
log.info(&quot;================response data: &quot;+response_data+&quot;====&quot;);
//将String类型的返回值构造成JSONObject对象
JSONObject data_obj = JSON.parse(response_data);
//将obj转成JSONObject类型
String conversation_id = data_obj.getString(&quot;conversation_id&quot;);
String answer_content = data_obj.getString(&quot;answer&quot;);
log.info(&quot;================response conversation_id: &quot;+conversation_id+&quot;====&quot;);
log.info(&quot;================response answer: &quot;+answer_content+&quot;====&quot;);

int answer_len = answer_content.length();
log.info(&quot;================len: &quot;+answer_len+&quot;========================&quot;);

 
//把长度塞进pats_len这个JMeter变量里
vars.put(&quot;answer_len&quot;,answer_len+&quot;&quot;);
vars.put(&quot;answer_content&quot;,answer_content);
vars.put(&quot;conversation_id&quot;,conversation_id);

</stringProp>
          </BeanShellPostProcessor>
          <hashTree/>
          <BeanShellAssertion guiclass="BeanShellAssertionGui" testclass="BeanShellAssertion" testname="BeanShell Assertion">
            <stringProp name="BeanShellAssertion.query">int answer_len = Integer.parseInt( vars.get(&quot;answer_len&quot;));
log.info(&quot;================response data: &quot;+answer_len+&quot;====&quot;);
if(answer_len &gt; 0){
	Failure = false;
}else{
	Failure = true;
	FailureMessage=&quot;返回的回答长度==0&quot;;
}</stringProp>
            <stringProp name="BeanShellAssertion.filename"></stringProp>
            <stringProp name="BeanShellAssertion.parameters"></stringProp>
            <boolProp name="BeanShellAssertion.resetInterpreter">false</boolProp>
          </BeanShellAssertion>
          <hashTree/>
          <BeanShellPreProcessor guiclass="TestBeanGUI" testclass="BeanShellPreProcessor" testname="BeanShell 预处理程序">
            <stringProp name="filename"></stringProp>
            <stringProp name="parameters"></stringProp>
            <boolProp name="resetInterpreter">false</boolProp>
            <stringProp name="script">long tid = Thread.currentThread().getId();
vars.put(&quot;tid&quot;,tid+&quot;&quot;);</stringProp>
          </BeanShellPreProcessor>
          <hashTree/>
        </hashTree>
        <IfController guiclass="IfControllerPanel" testclass="IfController" testname="If Controller">
          <stringProp name="IfController.condition">${__jexl3(${answer_len} &gt; 0)}</stringProp>
          <boolProp name="IfController.evaluateAll">false</boolProp>
          <boolProp name="IfController.useExpression">true</boolProp>
        </IfController>
        <hashTree>
          <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="二次发送-香港节点_chat-message(二次发送-香港节点_chat-message)">
            <stringProp name="HTTPSampler.domain">************</stringProp>
            <stringProp name="HTTPSampler.protocol">http</stringProp>
            <stringProp name="HTTPSampler.path">/v1/chat-messages</stringProp>
            <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
            <stringProp name="HTTPSampler.method">POST</stringProp>
            <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
            <boolProp name="HTTPSampler.postBodyRaw">true</boolProp>
            <elementProp name="HTTPsampler.Arguments" elementType="Arguments">
              <collectionProp name="Arguments.arguments">
                <elementProp name="" elementType="HTTPArgument">
                  <boolProp name="HTTPArgument.always_encode">false</boolProp>
                  <stringProp name="Argument.value">{   &#xd;
	&quot;files&quot;: [],   &#xd;
	&quot;query&quot;: &quot;使用小美系统有什么规则限制？~&quot;,   &#xd;
	&quot;inputs&quot;: {},   &#xd;
	&quot;workflow_id&quot;: &quot;3f732cba-8334-4527-9a12-98e183fb8d56&quot;,&#xd;
	&quot;conversation_id&quot;: &quot;${conversation_id}&quot;,&#xd;
	&quot;user&quot;: &quot;${tid}&quot; &#xd;
}</stringProp>
                  <stringProp name="Argument.metadata">=</stringProp>
                </elementProp>
              </collectionProp>
            </elementProp>
          </HTTPSamplerProxy>
          <hashTree>
            <HeaderManager guiclass="HeaderPanel" testclass="HeaderManager" testname="HTTP Header Manager">
              <collectionProp name="HeaderManager.headers">
                <elementProp name="" elementType="Header">
                  <stringProp name="Header.name">Authorization</stringProp>
                  <stringProp name="Header.value">Bearer app-bfevMY03zfTkI3IBDKPxLfao</stringProp>
                </elementProp>
                <elementProp name="" elementType="Header">
                  <stringProp name="Header.name">Content-Type</stringProp>
                  <stringProp name="Header.value">application/json</stringProp>
                </elementProp>
              </collectionProp>
            </HeaderManager>
            <hashTree/>
            <ResponseAssertion guiclass="AssertionGui" testclass="ResponseAssertion" testname="Response Assertion">
              <collectionProp name="Asserion.test_strings">
                <stringProp name="49586">200</stringProp>
              </collectionProp>
              <stringProp name="Assertion.custom_message"></stringProp>
              <stringProp name="Assertion.test_field">Assertion.response_code</stringProp>
              <boolProp name="Assertion.assume_success">false</boolProp>
              <intProp name="Assertion.test_type">8</intProp>
            </ResponseAssertion>
            <hashTree/>
            <BeanShellPostProcessor guiclass="TestBeanGUI" testclass="BeanShellPostProcessor" testname="BeanShell PostProcessor" enabled="true">
              <stringProp name="filename"></stringProp>
              <stringProp name="parameters"></stringProp>
              <boolProp name="resetInterpreter">false</boolProp>
              <stringProp name="script">//获取响应代码Unicode编码的
        String s2=new String(prev.getResponseData(),&quot;UTF-8&quot;);
//---------------以下步骤为转码过程---------------
        char aChar;
        int len= s2.length();
        StringBuffer outBuffer=new StringBuffer(len);
        for(int x =0; x &lt;len;){
            aChar= s2.charAt(x++);
            if(aChar==&apos;\\&apos;){
                aChar= s2.charAt(x++);
                if(aChar==&apos;u&apos;){
                    int value =0;
                    for(int i=0;i&lt;4;i++){
                        aChar= s2.charAt(x++);
                        switch(aChar){
                            case&apos;0&apos;:
                            case&apos;1&apos;:
                            case&apos;2&apos;:
                            case&apos;3&apos;:
                            case&apos;4&apos;:
                            case&apos;5&apos;:
                            case&apos;6&apos;:
                            case&apos;7&apos;:
                            case&apos;8&apos;:
                            case&apos;9&apos;:
                                value=(value &lt;&lt;4)+aChar-&apos;0&apos;;
                                break;
                            case&apos;a&apos;:
                            case&apos;b&apos;:
                            case&apos;c&apos;:
                            case&apos;d&apos;:
                            case&apos;e&apos;:
                            case&apos;f&apos;:
                                value=(value &lt;&lt;4)+10+aChar-&apos;a&apos;;
                                break;
                            case&apos;A&apos;:
                            case&apos;B&apos;:
                            case&apos;C&apos;:
                            case&apos;D&apos;:
                            case&apos;E&apos;:
                            case&apos;F&apos;:
                                value=(value &lt;&lt;4)+10+aChar-&apos;A&apos;;
                                break;
                            default:
                                throw new IllegalArgumentException(
                                        &quot;Malformed   \\uxxxx  encoding.&quot;);}}
                    outBuffer.append((char) value);}else{
                    if(aChar==&apos;t&apos;)
                        aChar=&apos;\t&apos;;
                    else if(aChar==&apos;r&apos;)
                    aChar=&apos;\r&apos;;
                    else if(aChar==&apos;n&apos;)
                    aChar=&apos;\n&apos;;
                    else if(aChar==&apos;f&apos;)
                    aChar=&apos;\f&apos;;
                    outBuffer.append(aChar);}}else
                outBuffer.append(aChar);}
//-----------------以上内容为转码过程---------------------------
//将转成中文的响应结果在查看结果树中显示
        prev.setResponseData(outBuffer.toString());</stringProp>
            </BeanShellPostProcessor>
            <hashTree/>
            <BeanShellPostProcessor guiclass="TestBeanGUI" testclass="BeanShellPostProcessor" testname="BeanShell PostProcessor" enabled="true">
              <boolProp name="resetInterpreter">false</boolProp>
              <stringProp name="parameters"></stringProp>
              <stringProp name="filename"></stringProp>
              <stringProp name="script">import com.alibaba.fastjson2.*;
 
//获取请求的返回值
String response_data = prev.getResponseDataAsString();
//日志打印获取请求的返回值
log.info(&quot;================response data: &quot;+response_data+&quot;====&quot;);
//将String类型的返回值构造成JSONObject对象
JSONObject data_obj = JSON.parse(response_data);
//将obj转成JSONObject类型
String conversation_id = data_obj.getString(&quot;conversation_id&quot;);
String answer_content = data_obj.getString(&quot;answer&quot;);
log.info(&quot;================response conversation_id: &quot;+conversation_id+&quot;====&quot;);
log.info(&quot;================response answer: &quot;+answer_content+&quot;====&quot;);

int answer_len = answer_content.length();
log.info(&quot;================len: &quot;+answer_len+&quot;========================&quot;);

 
//把长度塞进pats_len这个JMeter变量里
vars.put(&quot;answer_len&quot;,answer_len+&quot;&quot;);
vars.put(&quot;answer_content&quot;,answer_content);
vars.put(&quot;conversation_id&quot;,conversation_id);
</stringProp>
            </BeanShellPostProcessor>
            <hashTree/>
            <BeanShellAssertion guiclass="BeanShellAssertionGui" testclass="BeanShellAssertion" testname="BeanShell Assertion" enabled="true">
              <stringProp name="BeanShellAssertion.query">int answer_len = Integer.parseInt( vars.get(&quot;answer_len&quot;));
log.info(&quot;================response data: &quot;+answer_len+&quot;====&quot;);
if(answer_len &gt; 0){
	Failure = false;
}else{
	Failure = true;
	FailureMessage=&quot;返回的回答长度==0&quot;;
}</stringProp>
              <stringProp name="BeanShellAssertion.filename"></stringProp>
              <stringProp name="BeanShellAssertion.parameters"></stringProp>
              <boolProp name="BeanShellAssertion.resetInterpreter">false</boolProp>
            </BeanShellAssertion>
            <hashTree/>
          </hashTree>
        </hashTree>
      </hashTree>
      <ResultCollector guiclass="ViewResultsFullVisualizer" testclass="ResultCollector" testname="查看结果树">
        <boolProp name="ResultCollector.error_logging">false</boolProp>
        <objProp>
          <name>saveConfig</name>
          <value class="SampleSaveConfiguration">
            <time>true</time>
            <latency>true</latency>
            <timestamp>true</timestamp>
            <success>true</success>
            <label>true</label>
            <code>true</code>
            <message>true</message>
            <threadName>true</threadName>
            <dataType>true</dataType>
            <encoding>false</encoding>
            <assertions>true</assertions>
            <subresults>true</subresults>
            <responseData>false</responseData>
            <samplerData>false</samplerData>
            <xml>false</xml>
            <fieldNames>true</fieldNames>
            <responseHeaders>false</responseHeaders>
            <requestHeaders>false</requestHeaders>
            <responseDataOnError>false</responseDataOnError>
            <saveAssertionResultsFailureMessage>true</saveAssertionResultsFailureMessage>
            <assertionsResultsToSave>0</assertionsResultsToSave>
            <bytes>true</bytes>
            <sentBytes>true</sentBytes>
            <url>true</url>
            <threadCounts>true</threadCounts>
            <idleTime>true</idleTime>
            <connectTime>true</connectTime>
          </value>
        </objProp>
        <stringProp name="filename"></stringProp>
      </ResultCollector>
      <hashTree/>
      <ResultCollector guiclass="SummaryReport" testclass="ResultCollector" testname="汇总报告">
        <boolProp name="ResultCollector.error_logging">false</boolProp>
        <objProp>
          <name>saveConfig</name>
          <value class="SampleSaveConfiguration">
            <time>true</time>
            <latency>true</latency>
            <timestamp>true</timestamp>
            <success>true</success>
            <label>true</label>
            <code>true</code>
            <message>true</message>
            <threadName>true</threadName>
            <dataType>true</dataType>
            <encoding>false</encoding>
            <assertions>true</assertions>
            <subresults>true</subresults>
            <responseData>false</responseData>
            <samplerData>false</samplerData>
            <xml>false</xml>
            <fieldNames>true</fieldNames>
            <responseHeaders>false</responseHeaders>
            <requestHeaders>false</requestHeaders>
            <responseDataOnError>false</responseDataOnError>
            <saveAssertionResultsFailureMessage>true</saveAssertionResultsFailureMessage>
            <assertionsResultsToSave>0</assertionsResultsToSave>
            <bytes>true</bytes>
            <sentBytes>true</sentBytes>
            <url>true</url>
            <threadCounts>true</threadCounts>
            <idleTime>true</idleTime>
            <connectTime>true</connectTime>
          </value>
        </objProp>
        <stringProp name="filename"></stringProp>
      </ResultCollector>
      <hashTree/>
      <Arguments guiclass="ArgumentsPanel" testclass="Arguments" testname="用户定义变量">
        <collectionProp name="Arguments.arguments">
          <elementProp name="secondLastOutputs" elementType="Argument">
            <stringProp name="Argument.name">secondLastOutputs</stringProp>
            <stringProp name="Argument.value">[object Object]</stringProp>
            <stringProp name="Argument.metadata">=</stringProp>
          </elementProp>
          <elementProp name="requestStatus" elementType="Argument">
            <stringProp name="Argument.name">requestStatus</stringProp>
            <stringProp name="Argument.value">200</stringProp>
            <stringProp name="Argument.metadata">=</stringProp>
          </elementProp>
          <elementProp name="erroMessage" elementType="Argument">
            <stringProp name="Argument.name">erroMessage</stringProp>
            <stringProp name="Argument.value">400</stringProp>
            <stringProp name="Argument.metadata">=</stringProp>
          </elementProp>
          <elementProp name="status" elementType="Argument">
            <stringProp name="Argument.name">status</stringProp>
            <stringProp name="Argument.value">succeeded</stringProp>
            <stringProp name="Argument.metadata">=</stringProp>
          </elementProp>
        </collectionProp>
      </Arguments>
      <hashTree/>
    </hashTree>
  </hashTree>
</jmeterTestPlan>
