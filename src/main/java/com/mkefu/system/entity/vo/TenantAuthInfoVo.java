package com.mkefu.system.entity.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 租户管理VO
 * <AUTHOR>
 * @since 2025-04-10
 */
@Data
@Schema(name = "租户授权码信息VO", description = "TenantAuthInfoVo")
public class TenantAuthInfoVo {

    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "租户ID")
    private String tenantId;

    @Schema(description = "租户名字")
    private String tenantName;

    @Schema(description = "授权码")
    private String authCode;

    @Schema(description = "删除标志 0-已删除 1-未删除")
    private String delFlag;
}
