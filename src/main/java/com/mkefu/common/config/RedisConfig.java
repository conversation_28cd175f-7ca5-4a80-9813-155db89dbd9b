package com.mkefu.common.config;

import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.redisson.spring.data.connection.RedissonConnectionFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.Resource;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.StringRedisSerializer;

import java.io.IOException;

/**
 * Redis 配置类，只使用 Redisson 配置连接 Redis
 * <AUTHOR>
 * @since 2025-04-11
 * @modified 2025-06-06
 */
@Configuration
public class RedisConfig {

    /**
     * Redisson 配置文件路径（通过 spring.redis.redisson.file 指定）
     */
    @Value("${spring.redis.redisson.file}")
    private Resource redissonConfigFile;

    /**
     * 配置 Redisson 客户端
     * @return RedissonClient
     * @throws IOException 如果加载 redisson.yaml 失败
     */
    @Bean(destroyMethod = "shutdown")
    public RedissonClient redissonClient() throws IOException {
        Config config = Config.fromYAML(redissonConfigFile.getInputStream());
        return Redisson.create(config);
    }

    /**
     * 配置 Spring Data Redis 的连接工厂，使用 Redisson 作为连接来源
     * @return RedissonConnectionFactory
     */
    @Bean
    public RedisConnectionFactory redisConnectionFactory(RedissonClient redissonClient) {
        return new RedissonConnectionFactory(redissonClient);
    }

    /**
     * 配置 RedisTemplate，用于 Spring Data Redis 操作
     * @param connectionFactory Redis 连接工厂
     * @return RedisTemplate
     */
    @Bean
    public RedisTemplate<String, String> redisTemplate(RedisConnectionFactory connectionFactory) {
        RedisTemplate<String, String> template = new RedisTemplate<>();
        template.setConnectionFactory(connectionFactory);

        // 设置序列化方式（仅使用 String）
        template.setKeySerializer(new StringRedisSerializer());
        template.setValueSerializer(new StringRedisSerializer());
        template.setHashKeySerializer(new StringRedisSerializer());
        template.setHashValueSerializer(new StringRedisSerializer());

        template.afterPropertiesSet();
        return template;
    }
}
