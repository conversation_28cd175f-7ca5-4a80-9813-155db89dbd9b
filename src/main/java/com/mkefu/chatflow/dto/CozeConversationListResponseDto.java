package com.mkefu.chatflow.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> Yang 杨国锋
 * @since 2025-05-08
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "会话列表响应实体类", description = "CozeConversationListResponseDto")
public class CozeConversationListResponseDto implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "调用状态码。0 表示调用成功，其他值表示调用失败，你可以通过 msg 字段判断详细的错误原因。")
    private int code;

    @Schema(description = "状态信息。API 调用失败时可通过此字段查看详细错误信息。")
    private String msg;

    @Schema(description = "会话列表的详细。")
    private ListConversationData data;

    @Schema(description = "响应的详细信息。")
    private Map<String, Object> detail;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "会话列表的详细。")
    public static class ListConversationData implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        @Schema(description = "是否还有更多会话未在本次请求中返回。" +
                "true：还有更多未返回的会话。" +
                "false：已返回符合筛选条件的全部会话。")
        @JsonProperty("has_more")
        private boolean hasMore;

        @Schema(description = "会话的详细信息。")
        private List<ConversationData> conversations;

    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "会话列表的详细。")
    public static class ConversationData implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        @Schema(description = "Conversation ID，即会话的唯一标识。")
        private String id;

        @Schema(description = "会话创建的时间。格式为 10 位的 Unixtime 时间戳，单位为秒。")
        @JsonProperty("created_at")
        private Long createdAt;

        @Schema(description = "会话中最新的一个上下文片段 ID。")
        @JsonProperty("last_section_id")
        private String lastSectionId;

    }
}
