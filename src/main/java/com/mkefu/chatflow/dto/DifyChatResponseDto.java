package com.mkefu.chatflow.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * Dify 聊天接口响应实体类
 *
 * <AUTHOR>
 * @since 2025-04-22
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "Dify 发送聊天响应DTO", description = "DifyChatResponseDto")
public class DifyChatResponseDto implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "当前问题是否在知识库匹配到，true：匹配到了  false：未匹配到")
    @JsonProperty("is_resolved")
    private boolean isResolved = true;

    @Schema(description = "事件类型，例如 'message'", example = "message")
    @JsonProperty("event")
    private String event;

    @Schema(description = "任务Id")
    @JsonProperty("task_id")
    private String taskId;

    @Schema(description = "消息 ID", example = "9da23599-e713-473b-982c-4328d4f5c78a")
    @JsonProperty("message_id")
    private String messageId;

    @Schema(description = "会话 ID", example = "*************-4bc5-8e9b-64562b4555f2")
    @JsonProperty("conversation_id")
    private String conversationId;

    @Schema(description = "聊天模式，例如 'chat'", example = "chat")
    @JsonProperty("mode")
    private String mode;

    @Schema(description = "回复内容", example = "iPhone 13 Pro Max specs are listed here:...")
    @JsonProperty("answer")
    private String answer;

    @Schema(description = "元数据，包含使用统计和检索资源")
    @JsonProperty("metadata")
    private Metadata metadata;

    @Schema(description = "创建时间戳（Unix 时间，秒）", example = "1705407629")
    @JsonProperty("created_at")
    private Long createdAt;

    /**
     * 元数据实体类
     */
    @Schema(description = "元数据，包含使用统计和检索资源")
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Metadata implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        @Schema(description = "使用统计信息")
        @JsonProperty("usage")
        private Usage usage;

        @Schema(description = "检索资源列表")
        @JsonProperty("retriever_resources")
        private List<RetrieverResource> retrieverResources;
    }

    /**
     * 使用统计实体类
     */
    @Schema(description = "使用统计信息")
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Usage implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        @Schema(description = "提示词 token 数", example = "1033")
        @JsonProperty("prompt_tokens")
        private Integer promptTokens;

        @Schema(description = "提示词单价", example = "0.001")
        @JsonProperty("prompt_unit_price")
        private String promptUnitPrice;

        @Schema(description = "提示词价格单位", example = "0.001")
        @JsonProperty("prompt_price_unit")
        private String promptPriceUnit;

        @Schema(description = "提示词总价", example = "0.0010330")
        @JsonProperty("prompt_price")
        private String promptPrice;

        @Schema(description = "完成 token 数", example = "128")
        @JsonProperty("completion_tokens")
        private Integer completionTokens;

        @Schema(description = "完成单价", example = "0.002")
        @JsonProperty("completion_unit_price")
        private String completionUnitPrice;

        @Schema(description = "完成价格单位", example = "0.001")
        @JsonProperty("completion_price_unit")
        private String completionPriceUnit;

        @Schema(description = "完成总价", example = "0.0002560")
        @JsonProperty("completion_price")
        private String completionPrice;

        @Schema(description = "总 token 数", example = "1161")
        @JsonProperty("total_tokens")
        private Integer totalTokens;

        @Schema(description = "总价", example = "0.0012890")
        @JsonProperty("total_price")
        private String totalPrice;

        @Schema(description = "货币单位", example = "USD")
        @JsonProperty("currency")
        private String currency;

        @Schema(description = "延迟时间（秒）", example = "0.7682376249867957")
        @JsonProperty("latency")
        private Double latency;
    }

    /**
     * 检索资源实体类
     */
    @Schema(description = "检索资源信息")
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RetrieverResource implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        @Schema(description = "资源位置", example = "1")
        @JsonProperty("position")
        private Integer position;

        @Schema(description = "数据集 ID", example = "101b4c97-fc2e-463c-90b1-5261a4cdcafb")
        @JsonProperty("dataset_id")
        private String datasetId;

        @Schema(description = "数据集名称", example = "iPhone")
        @JsonProperty("dataset_name")
        private String datasetName;

        @Schema(description = "文档 ID", example = "8dd1ad74-0b5f-4175-b735-7d98bbbb4e00")
        @JsonProperty("document_id")
        private String documentId;

        @Schema(description = "文档名称", example = "iPhone List")
        @JsonProperty("document_name")
        private String documentName;

        @Schema(description = "片段 ID", example = "ed599c7f-2766-4294-9d1d-e5235a61270a")
        @JsonProperty("segment_id")
        private String segmentId;

        @Schema(description = "相关性得分", example = "0.98457545")
        @JsonProperty("score")
        private Double score;

        @Schema(description = "资源内容", example = "\"Model\",\"Release Date\",...")
        @JsonProperty("content")
        private String content;
    }
}
