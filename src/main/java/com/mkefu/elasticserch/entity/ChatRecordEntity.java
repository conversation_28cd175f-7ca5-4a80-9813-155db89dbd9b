package com.mkefu.elasticserch.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 聊天记录实体类
 * <AUTHOR>
 * @since 2025-04-10
 */
@Data
@TableName("chat_record")
@Schema(name = "聊天记录实体类", description = "ChatRecordEntity")
public class ChatRecordEntity {

    @TableId(type = IdType.AUTO)
    @Schema(description = "自增主键ID", example = "1")
    private Long id;


}
