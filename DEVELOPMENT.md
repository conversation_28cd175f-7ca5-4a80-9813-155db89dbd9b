# mkefu-chatflow-service 开发指南

本文档为 mkefu-chatflow-service 项目的开发人员提供必要的信息。

## 构建/配置说明

### 前提条件
- Java 21
- Maven 3.8+
- MySQL 8.x
- Redis
- PostgreSQL (用于 Dify 集成)

### 构建项目
项目使用 Maven 进行构建管理。构建项目的方法：

```bash
# 不运行测试进行构建
mvn clean package

# 运行测试进行构建
mvn clean package -DskipTests=false
```

### 配置
应用程序使用 Spring Boot 的配置系统，采用 YAML 文件：

1. **主配置文件**: `src/main/resources/application.yaml`
2. **特定环境配置文件**: `src/main/resources/application-{profile}.yaml`

默认激活的环境是 `local`，在 `application.yaml` 中定义。

#### 关键配置属性
- **数据库**: 在特定环境的 YAML 文件中配置 MySQL 和 PostgreSQL 连接
- **Redis**: 在特定环境的 YAML 文件中配置 Redis 连接
- **API 密钥**: 在特定环境的 YAML 文件中配置外部 API 密钥（如快递鸟）

本地配置示例（来自 `application-local.yaml`）：
```yaml
spring:
  datasource:
    dynamic:
      primary: mysql
      datasource:
        mysql:
          url: ****************************************
          username: your_username
          password: your_password
```

## 测试信息

### 运行测试
测试使用 JUnit 5 实现，可以通过 Maven 运行：

```bash
# 运行所有测试
mvn test

# 运行特定测试类
mvn test -Dtest=KdniaoSignUtilTest

# 运行特定测试方法
mvn test -Dtest=KdniaoSignUtilTest#generateDataSign_ShouldReturnCorrectSignature
```

默认情况下，构建过程中会跳过测试。要在构建过程中启用测试，请在 Maven 命令或 `pom.xml` 中设置 `maven.test.skip=false`。

### 编写测试
项目使用：
- JUnit 5 作为测试框架
- Mockito 用于模拟依赖
- Spring Boot Test 用于集成测试

#### 单元测试示例
以下是工具类单元测试的简单示例：

```java
@ExtendWith(MockitoExtension.class)
class KdniaoSignUtilTest {

    @Mock
    private KuaidiniaoConfigEntity kuaidiniaoConfigEntity;

    private KdniaoSignUtil kdniaoSignUtil;

    @BeforeEach
    void setUp() {
        // 创建测试对象
        kdniaoSignUtil = new KdniaoSignUtil(kuaidiniaoConfigEntity);
    }

    @Test
    void generateDataSign_ShouldReturnCorrectSignature() {
        // 配置模拟对象
        when(kuaidiniaoConfigEntity.getApiKey()).thenReturn("test-api-key");

        // 准备测试数据
        Map<String, Object> requestMap = new HashMap<>();
        requestMap.put("RequestData", "{\"OrderCode\":\"test123\"}");

        // 执行测试
        String signature = kdniaoSignUtil.generateDataSign(requestMap);

        // 验证结果
        assertNotNull(signature);
    }
}
```

#### 集成测试示例
对于控制器测试，使用 Spring Boot Test 加载应用程序上下文：

```java
@SpringBootTest
class ChatFlowControllerTest {

    @Autowired
    private TenantAuthInfoServiceImpl tenantAuthInfoServiceImpl;

    @Autowired
    private SignatureUtil signatureUtil;

    @BeforeEach
    void setUp() {
        // 设置测试数据
    }

    @Test
    void testEndpoint() {
        // 测试 API 端点
    }
}
```

### 测试最佳实践
1. **隔离**: 测试应该独立，不依赖外部系统
2. **模拟**: 使用 Mockito 模拟依赖
3. **命名**: 使用描述性的测试方法名称（例如，`methodName_scenario_expectedBehavior`）
4. **断言**: 使用 JUnit 断言验证结果
5. **覆盖率**: 争取高测试覆盖率，特别是对关键业务逻辑

## 其他开发信息

### 代码风格
- 项目遵循标准 Java 代码风格约定
- 使用 Lombok 注解减少样板代码
- 使用 Javadoc 注释记录公共 API

### 项目结构
- `src/main/java/com/mkefu/chatflow`: 核心聊天流功能
- `src/main/java/com/mkefu/common`: 通用工具和配置
- `src/main/java/com/mkefu/system`: 系统级功能

### 关键组件
1. **控制器**: 处理 HTTP 请求和响应
2. **服务**: 实现业务逻辑
3. **DTO**: 用于 API 请求/响应的数据传输对象
4. **实体**: 数据库实体
5. **工具类**: 用于通用功能的辅助类

### API 文档
项目使用 SpringDoc OpenAPI 进行 API 文档编制。访问 API 文档的地址：
```
http://localhost:8080/swagger-ui.html
```

### 日志记录
- 使用带有 Lombok 的 `@Slf4j` 注解进行日志记录
- 在适当的级别（INFO、DEBUG、ERROR）记录重要事件
- 在日志消息中包含相关上下文

### 错误处理
- 使用自定义异常处理业务逻辑错误
- 在控制器级别使用 `@ExceptionHandler` 处理异常
- 返回适当的 HTTP 状态码和错误消息

### 安全性
- API 端点使用基于签名的身份验证进行保护
- 使用 `SignatureUtil` 类生成签名
- 使用时间戳防止重放攻击
