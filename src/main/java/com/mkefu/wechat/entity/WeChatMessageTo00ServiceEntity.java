package com.mkefu.wechat.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.mkefu.common.entity.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 类描述
 *
 * <AUTHOR> 杨国锋
 * @since 2025-07-07
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Schema(name = "适配微信不同消息类型的实体类", description = "WeChatMessageTo00ServiceEntity")
public class WeChatMessageTo00ServiceEntity extends BaseEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "消息类型")
    @JsonProperty("msgtype")
    private String msgType;

    @Schema(description = "消息内容")
    private String content;
    
    @Schema(description = "菜单id")
    @JsonProperty("menu_id")
    private String menuId;

    @Schema(description = "图片文件Id、语音文件Id、视频文件Id、普通文件Id")
    private String mediaId;
    
    @Schema(description = "纬度")
    private Float locationLatitude;

    @Schema(description = "经度")
    private Float locationLongitude;

    @Schema(description = "位置名")
    private String locationName;

    @Schema(description = "地址详情说明")
    private String locationAddress;

    @Schema(description = "标题")
    private String linkTitle;

    @Schema(description = "描述")
    private String linkDesc;

    @Schema(description = "点击后跳转的链接")
    private String linkUrl;

    @Schema(description = "缩略图链接")
    private String linkPicUrl;

    @Schema(description = "名片userid")
    private String businessCardUserid;

    @Schema(description = "小程序标题")
    private String miniProgramTitle;

    @Schema(description = "小程序appid")
    private String miniProgramAppId;

    @Schema(description = "点击消息卡片后进入的小程序页面路径")
    private String miniProgramPagePath;

    @Schema(description = "小程序消息封面的mediaid")
    private String miniProgramThumbMediaId;

    @Schema(description = "菜单起始文本")
    private String msgMenuHeadContent;

    @Schema(description = "菜单项配置实体类")
    private List<WeChatMsgMenuEntity> msgMenuList;

    @Schema(description = "视频号商品ID")
    private String channelsShopOrderProductId;

    @Schema(description = "视频号商品图片")
    private String channelsShopProductHeadImage;

    @Schema(description = "视频号商品标题")
    private String channelsShopProductTitle;

    @Schema(description = "视频号商品价格，以分为单位")
    private String channelsShopProductSalesPrice;

    @Schema(description = "视频号商品店铺名称")
    private String channelsShopProductShopNickName;

    @Schema(description = "视频号商品店铺头像")
    private String channelsShopProductShopHeadImage;

    @Schema(description = "视频号订单号")
    private String channelsShopOrderId;

    @Schema(description = "视频号订单商品标题")
    private String channelsShopOrderTitles;

    @Schema(description = "视频号订单价格描述")
    private String channelsShopOrderPriceWording;

    @Schema(description = "视频号订单状态")
    private String channelsShopOrderState;

    @Schema(description = "视频号订单缩略图")
    private String channelsShopOrderImageUrl;

    @Schema(description = "视频号订单店铺名称")
    private String channelsShopOrderShopNickName;

    @Schema(description = "聊天记录标题")
    private String mergedMsgTitle;

    @Schema(description = "消息记录内的消息内容")
    private Map<String, Object> mergedMsgItemMap;

    @Schema(description = "视频号消息")
    private Map<String, Object> channelsMap;
}
