package com.mkefu.wechat.controller;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mkefu.common.constant.CustomConstant;
import com.mkefu.common.constant.ErrorCode;
import com.mkefu.common.exception.BusinessException;
import com.mkefu.common.filter.AttackDetector;
import com.mkefu.common.util.SignatureUtil;
import com.mkefu.system.entity.TenantAuthInfoEntity;
import com.mkefu.system.service.impl.TenantAuthInfoServiceImpl;
import com.mkefu.wechat.dto.WeChatGetPreAuthCodeResponseDto;
import com.mkefu.wechat.entity.WeChatKfAccountInfoEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.HashMap;
import java.util.Map;

/**
 * 应用授权控制层测试类
 *
 * <AUTHOR> Yang 杨国锋
 * @since 2025-06-18
 */
@Slf4j
@SpringBootTest
@Schema(description = "应用授权控制层测试类")
class WeChatThirdPartyAuthInfoControllerTest {
    
    private static final String SUITE_ID = "ww2ff88ead3414d6c8";

    private static final String SUITE_SECRET = "Vpp5YRKwT0GSygOUrsPmUuR2fWRbY2GlFcisYM_a1dQ";

    private static final String SUITE_TICKET = "NJMw5zfiQzOLJhg9Lanh1nmVYLa5Zaz9EsNnl7W_oTouGshJZ9oxgOgNY7xF-fUG";
    
    private final String timestamp = String.valueOf(System.currentTimeMillis() / 1000);
    
    private final ObjectMapper objectMapper;
    
    private final TenantAuthInfoEntity tenantAuthInfoSecretEntity = new TenantAuthInfoEntity();

    private final TenantAuthInfoServiceImpl tenantAuthInfoServiceImpl;

    private final SignatureUtil signatureUtil;
    
    @Autowired
    WeChatThirdPartyAuthInfoControllerTest(ObjectMapper objectMapper, TenantAuthInfoServiceImpl tenantAuthInfoServiceImpl, SignatureUtil signatureUtil) {
        this.objectMapper = objectMapper;
        this.tenantAuthInfoServiceImpl = tenantAuthInfoServiceImpl;
        this.signatureUtil = signatureUtil;
    }

    @BeforeEach
    void setUp() {
        // 生成接口私钥
        tenantAuthInfoSecretEntity.setTenantId("T05158");
        tenantAuthInfoSecretEntity.setTenantName("L55555");
        TenantAuthInfoEntity authCodeEntity = tenantAuthInfoServiceImpl.generateMethodSecretKey(tenantAuthInfoSecretEntity);
        tenantAuthInfoSecretEntity.setAuthCode(authCodeEntity.getAuthCode());
        tenantAuthInfoSecretEntity.setHeadImageUrl("/Users/<USER>/Downloads/WorkSpace/mkefu-chatflow-service/微信客服时序图.png");
    }

    /**
     * 生成签名
     * @param object 对象
     * @param timestamp 时间戳
     * @param tenantId 租户ID
     * @return 签名
     */
    private String generateSignature(Object object, String timestamp, String tenantId) {
        try {
            return signatureUtil.generateSignature(object, timestamp, tenantId);
        } catch (Exception e) {
            throw new RuntimeException(e.getMessage());
        }
    }

    @Test
    void getThirdPartyCredentials() {
        Map<String, Object> requestMap = new HashMap<>(3);
        requestMap.put("suite_id", SUITE_ID);
        requestMap.put("suite_secret", SUITE_SECRET);
        requestMap.put("suite_ticket", SUITE_TICKET);
        try {
            HttpRequest request = HttpRequest
                    .post("http://localhost:8080/api/wechat/appAuth/getThirdPartyCredentials")
                    .body(objectMapper.writeValueAsString(requestMap));
            try (HttpResponse response = request.execute()) {
                String respBody = response.body();
                Assertions.assertTrue(response.isOk(), "获取第三方应用凭证失败：" + respBody);
                WeChatGetPreAuthCodeResponseDto dto = objectMapper.readValue(respBody, WeChatGetPreAuthCodeResponseDto.class);
                log.info("成功获取预授权码：{}", dto);
            }
        } catch (Exception e) {
            throw new BusinessException(ErrorCode.WECHAT_THIRD_PARTY_CREDENTIALS_FAILED.getCode(), e.getMessage());
        }
    }
    
    @Test
    void getCorpAccessToken() {
        try {
            String jsonStringBody = objectMapper.writeValueAsString(tenantAuthInfoSecretEntity);
            String signature = generateSignature(tenantAuthInfoSecretEntity, timestamp, tenantAuthInfoSecretEntity.getTenantId());
            HttpRequest request = HttpRequest
                    .post("localhost:8080/api/wechat/thirdPartyAuthInfo/getCorpAccessToken")
                    .header(CustomConstant.HEADER_X_SIGNATURE, signature)
                    .header(CustomConstant.HEADER_X_TIMESTAMP, timestamp)
                    .header(CustomConstant.HEADER_X_TENANTENTITY, jsonStringBody);
            try (HttpResponse response = request.execute()) {
                String body = response.body();
                log.info(body);
            }
        } catch (Exception e) {
            log.error("生成客服账号失败：{}", e.getMessage(), e);
        }
    }
    
    @Test
    void generatorKfAccount() {
        WeChatKfAccountInfoEntity weChatKfAccountInfoEntity = new WeChatKfAccountInfoEntity();
        weChatKfAccountInfoEntity.setKfName("测试1");
        weChatKfAccountInfoEntity.setHeadImageUrl("https://app-test0.chat5188.com//file/upload/a2m0kbRrI/LzOigv88/Gnf357t2/09b36efa80faa1bf7269847ebda28a61.jpg?m=Eohj3QZjQN0aw-PuTga6Cw&e=**********");
        weChatKfAccountInfoEntity.setTenantId("T05158");
        weChatKfAccountInfoEntity.setPlatFormType("wechat");
        try {
            String jsonStringBody = objectMapper.writeValueAsString(weChatKfAccountInfoEntity);
            String signature = generateSignature(weChatKfAccountInfoEntity, timestamp, tenantAuthInfoSecretEntity.getTenantId());
            HttpRequest request = HttpRequest
                    .post("localhost:8080/api/wechat/thirdPartyAuthInfo/generatorKfAccount")
                    .header(CustomConstant.HEADER_X_SIGNATURE, signature)
                    .header(CustomConstant.HEADER_X_TIMESTAMP, timestamp)
                    .body(jsonStringBody);
            try (HttpResponse response = request.execute()) {
                String body = response.body();
                log.info(body);
            }
        } catch (Exception e) {
            log.error("生成客服账号失败：{}", e.getMessage(), e);
        }
    }
    
    @Test
    void getAuthInfoUrlByTenantId() {
        try {
            String signature = generateSignature(tenantAuthInfoSecretEntity, timestamp, tenantAuthInfoSecretEntity.getTenantId());
            HttpRequest request = HttpRequest
                    .post("http://localhost:8080/api/wechat/thirdPartyAuthInfo/getAuthInfoUrlByTenantId")
                    .header(CustomConstant.HEADER_X_TIMESTAMP, timestamp)
                    .header(CustomConstant.HEADER_X_SIGNATURE, signature)
                    .header(CustomConstant.HEADER_X_TENANTENTITY, objectMapper.writeValueAsString(tenantAuthInfoSecretEntity));
            try (HttpResponse response = request.execute()) {
                String body = response.body();
                log.info(body);
            }
        } catch (Exception e) {
            log.error("获取授权URL失败", e);
            throw new BusinessException(ErrorCode.WECHAT_THIRD_PARTY_AUTH_INFO_NOT_FOUND.getCode(), e.getMessage());
        }
    }
}
