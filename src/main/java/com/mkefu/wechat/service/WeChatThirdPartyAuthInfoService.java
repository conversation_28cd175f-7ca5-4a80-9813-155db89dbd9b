package com.mkefu.wechat.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.mkefu.common.response.Result;
import com.mkefu.wechat.entity.WeChatKfAccountInfoEntity;
import com.mkefu.wechat.entity.WeChatThirdPartyAuthInfoEntity;
import com.mkefu.wechat.entity.WeChatThirdPartyBaseInfoEntity;

/**
 * 代开发应用授权信息接口层
 * <AUTHOR> Yang 杨国锋
 * @since 2025-06-18
 */
public interface WeChatThirdPartyAuthInfoService extends IService<WeChatThirdPartyAuthInfoEntity> {

    /**
     * 获取第三方应用凭证
     *
     * @param entity    请求体
     * @param receiveId 服务商企业Id
     */
    void getThirdPartyCredentials(WeChatThirdPartyAuthInfoEntity entity, String receiveId);

    /**
     * 获取预授权码
     * @param suiteAccessToken 第三方应用凭证
     * @return 响应体
     */
    Result getPreAuthCode(String suiteAccessToken);

    /**
     * 获取企业永久授权码
     *
     * @param suiteAccessToken  第三方应用凭证
     * @param temporaryAuthCode 用户扫二维码授权之后，微信回调给的临时授权码
     * @param suiteId           服务商应用Id
     * @param platFormId        平台Id
     * @param baseInfoEntity    服务商基础信息
     * @param state        00-service环境
     */
    void getPermanentCode(String suiteAccessToken, String temporaryAuthCode, String suiteId, String platFormId, WeChatThirdPartyBaseInfoEntity baseInfoEntity, String state);

    /**
     * 获取租户对应的授权绑定链接
     * @param jsonTenantAuthInfoEntity 租户实体类
     * @return 授权绑定链接
     */
    String getAuthInfoUrlByTenantId(String jsonTenantAuthInfoEntity);

    /**
     * 获取授权方企业凭证
     * @param jsonTenantAuthInfoEntity 租户信息
     * @return 授权方企业凭证
     */
    String getCorpAccessToken(String jsonTenantAuthInfoEntity);

    /**
     * 生成客服账号与客服链接
     * @param weChatKfAccountInfoEntity 客服信息
     * @return 客服链接
     */
    String generatorKfAccount(WeChatKfAccountInfoEntity weChatKfAccountInfoEntity);

    /**
     * 获取授权方企业授权信息
     * @param jsonTenantAuthInfoEntity 租户信息
     * @return 认证名称
     */
    String getCorpAuthInfo(String jsonTenantAuthInfoEntity);
}
