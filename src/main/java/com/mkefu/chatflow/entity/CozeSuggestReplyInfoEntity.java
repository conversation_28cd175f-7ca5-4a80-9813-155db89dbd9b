package com.mkefu.chatflow.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR> 杨国锋
 * @since 2025-05-29
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "用户问题建议实体类", description = "CozeSuggestReplyInfoEntity")
public class CozeSuggestReplyInfoEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "回复模式。")
    @JsonProperty("reply_mode")
    private String replyMode;

    @Schema(description = "custom 模式下的自定义 prompt。")
    @JsonProperty("customized_prompt")
    private String customizedPrompt;

}
