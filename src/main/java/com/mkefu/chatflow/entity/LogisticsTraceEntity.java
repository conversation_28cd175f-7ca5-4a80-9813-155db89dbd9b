package com.mkefu.chatflow.entity;

import com.mkefu.common.entity.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 物流轨迹实体类
 * <AUTHOR>
 * @since 2025-04-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(name = "物流轨迹实体类", description = "LogisticsTraceEntity")
public class LogisticsTraceEntity extends BaseEntity {

    @Schema(description = "物流单号")
    private String trackingNumber;

    @Schema(description = "物流公司编码")
    private String carrierCode;

    @Schema(description = "物流公司名称")
    private String carrierName;

    @Schema(description = "物流状态")
    private String status;

    @Schema(description = "物流轨迹")
    private static List<TraceDetail> traces;


    @Data
    public static class TraceDetail {

        @Schema(description = "物流时间")
        private String time;

        @Schema(description = "物流地点")
        private String location;

        @Schema(description = "物流描述")
        private String description;
    }
}
