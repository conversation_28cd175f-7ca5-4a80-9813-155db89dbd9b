package com.mkefu.common.util;

import org.springframework.stereotype.Component;

import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.util.Base64;

/**
 * 请求ImService加密解密工具
 * <AUTHOR> 黄杰文
 * @since 2025-07-18
 */
@Component
public class AesEncryptionUtil {
    
    /**
     * 使用 AES 加密数据
     *
     * @param data   待加密的数据
     * @param aesKey AES密钥
     * @param iv     初始化向量
     * @return 加密后的数据
     * @throws Exception 加密过程中可能抛出的异常
     */
    public static byte[] encryptAeS(byte[] data, SecretKey aesKey, byte[] iv) throws Exception {
        Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
        IvParameterSpec ivSpec = new IvParameterSpec(iv);
        cipher.init(Cipher.ENCRYPT_MODE, aesKey, ivSpec);
        return cipher.doFinal(data);
    }

    /**
     * AES加密字符串
     *
     * @param data 加密byte串
     * @return 加密后的数据
     */
    public static String encryptAesString(String data, String key, String iv) throws Exception {
        byte[] ivBytes = Base64.getDecoder().decode(iv);
        byte[] keyBytes = Base64.getDecoder().decode(key);
        byte[] byteData = encryptAeS(data.getBytes(StandardCharsets.UTF_8),
                new SecretKeySpec(keyBytes, 0, keyBytes.length, "AES"),
                ivBytes);
        return Base64.getEncoder().encodeToString(byteData);
    }

    /**
     * 使用 AES 解密数据
     *
     * @param encryptedData 加密的数据
     * @param aesKey        AES密钥
     * @param iv            初始化向量
     * @return 解密后的原始数据
     * @throws Exception 解密过程中可能抛出的异常
     */
    public static byte[] decryptAeS(byte[] encryptedData, SecretKey aesKey, byte[] iv) throws Exception {
        Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
        IvParameterSpec ivSpec = new IvParameterSpec(iv);
        cipher.init(Cipher.DECRYPT_MODE, aesKey, ivSpec);
        return cipher.doFinal(encryptedData);
    }

    /**
     * 解密转为字符
     *
     * @param data 需要解密的数据
     * @return 解密后的数据
     */
    public static String decryptAesString(String data, String base64Key, String iv) {
        byte[] dataBytes = null;
        try {
            byte[] ivBytes = Base64.getDecoder().decode(iv);
            byte[] base64KeyBytes = Base64.getDecoder().decode(base64Key);
            dataBytes = decryptAeS(Base64.getDecoder().decode(data),
                    new SecretKeySpec(base64KeyBytes, 0, base64KeyBytes.length, "AES"),
                    ivBytes);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return dataBytes == null ? "" : new String(dataBytes, StandardCharsets.UTF_8);
    }
}
