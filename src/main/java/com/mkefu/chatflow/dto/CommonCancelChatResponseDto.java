package com.mkefu.chatflow.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.Map;

/**
 * <AUTHOR> 杨国锋
 * @since 2025-06-09
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "通用取消进行中的对话响应对象", description = "CommonCancelChatResponseDto")
public class CommonCancelChatResponseDto implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;
    
    @Schema(description = "Dify中固定返回success")
    private String result;

    @Schema(description = "Coze被取消对话的详细信息")
    private Map<String, Object> data;

    @Schema(description = "Coze详细信息")
    private Map<String, Object> detail;

    @Schema(description = "Coze调用状态码。" +
            "0 表示调用成功。" +
            "其他值表示调用失败，你可以通过 msg 字段判断详细的错误原因。")
    private int code;

    @Schema(description = "Coze调用状态信息。API 调用失败时可通过此字段查看详细错误信息。")
    private String msg;
    
    
    
    
}
