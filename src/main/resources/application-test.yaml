spring:
  datasource:
    dynamic:
      # 指定主数据源为 MySQL
      primary: mysql
      datasource:
        mysql:
          url: *************************************************************************************************************************************************************
          username: ktkip_fpwjz_b
          password: Sq@x7(UDR-&lm%UF@nOJ0RqIOGlce
          driver-class-name: com.mysql.cj.jdbc.Driver
          hikari:
            minimum-idle: 5
            maximum-pool-size: 20
            idle-timeout: 300000
            max-lifetime: 1800000
            connection-timeout: 60000
            connection-test-query: SELECT 1

        postgresql:
          url: ****************************************************
          username: postgres
          password: difyai123456
          driver-class-name: org.postgresql.Driver
          hikari:
            minimum-idle: 5
            maximum-pool-size: 20
            idle-timeout: 300000
            max-lifetime: 1800000
            connection-timeout: 60000
            connection-test-query: SELECT 1
            # 将其设置为 -1 意味着 HikariCP 不会在启动时失败，即使无法建立初始连接。
            # 它会在应用程序首次尝试获取连接时才去尝试建立连接。如果那时数据库仍然不可用，那么只有尝试访问数据库的那个操作会失败，而不是整个应用程序启动失败。
            initialization-fail-timeout: -1
  data:
    jdbc:
      repositories:
        enabled: false
  redis:
    redisson:
      file: file:./config/redisson-test.yaml

  jackson:
    generator:
      # 禁用非 ASCII 字符的 Unicode 转义
      escape-non-ascii: false
    time-zone:
      Asia/Shanghai
    date-format:
      yyyy-MM-dd HH:mm:ss

  elasticsearch:
    uris: http://192.168.1.123:9200
    username: elastic
    password: chamc@1100337ygf

mybatis-plus:
  mapper-locations: classpath:mapper/*.xml
#  configuration:
#    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

api:
  logistics:
    kuaidiniao:
      api-url: https://api.kdniao.com/api/dist
      api-key: ad00d83c-ba77-4b83-972e-7a595fa117a5
      business-id: 1884791

# 私钥默认过期时间，单位秒
signature:
  validity-period-time: 86400
# 租户授权码过期时间，单位秒
authCode:
  validity-period-time: 4102185600

dify:
  node-refresh-interval: 60s
  node-manager:
    # 默认为 true，如果数据库连接不上，可以设置为 false
    enabled: false

# Resilience4j 重试配置
resilience4j:
  retry:
    instances:
      difyApi:
        max-attempts: 3
        wait-duration: 500ms
        retry-exceptions:
          - java.net.ConnectException
          - java.net.SocketTimeoutException

chatflow-config:
  notmatchChinese: 犇犇犇犇犇犇犇
  notmatchEnglish: 犇犇犇
  
wechat:
  kf:
    name: 测试客服账号
    receiveId: ww7aec752786f26e1d
    headImageUrl: /Users/<USER>/Downloads/WorkSpace/mkefu-chatflow-service/微信客服时序图.png
    
# ImService服务配置
im-service:
  aesKey: LeZaRfMSU2quchiauXnYCYqeyBnKkXeRepAL/m2Aw+c=
  aesIv: A4o3pJdC82jFSFRcdgIPqw==