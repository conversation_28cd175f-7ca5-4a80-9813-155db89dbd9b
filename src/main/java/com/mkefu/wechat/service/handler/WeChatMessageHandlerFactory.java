package com.mkefu.wechat.service.handler;

import com.mkefu.common.constant.CustomConstant;
import com.mkefu.wechat.service.handler.impl.*;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 微信消息处理器工厂类
 * 负责管理和提供不同类型消息的处理器
 *
 * <AUTHOR> 杨国锋
 * @since 2025-07-08
 */
@Slf4j
@Component
public class WeChatMessageHandlerFactory {

    private final Map<String, MessageHandler> messageHandlerMap = new HashMap<>();

    private final TextMessageHandler textMessageHandler;
    private final ImageMessageHandler imageMessageHandler;
    private final VoiceMessageHandler voiceMessageHandler;
    private final VideoMessageHandler videoMessageHandler;
    private final FileMessageHandler fileMessageHandler;
    private final LocationMessageHandler locationMessageHandler;
    private final LinkMessageHandler linkMessageHandler;
    private final EventMessageHandler eventMessageHandler;
    private final BusinessCardMessageHandler businessCardMessageHandler;
    private final MiniprogramMessageHandler miniprogramMessageHandler;
    private final MsgMenuMessageHandler msgMenuMessageHandler;
    private final ChannelsShopProductMessageHandler channelsShopProductMessageHandler;
    private final ChannelsShopOrderMessageHandler channelsShopOrderMessageHandler;
    private final MergedMsgMessageHandler mergedMsgMessageHandler;
    private final ChannelsMessageHandler channelsMessageHandler;
    private final MeetingMessageHandler meetingMessageHandler;
    private final CalendarMessageHandler calendarMessageHandler;
    private final NoteMessageHandler noteMessageHandler;
    private final DefaultMessageHandler defaultMessageHandler;

    @Autowired
    public WeChatMessageHandlerFactory(
            TextMessageHandler textMessageHandler,
            ImageMessageHandler imageMessageHandler,
            VoiceMessageHandler voiceMessageHandler,
            VideoMessageHandler videoMessageHandler,
            FileMessageHandler fileMessageHandler,
            LocationMessageHandler locationMessageHandler,
            LinkMessageHandler linkMessageHandler,
            EventMessageHandler eventMessageHandler,
            BusinessCardMessageHandler businessCardMessageHandler,
            MiniprogramMessageHandler miniprogramMessageHandler,
            MsgMenuMessageHandler msgMenuMessageHandler,
            ChannelsShopProductMessageHandler channelsShopProductMessageHandler,
            ChannelsShopOrderMessageHandler channelsShopOrderMessageHandler,
            MergedMsgMessageHandler mergedMsgMessageHandler,
            ChannelsMessageHandler channelsMessageHandler,
            MeetingMessageHandler meetingMessageHandler,
            CalendarMessageHandler calendarMessageHandler,
            NoteMessageHandler noteMessageHandler,
            DefaultMessageHandler defaultMessageHandler) {
        this.textMessageHandler = textMessageHandler;
        this.imageMessageHandler = imageMessageHandler;
        this.voiceMessageHandler = voiceMessageHandler;
        this.videoMessageHandler = videoMessageHandler;
        this.fileMessageHandler = fileMessageHandler;
        this.locationMessageHandler = locationMessageHandler;
        this.linkMessageHandler = linkMessageHandler;
        this.eventMessageHandler = eventMessageHandler;
        this.businessCardMessageHandler = businessCardMessageHandler;
        this.miniprogramMessageHandler = miniprogramMessageHandler;
        this.msgMenuMessageHandler = msgMenuMessageHandler;
        this.channelsShopProductMessageHandler = channelsShopProductMessageHandler;
        this.channelsShopOrderMessageHandler = channelsShopOrderMessageHandler;
        this.mergedMsgMessageHandler = mergedMsgMessageHandler;
        this.channelsMessageHandler = channelsMessageHandler;
        this.meetingMessageHandler = meetingMessageHandler;
        this.calendarMessageHandler = calendarMessageHandler;
        this.noteMessageHandler = noteMessageHandler;
        this.defaultMessageHandler = defaultMessageHandler;
    }

    /**
     * 初始化消息处理器映射表
     */
    @PostConstruct
    private void initMessageHandlers() {
        // 注册各种消息类型的处理器
        messageHandlerMap.put(CustomConstant.MESSAGE_TYPE_TEXT, textMessageHandler);
        messageHandlerMap.put(CustomConstant.MESSAGE_TYPE_IMAGE, imageMessageHandler);
        messageHandlerMap.put(CustomConstant.MESSAGE_TYPE_VOICE, voiceMessageHandler);
        messageHandlerMap.put(CustomConstant.MESSAGE_TYPE_VIDEO, videoMessageHandler);
        messageHandlerMap.put(CustomConstant.MESSAGE_TYPE_FILE, fileMessageHandler);
        messageHandlerMap.put(CustomConstant.MESSAGE_TYPE_LOCATION, locationMessageHandler);
        messageHandlerMap.put(CustomConstant.MESSAGE_TYPE_LINK, linkMessageHandler);
        messageHandlerMap.put(CustomConstant.MESSAGE_TYPE_EVENT, eventMessageHandler);
        messageHandlerMap.put(CustomConstant.MESSAGE_TYPE_BUSINESS_CARD, businessCardMessageHandler);
        messageHandlerMap.put(CustomConstant.MESSAGE_TYPE_MINIPROGRAM, miniprogramMessageHandler);
        messageHandlerMap.put(CustomConstant.MESSAGE_TYPE_MSG_MENU, msgMenuMessageHandler);
        messageHandlerMap.put(CustomConstant.MESSAGE_TYPE_CHANNELS_SHOP_PRODUCT, channelsShopProductMessageHandler);
        messageHandlerMap.put(CustomConstant.MESSAGE_TYPE_CHANNELS_SHOP_ORDER, channelsShopOrderMessageHandler);
        messageHandlerMap.put(CustomConstant.MESSAGE_TYPE_MERGED_MSG, mergedMsgMessageHandler);
        messageHandlerMap.put(CustomConstant.MESSAGE_TYPE_CHANNELS, channelsMessageHandler);
        messageHandlerMap.put(CustomConstant.MESSAGE_TYPE_MEETING, meetingMessageHandler);
        messageHandlerMap.put(CustomConstant.MESSAGE_TYPE_CALENDAR, calendarMessageHandler);
        messageHandlerMap.put(CustomConstant.MESSAGE_TYPE_NOTE, noteMessageHandler);

        log.info("微信消息处理器初始化完成，共加载 {} 种消息类型处理器", messageHandlerMap.size());
    }

    /**
     * 获取指定消息类型的处理器
     *
     * @param msgType 消息类型
     * @return 对应的消息处理器，如果不存在返回默认处理器
     */
    public MessageHandler getHandler(String msgType) {
        return messageHandlerMap.getOrDefault(msgType, defaultMessageHandler);
    }
}
