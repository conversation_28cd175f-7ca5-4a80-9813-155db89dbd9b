package com.mkefu.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mkefu.common.entity.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 系统交互日志实体类
 * <AUTHOR>
 * @since 2025-04-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("interaction_log")
@Schema(name = "系统交互日志实体类", description = "InteractionLogEntity")
public class InteractionLogEntity extends BaseEntity {

    @TableId(type = IdType.AUTO)
    @Schema(description = "自增主键ID", example = "1")
    private Long id;

    @TableField("method_name")
    @Schema(description = "执行的方法名称", example = "saveChatRecord")
    private String methodName;

    @TableField("arguments")
    @Schema(description = "方法传入的参数", example = "[ChatRecord(id=null, userId=user1, message=Hello, timestamp=2025-04-08T12:00:00)]")
    private String arguments;

    @TableField("result")
    @Schema(description = "方法返回的结果", example = "ChatRecord(id=abc123, userId=user1, message=Hello, timestamp=2025-04-08T12:00:00)")
    private String result;

    @TableField("exception")
    @Schema(description = "方法执行失败时的异常信息", example = "NullPointerException: Some error")
    private String exception;

    @TableField("execution_time")
    @Schema(description = "方法执行时间（毫秒）", example = "25")
    private Long executionTime;

    @TableField("timestamp")
    @Schema(description = "交互时间戳", example = "2025-04-08T12:00:01")
    private LocalDateTime timestamp;

    @TableField("description")
    @Schema(description = "来自@LogInteraction注解的方法描述", example = "Save a single chat record")
    private String description;

    @TableField("ip_address")
    @Schema(description = "请求的客户端IP地址", example = "***********")
    private String ipAddress;
}
