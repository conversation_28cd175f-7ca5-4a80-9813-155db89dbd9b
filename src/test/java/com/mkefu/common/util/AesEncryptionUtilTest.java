package com.mkefu.common.util;

import com.mkefu.common.entity.ImServiceConfigEntity;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * 类描述
 *
 * <AUTHOR> 杨国锋
 * @since 2025-07-22
 */
@Slf4j
@SpringBootTest
class AesEncryptionUtilTest {
    
    private final ImServiceConfigEntity imServiceConfigEntity ;

    @Autowired
    public AesEncryptionUtilTest(ImServiceConfigEntity imServiceConfigEntity) {
        this.imServiceConfigEntity = imServiceConfigEntity;
    }

    /**
     * 测试main方法
     */
    @Test
    void mockDataTest() throws Exception {
        String encData = "Hello, this is a secure message!";
        String eco = AesEncryptionUtil.encryptAesString(encData, imServiceConfigEntity.getAesKey(), imServiceConfigEntity.getAesIv());
        log.info("加密前：{}", encData);
        log.info("加密后：{}", eco);

        String dec = "4vXlhpODE9klvskL5Lzoe+TNqOOczyK2wjKwuu7tYKCknayyukQ9QQ2JT6CUqrh1v3RIZoF12c0E1kCNM5ixNHfB1HIodKwg2gbBl7rQqRSyJ+WzlMhFGetilYPk6Aje7lkKjPt2Y3Ag4o9N8r2yMm3UpJvxKL/XNSYPCI/R6/io5JTLZ+d6Hw7IHC4DchfY1OUWgTMlEsi7EWewlCnf2DwZiDTUQwXNxJp6i1C1TcxDDbmfbTSVcEe/3cpkavLnWoXiPRbzD7uRMGAdAfjPYdFeFUtq49fFMvbFJ5ZisuZdihAuEJg4ealv6K+nDy8JJKK+yawzgqurJ9PZTNlnWVhvmWPmR8Pj2IEidfE+5Db+4OcDOa+RH+GF8of8gjCymiNs7SfCiaARl22fxU7N/RcmFRwnsM88uNpjSb7pF380jIg5bK/WRqxyyBpQAiasb5xNDGMYwJCqxM/xdw4SzqZBV6B0SKpu54SoyN4cl6bjKujx0Pt0EppklIUR1YoFmAgCLcRTIXD9Jl0YH0v0PvrnQfjRMHIXw80tnZIxyYgYpR0ra+kSwLaFzSmfpz5t9ks54wzSO4XDiNUUobKkCUFRzfB9ZMRLIixAUeq5kcL7wJQcNaKLAEYZZCpSIZK1Tb9FMUiH2ntPedtPloPF4Ar3pxqfdSv9xRQI53/4aS/aVR0g4qgUEsxfYm8+wMv7CObm/kYkEV2vPdNXr2HZKew16574iLYZVC+ZRaW3LVEFhcWyTNd09n/RP2pqTbRHUcVkd+SwKgtZSa/qm8ueqqDR71eTpmcz5dHmPY/0EUcAg5lUfHYgFpu6gW6+3g8BF3rUW37NYsLOXJGkmQHLOIWEHPM0X6P9zoKFpSibfHXXLMNgjikWvA9Dfr2OhsN4sNmZbYyfBZNLVBOl4TCbxkTO9EKl8J3yjTkvTQxEDMs=";
        String decryptAesString = AesEncryptionUtil.decryptAesString(dec, imServiceConfigEntity.getAesKey(), imServiceConfigEntity.getAesIv());
        log.info("解密前：{}", dec);
        log.info("解密后：{}", decryptAesString);
    }
}
