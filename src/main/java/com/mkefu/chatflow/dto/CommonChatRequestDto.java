package com.mkefu.chatflow.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR> 杨国锋
 * @since 2025-06-09
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "")
public class CommonChatRequestDto implements Serializable {
    
    @Serial
    private static final long serialVersionUID = 1L;
    
    private CozeChatRequestDto cozeDto;
    
    private DifyChatRequestDto difyDto;
}
