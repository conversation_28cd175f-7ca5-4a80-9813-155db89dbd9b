package com.mkefu.wechat.controller;

import com.mkefu.common.annotation.LogInteraction;
import com.mkefu.common.constant.CustomConstant;
import com.mkefu.common.response.Result;
import com.mkefu.wechat.entity.WeChatKfAccountInfoEntity;
import com.mkefu.wechat.service.WeChatThirdPartyAuthInfoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 微信客服服务商授权信息Controller
 * 
 * <AUTHOR> Yang 杨国锋
 * @since 2025-06-30
 */
@Validated
@RestController
@RequestMapping("/api/wechat/thirdPartyAuthInfo")
@Schema(description = "微信客服服务商授权信息Controller")
public class WeChatThirdPartyAuthInfoController {
    
    private final WeChatThirdPartyAuthInfoService weChatThirdPartyAuthInfoService;

    public WeChatThirdPartyAuthInfoController(WeChatThirdPartyAuthInfoService weChatThirdPartyAuthInfoService) {
        this.weChatThirdPartyAuthInfoService = weChatThirdPartyAuthInfoService;
    }
    
    @Operation(summary = "获取租户对应的授权绑定链接")
    @LogInteraction(description = "获取租户对应的授权绑定链接")
    @PostMapping("/getAuthInfoUrlByTenantId")
    public Result getAuthInfoUrl(@RequestHeader(CustomConstant.HEADER_X_TENANTENTITY) String jsonTenantAuthInfoEntity) {
        return Result.success(weChatThirdPartyAuthInfoService.getAuthInfoUrlByTenantId(jsonTenantAuthInfoEntity));
    }
    
    @Operation(summary = "获取授权方企业凭证")
    @LogInteraction(description = "获取授权方企业凭证")
    @PostMapping("/getCorpAccessToken")
    public Result getCorpAccessToken(@RequestHeader(CustomConstant.HEADER_X_TENANTENTITY) String jsonTenantAuthInfoEntity) {
        return Result.success(weChatThirdPartyAuthInfoService.getCorpAccessToken(jsonTenantAuthInfoEntity));
    }

    @Operation(summary = "生成客服账号与客服链接")
    @LogInteraction(description = "生成客服账号与客服链接")
    @PostMapping("/generatorKfAccount")
    public Result generatorKfAccount(@RequestBody WeChatKfAccountInfoEntity weChatKfAccountInfoEntity) {
        return Result.success(weChatThirdPartyAuthInfoService.generatorKfAccount(weChatKfAccountInfoEntity));
    }
    
    @Operation(summary = "获取企业授权信息")
    @LogInteraction(description = "获取企业授权信息")
    @GetMapping("/getCorpAuthInfo")
    public Result getCorpAuthInfo(@RequestHeader(CustomConstant.HEADER_X_TENANTENTITY) String jsonTenantAuthInfoEntity) {
        return Result.success(weChatThirdPartyAuthInfoService.getCorpAuthInfo(jsonTenantAuthInfoEntity));
    }
}
