package com.mkefu.chatflow.dto;

import com.mkefu.chatflow.entity.CozeBaseResponseEntity;
import com.mkefu.chatflow.entity.CozeBotInfoEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR> Yang 杨国锋
 * @since 2025-05-29
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "智能体信息实体类", description = "CozeAgentInfoResponseDto")
@EqualsAndHashCode(callSuper = true)
public class CozeAgentInfoResponseDto extends CozeBaseResponseEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "响应的业务信息。")
    private CozeBotInfoEntity data;

}
