package com.mkefu.chatflow.service;

import com.mkefu.chatflow.dto.*;
import com.mkefu.chatflow.dto.CommonCancelChatRequestDto;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import reactor.core.publisher.Flux;

import java.util.Map;

/**
 * <AUTHOR> 杨国锋
 * @since 2025-04-17
 */
public interface DifyChatService {

    /**
     * 发送对话消息-SSE流
     * @param dto 数据传输对象
     * @param accessToken 令牌
     * @return 响应内容
     */
    Flux<String> chatMessagesStream(@Valid DifyChatRequestDto dto,
                                    @NotBlank(message = "请传入Authorization") String accessToken);

    /**
     * 发送对话消息-阻塞式
     * @param dto 数据传输对象
     * @param accessToken 令牌
     * @return 响应内容
     */
    DifyChatResponseDto chatMessages(@Valid DifyChatRequestDto dto,
                                     @NotBlank(message = "请传入Authorization") String accessToken);

    /**
     * 获取下一轮建议问题列表
     * @param difySuggestedRequestDto 数据传输对象
     * @param accessToken 令牌
     * @return 响应内容
     */
    DifySuggestedResponseDto getSuggested(DifySuggestedRequestDto difySuggestedRequestDto, String accessToken);


    /**
     * 获取会话列表
     * @param accessToken 令牌
     * @param params 参数
     * @return 响应内容
     */
    DifyGetConversationListResponseDto getConversationList(@NotBlank(message = "请传入Authorization") String accessToken,
                                                           @NotNull(message = "请传入Map结构") Map<String, Object> params);

    /**
     * 获取会话历史消息
     * @param accessToken 令牌
     * @param params 参数
     * @return 响应内容
     */
    DifyGetMessagesHistoryResponseDto getMessages(@NotBlank(message = "请传入Authorization") String accessToken,
                                                         @NotNull(message = "请传入Map结构") Map<String, Object> params);

    /**
     * 删除会话
     * @param accessToken 令牌
     * @param map 参数
     * @return 响应内容
     */
    String deleteConversationInfo(Map<String, Object> map, @NotBlank(message = "请传入Authorization") String accessToken);

    /**
     * 获取智能体信息
     * @param accessToken 令牌
     * @return 响应内容
     */
    DifyAgentInfoResponseDto getAgentInfo(@NotBlank(message = "请传入Authorization") String accessToken);

    /**
     * 取消对话
     * @param dto 数据传输对象
     * @param accessToken 令牌
     * @return 响应内容
     */
    CommonCancelChatResponseDto cancelChat(@Valid CommonCancelChatRequestDto dto, @NotBlank(message = "请传入Authorization") String accessToken);
}
