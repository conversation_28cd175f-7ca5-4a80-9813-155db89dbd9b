package com.mkefu.common.filter;

import org.jsoup.Jsoup;
import org.jsoup.safety.Safelist;
import org.owasp.encoder.Encode;
import org.owasp.encoder.Encoder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.HashSet;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Service
public class AttackDetector {
    
    private static final Logger logger = LoggerFactory.getLogger(AttackDetector.class);

    // XSS 模式（扩展以覆盖更多变体）
    private static final Pattern STORED_XSS = Pattern.compile("<script.*?>", Pattern.CASE_INSENSITIVE); // 存储型：完整<script>标签

    // 反射型：扩展关键词，包括base64/obfuscated
    private static final Pattern REFLECTED_XSS = Pattern.compile("javascript:|alert\\(|eval\\(|atob\\(|fromCharCode\\(|fetch\\(|constructor|appendChild|createElement|strict-dynamic", Pattern.CASE_INSENSITIVE); 
    
    private static final Pattern DOM_XSS = Pattern.compile("on\\w*=", Pattern.CASE_INSENSITIVE); // DOM型：事件处理器（如onerror、ontoggle、onloadstart）
    
    private static final Pattern BASE64_XSS = Pattern.compile("[A-Za-z0-9+/=]{40,}", Pattern.CASE_INSENSITIVE); // base64编码payload检测（长串）
    
    private static final Pattern SQL_INJECT = Pattern.compile("(drop|select|insert|update|delete|union|exec|truncate|--|/\\*)", Pattern.CASE_INSENSITIVE);

    // 恶意关键词检查（用于事件值和解码后的Base64）
    private static final Pattern MALICIOUS_EVENT_VALUE = Pattern.compile("alert|eval|atob|fetch|constructor|appendChild|createElement|src=|javascript:|http|document|window|body", Pattern.CASE_INSENSITIVE);/**

    /**
     * 检测并返回攻击类型
     * @param input 用户输入
     * @return 攻击类型字符串，或"SAFE"如果安全
     */
    public String detectAttackType(String input) {
//        if (input == null || input.isEmpty()) {
//            return "EMPTY_INPUT";
//        }
//
//        // 先解码可能URL编码的输入
//        String decoded = java.net.URLDecoder.decode(input, StandardCharsets.UTF_8);
//
//        Set<String> attacks = new HashSet<>();
//
//        // 检查存储型XSS
//        if (STORED_XSS.matcher(decoded).find()) {
//            attacks.add("STORED_XSS");
//        }
//
//        // 检查反射型XSS
//        if (REFLECTED_XSS.matcher(decoded).find()) {
//            attacks.add("REFLECTED_XSS");
//        }
//
//        // 检查DOM型XSS（事件处理器），并添加白名单逻辑
//        Matcher domMatcher = DOM_XSS.matcher(decoded);
//        while (domMatcher.find()) {
//            // 提取事件值（假设格式如onerror="value"）
//            int start = domMatcher.end();
//            int end = decoded.indexOf("\"", start + 1); // 假设双引号包围
//            if (end == -1) end = decoded.length();
//            String eventValue = decoded.substring(start, end).trim();
//
//            // 白名单：如果onclick='copyValue(this)'，视为安全
//            if (domMatcher.group().toLowerCase().contains("onclick") && eventValue.equals("copyValue(this)")) {
//                continue; // 跳过，安全
//            }
//
//            // 否则，检查值是否含恶意关键词
//            if (MALICIOUS_EVENT_VALUE.matcher(eventValue).find()) {
//                attacks.add("DOM_XSS");
//            }
//        }
//
//        // 检查base64 XSS（新增解码验证）
//        Matcher base64Matcher = BASE64_XSS.matcher(decoded);
//        while (base64Matcher.find()) {
//            String base64Str = base64Matcher.group();
//            try {
//                byte[] bytes = Base64.getDecoder().decode(base64Str);
//                String decodedBase64 = new String(bytes, StandardCharsets.UTF_8);
//                if (MALICIOUS_EVENT_VALUE.matcher(decodedBase64).find()) {
//                    attacks.add("BASE64_XSS");
//                }
//            } catch (IllegalArgumentException e) {
//                // 无效Base64（如文件路径），忽略
//            }
//        }
//
//        // 检查SQL注入
//        if (SQL_INJECT.matcher(decoded).find()) {
//            attacks.add("SQL_INJECTION");
//        }
//
//        if (!attacks.isEmpty()) {
//            String type = String.join(",", attacks);
//            logger.warn("Detected attack: {} in {}", type, decoded);
//            return type;
//        }
//
//        // 使用Jsoup检查潜在HTML标签（允许基本标签和图片）
//        Safelist safelist = Safelist.basicWithImages()
//                .addAttributes("a", "onclick")  // 允许a的onclick
//                .addAttributes("div", "class")  // 允许div的class
//                .addAttributes("span", "class")  // 允许span的class
//                .addAttributes("img", "src", "alt", "width", "height");  // 显式允许img属性
//
//        // 新增规范化步骤：比较规范化的原HTML与清洗后
//        String normalized = Jsoup.parse(decoded).html();  // 规范化原输入
//        String cleaned = Jsoup.clean(decoded, safelist);
//        if (!cleaned.equals(normalized)) {
//            logger.warn("Detected Potential XSS via HTML: {}", decoded);
//            return "POTENTIAL_XSS";
//        }
        String s = Encode.forHtml(input);
        String s1 = Encode.forCDATA(input);
        String s2 = Encode.forCssString(input);
        String s3 = Encode.forJavaScript(input);
        String s4 = Encode.forJava(input);
        
//        Encode.for
        return "SAFE";
    }

    /**
     * 清洗输入：如果安全，返回编码后输出；否则拒绝
     * @param input 输入
     * @return 安全输出或null（如果攻击）
     */
    public String sanitizeInput(String input) {
        String type = detectAttackType(input);
        if (!"SAFE".equals(type)) {
            // 拒绝消息
            return ""; 
        }
        // 输出编码（HTML上下文，适合聊天显示）
        return Encode.forHtml(input);
    }
}