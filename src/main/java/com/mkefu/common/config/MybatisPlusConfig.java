package com.mkefu.common.config;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import com.mkefu.common.interceptor.SqlLogInterceptor;

/**
 * MybatisPlus配置
 * <AUTHOR>
 * @since 2025-04-10
 */
@Configuration
public class MybatisPlusConfig {

    /**
     * MybatisPlus拦截器
     * @return MybatisPlus拦截器
     */
    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        interceptor.addInnerInterceptor(new PaginationInnerInterceptor(DbType.MYSQL));
        interceptor.addInnerInterceptor(new SqlLogInterceptor());
        return interceptor;
    }
}
